#!/usr/bin/env python3
"""
数据集整合脚本
将两个RGBD数据集整合成统一格式，支持YOLO训练
"""

import os
import json
import shutil
from pathlib import Path
import cv2
import numpy as np
from typing import Dict, List, Tuple

class RGBDDatasetIntegrator:
    def __init__(self, output_dir: str = "unified_rgbd_dataset"):
        self.output_dir = Path(output_dir)
        self.setup_output_structure()
        
        # 类别映射 - 根据比赛说明
        self.category_mapping = {
            'CA001': 0,  # 勺子
            'CA002': 1,  # 筷子  
            'CA003': 2,  # 碗
            'CA004': 3,  # 衣架
            'CB001': 4,  # 沙琪玛
            'CB002': 5,  # 罐装蜜饯食品
            'CB003': 6,  # 火腿肠
            'CB004': 7,  # 薯片
            'CC001': 8,  # 罐装饮料
            'CC002': 9,  # 瓶装饮料
            'CC003': 10, # 盒装牛奶
            'CC004': 11, # 瓶装饮用水
            'CD001': 12, # 苹果
            'CD002': 13, # 橙子
            'CD003': 14, # 香蕉
            'CD004': 15, # 芒果
            'Wxxx': 16   # 未知物品
        }
        
    def setup_output_structure(self):
        """创建输出目录结构"""
        dirs = [
            self.output_dir / "images" / "train",
            self.output_dir / "images" / "val", 
            self.output_dir / "images" / "test",
            self.output_dir / "depth" / "train",
            self.output_dir / "depth" / "val",
            self.output_dir / "depth" / "test",
            self.output_dir / "labels" / "train",
            self.output_dir / "labels" / "val",
            self.output_dir / "labels" / "test"
        ]
        
        for dir_path in dirs:
            dir_path.mkdir(parents=True, exist_ok=True)
            
    def load_coco_annotations(self, coco_file: str) -> Dict:
        """加载COCO格式标注文件"""
        with open(coco_file, 'r', encoding='utf-8') as f:
            return json.load(f)
            
    def coco_to_yolo_bbox(self, bbox: List[float], img_width: int, img_height: int) -> List[float]:
        """将COCO格式的bbox转换为YOLO格式"""
        x, y, w, h = bbox
        
        # COCO: [x_min, y_min, width, height]
        # YOLO: [x_center, y_center, width, height] (normalized)
        x_center = (x + w / 2) / img_width
        y_center = (y + h / 2) / img_height
        width = w / img_width
        height = h / img_height
        
        return [x_center, y_center, width, height]
        
    def process_dataset(self, dataset_path: str, dataset_name: str, split_ratio: Tuple[float, float, float] = (0.7, 0.2, 0.1)):
        """处理单个数据集"""
        dataset_path = Path(dataset_path)
        
        # 查找COCO标注文件
        coco_file = dataset_path / "_annotations.coco.json"
        if not coco_file.exists():
            print(f"警告: 在 {dataset_path} 中未找到COCO标注文件")
            return
            
        print(f"处理数据集: {dataset_name}")
        coco_data = self.load_coco_annotations(coco_file)
        
        # 创建图像ID到文件名的映射
        image_id_to_filename = {img['id']: img['file_name'] for img in coco_data['images']}
        image_id_to_size = {img['id']: (img['width'], img['height']) for img in coco_data['images']}
        
        # 按图像组织标注
        annotations_by_image = {}
        for ann in coco_data['annotations']:
            image_id = ann['image_id']
            if image_id not in annotations_by_image:
                annotations_by_image[image_id] = []
            annotations_by_image[image_id].append(ann)
            
        # 获取所有图像文件
        image_files = []
        for img_info in coco_data['images']:
            filename = img_info['file_name']
            # 查找RGB图像
            rgb_paths = list(dataset_path.rglob(filename))
            if rgb_paths:
                image_files.append((img_info['id'], rgb_paths[0]))
                
        # 数据集分割
        total_images = len(image_files)
        train_count = int(total_images * split_ratio[0])
        val_count = int(total_images * split_ratio[1])
        
        splits = {
            'train': image_files[:train_count],
            'val': image_files[train_count:train_count + val_count],
            'test': image_files[train_count + val_count:]
        }
        
        for split_name, split_files in splits.items():
            print(f"  处理 {split_name} 集: {len(split_files)} 张图像")
            
            for image_id, rgb_path in split_files:
                filename = image_id_to_filename[image_id]
                base_name = Path(filename).stem
                
                # 复制RGB图像
                new_rgb_name = f"{dataset_name}_{base_name}.jpg"
                rgb_dst = self.output_dir / "images" / split_name / new_rgb_name
                shutil.copy2(rgb_path, rgb_dst)
                
                # 查找并复制深度图像
                depth_name = f"{base_name}_depth.png"
                depth_paths = list(dataset_path.rglob(depth_name))
                if depth_paths:
                    new_depth_name = f"{dataset_name}_{base_name}_depth.png"
                    depth_dst = self.output_dir / "depth" / split_name / new_depth_name
                    shutil.copy2(depth_paths[0], depth_dst)
                else:
                    print(f"    警告: 未找到深度图像 {depth_name}")
                    
                # 生成YOLO格式标注
                if image_id in annotations_by_image:
                    img_width, img_height = image_id_to_size[image_id]
                    yolo_annotations = []
                    
                    for ann in annotations_by_image[image_id]:
                        category_id = ann['category_id']
                        # 查找类别名称
                        category_name = None
                        for cat in coco_data['categories']:
                            if cat['id'] == category_id:
                                category_name = cat['name']
                                break
                                
                        if category_name and category_name in self.category_mapping:
                            yolo_class_id = self.category_mapping[category_name]
                            bbox = self.coco_to_yolo_bbox(ann['bbox'], img_width, img_height)
                            yolo_annotations.append(f"{yolo_class_id} {' '.join(map(str, bbox))}")
                            
                    # 保存YOLO标注文件
                    if yolo_annotations:
                        label_file = self.output_dir / "labels" / split_name / f"{dataset_name}_{base_name}.txt"
                        with open(label_file, 'w') as f:
                            f.write('\n'.join(yolo_annotations))
                            
    def create_dataset_yaml(self):
        """创建YOLO数据集配置文件"""
        yaml_content = f"""# RGBD Dataset Configuration
path: {self.output_dir.absolute()}
train: images/train
val: images/val
test: images/test

# Classes
nc: 17  # number of classes
names: ['CA001_勺子', 'CA002_筷子', 'CA003_碗', 'CA004_衣架', 
        'CB001_沙琪玛', 'CB002_罐装蜜饯', 'CB003_火腿肠', 'CB004_薯片',
        'CC001_罐装饮料', 'CC002_瓶装饮料', 'CC003_盒装牛奶', 'CC004_瓶装水',
        'CD001_苹果', 'CD002_橙子', 'CD003_香蕉', 'CD004_芒果', 'Wxxx_未知物品']

# Additional paths for depth images
depth_train: depth/train
depth_val: depth/val  
depth_test: depth/test
"""
        
        yaml_file = self.output_dir / "dataset.yaml"
        with open(yaml_file, 'w', encoding='utf-8') as f:
            f.write(yaml_content)
            
        print(f"数据集配置文件已保存: {yaml_file}")
        
    def generate_statistics(self):
        """生成数据集统计信息"""
        stats = {}
        
        for split in ['train', 'val', 'test']:
            rgb_dir = self.output_dir / "images" / split
            depth_dir = self.output_dir / "depth" / split
            label_dir = self.output_dir / "labels" / split
            
            rgb_count = len(list(rgb_dir.glob("*.jpg")))
            depth_count = len(list(depth_dir.glob("*.png")))
            label_count = len(list(label_dir.glob("*.txt")))
            
            stats[split] = {
                'rgb_images': rgb_count,
                'depth_images': depth_count,
                'labels': label_count
            }
            
        # 保存统计信息
        stats_file = self.output_dir / "dataset_stats.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)
            
        print("\n数据集统计:")
        for split, counts in stats.items():
            print(f"  {split}: RGB={counts['rgb_images']}, Depth={counts['depth_images']}, Labels={counts['labels']}")
            
        return stats

def main():
    integrator = RGBDDatasetIntegrator("unified_rgbd_dataset")
    
    # 处理两个数据集
    dataset_paths = [
        ("3D/dataset", "dataset1"),
        ("3D/dataset-20250723-coco", "dataset2")
    ]
    
    for dataset_path, dataset_name in dataset_paths:
        if Path(dataset_path).exists():
            integrator.process_dataset(dataset_path, dataset_name)
        else:
            print(f"警告: 数据集路径不存在: {dataset_path}")
            
    # 创建配置文件和统计信息
    integrator.create_dataset_yaml()
    integrator.generate_statistics()
    
    print(f"\n数据集整合完成! 输出目录: {integrator.output_dir}")

if __name__ == "__main__":
    main()
