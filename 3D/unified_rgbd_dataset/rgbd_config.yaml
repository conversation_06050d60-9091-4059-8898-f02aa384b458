
# RGB-D YOLOv8 Training Configuration
path: /home/<USER>/homes/ch/claude/SpatialVLA/3D/unified_rgbd_dataset
train: images/train
val: images/val
test: images/test

# Classes (from original dataset.yaml)
nc: 17
names: ['CA001_勺子', 'CA002_筷子', 'CA003_碗', 'CA004_衣架', 
        'CB001_沙琪玛', 'CB002_罐装蜜饯', 'CB003_火腿肠', 'CB004_薯片',
        'CC001_罐装饮料', 'CC002_瓶装饮料', 'CC003_盒装牛奶', 'CC004_瓶装水',
        'CD001_苹果', 'CD002_橙子', 'CD003_香蕉', 'CD004_芒果', 'Wxxx_未知物品']

# RGB-D specific paths
depth_train: depth/train
depth_val: depth/val
depth_test: depth/test

# Training parameters
epochs: 100
batch_size: 16
imgsz: 640
device: 0  # GPU device

# Augmentation parameters
hsv_h: 0.015
hsv_s: 0.7
hsv_v: 0.4
degrees: 0.0
translate: 0.1
scale: 0.5
shear: 0.0
perspective: 0.0
flipud: 0.0
fliplr: 0.5
mosaic: 1.0
mixup: 0.0
