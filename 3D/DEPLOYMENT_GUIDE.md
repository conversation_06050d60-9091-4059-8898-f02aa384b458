# 2025年比赛RGB-D YOLO系统部署指南

## 📦 包内容说明

您收到的压缩包包含以下内容：

```
competition_2025_rgbd_yolo_system/
├── README.md                    # 主要说明文档
├── DEPLOYMENT_GUIDE.md          # 本部署指南
├── requirements.txt             # Python依赖包
├── setup.py                    # 安装脚本
├── run_complete_pipeline.py    # 一键运行脚本
├── package_info.json          # 包信息
│
├── data_preparation/           # 数据准备模块
├── training/                   # 训练模块
├── inference/                  # 推理模块
├── evaluation/                 # 评估模块
├── utils/                      # 工具模块
├── models/                     # 预训练模型（如果包含）
├── datasets/                   # 示例数据
├── docs/                       # 文档
└── examples/                   # 示例代码
```

## 🚀 快速部署（5分钟上手）

### 步骤1: 解压和环境准备
```bash
# 解压文件
unzip competition_2025_rgbd_yolo_system_v1.2.0.zip
cd competition_2025_rgbd_yolo_system

# 创建虚拟环境（推荐）
conda create -n competition2025 python=3.8
conda activate competition2025

# 或使用venv
python -m venv competition2025
source competition2025/bin/activate  # Linux/Mac
# competition2025\Scripts\activate  # Windows
```

### 步骤2: 安装依赖
```bash
# 安装所有依赖
pip install -r requirements.txt

# 或使用setup.py安装
python setup.py install
```

### 步骤3: 验证安装
```bash
# 运行简单测试
python examples/simple_inference.py

# 或运行完整流程测试
python run_complete_pipeline.py --mode evaluation
```

## 🎯 针对不同场景的部署

### 场景1: 仅需要推理功能
如果您只需要使用训练好的模型进行推理：

```bash
# 运行推理系统
python inference/ultimate_competition_system.py

# 或使用自适应推理
python inference/adaptive_inference_system.py
```

### 场景2: 需要重新训练模型
如果您有自己的数据集需要重新训练：

```bash
# 1. 准备数据集
python data_preparation/competition_2025_dataset_preparation.py

# 2. 开始训练
python training/refined_training_system.py

# 3. 评估模型
python evaluation/comprehensive_model_evaluation.py
```

### 场景3: 完整开发环境
如果您需要完整的开发和实验环境：

```bash
# 运行完整流程
python run_complete_pipeline.py --mode full
```

## 🔧 硬件要求和优化

### 最低要求
- **CPU**: Intel i5 或 AMD Ryzen 5 以上
- **内存**: 8GB RAM
- **存储**: 10GB 可用空间
- **Python**: 3.8 或更高版本

### 推荐配置
- **CPU**: Intel i7 或 AMD Ryzen 7 以上
- **GPU**: NVIDIA GTX 1660 或更高（支持CUDA）
- **内存**: 16GB RAM
- **存储**: 50GB 可用空间（包含数据集）

### 比赛目标硬件
- **平台**: 香橙派 AI pro 8T
- **内存**: 16GB
- **相机**: 奥比中光 Astra Pro Plus RGBD

### GPU加速配置
```bash
# 检查CUDA是否可用
python -c "import torch; print(torch.cuda.is_available())"

# 如果需要安装CUDA版本的PyTorch
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
```

## 📊 模型选择指南

根据您的需求选择合适的模型：

### 1. 最高精度模型 🏆
- **文件**: `models/refined_models/refined_yolo11n_stage1_balanced/weights/best.pt`
- **性能**: mAP50=95.54%, mAP50-95=68.18%
- **适用**: 追求最高检测精度
- **推理时间**: ~10ms

### 2. 困难类别专用模型 🎯
- **文件**: `models/optimized_models_s/class_weighted_yolo11s_thin_small/weights/best.pt`
- **性能**: 牙刷43.47%, 果冻37.64%
- **适用**: 特别关注牙刷和果冻检测
- **推理时间**: ~15ms

### 3. 平衡性能模型 ⚖️
- **文件**: `models/optimized_models/class_weighted_yolo11n_thin_small/weights/best.pt`
- **性能**: mAP50=94.47%, 困难类别37.76%
- **适用**: 平衡精度和速度
- **推理时间**: ~8ms

## 🔍 故障排除

### 常见问题及解决方案

#### 1. 导入错误
```bash
# 错误: ModuleNotFoundError: No module named 'ultralytics'
# 解决: 重新安装依赖
pip install ultralytics

# 错误: No module named 'torch'
# 解决: 安装PyTorch
pip install torch torchvision
```

#### 2. CUDA相关错误
```bash
# 错误: CUDA out of memory
# 解决: 减少批次大小或使用CPU
export CUDA_VISIBLE_DEVICES=""  # 强制使用CPU

# 或在代码中设置
device = "cpu"
```

#### 3. 模型文件缺失
```bash
# 错误: 模型文件不存在
# 解决: 检查模型路径或重新下载完整包
ls models/*/weights/best.pt
```

#### 4. 权限问题
```bash
# 错误: Permission denied
# 解决: 检查文件权限
chmod +x run_complete_pipeline.py
```

### 性能优化建议

#### 1. 内存优化
```python
# 在训练脚本中添加
import torch
torch.backends.cudnn.benchmark = True
torch.backends.cudnn.deterministic = False
```

#### 2. 推理加速
```python
# 使用半精度推理
model.half()  # 转换为FP16

# 或使用TensorRT（如果支持）
model.export(format='engine')
```

#### 3. 数据加载优化
```python
# 增加数据加载进程数
num_workers = 4  # 根据CPU核心数调整
```

## 📱 在目标硬件上的部署

### 香橙派 AI pro 8T 部署

#### 1. 环境准备
```bash
# 更新系统
sudo apt update && sudo apt upgrade

# 安装Python和pip
sudo apt install python3 python3-pip

# 安装系统依赖
sudo apt install libopencv-dev python3-opencv
```

#### 2. 轻量化部署
```bash
# 只安装必要的包
pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu
pip install ultralytics opencv-python numpy

# 使用轻量化模型
python inference/ultimate_competition_system.py --model lightweight
```

#### 3. 性能监控
```bash
# 监控系统资源
htop
nvidia-smi  # 如果有GPU

# 监控推理性能
python -c "
import time
from inference.ultimate_competition_system import UltimateCompetitionSystem
system = UltimateCompetitionSystem()
system.initialize_system()
# 测试推理时间
"
```

## 🔄 持续集成和更新

### 版本管理
```bash
# 检查当前版本
python -c "import json; print(json.load(open('package_info.json'))['version'])"

# 更新到新版本
git pull origin main  # 如果使用git
pip install -r requirements.txt --upgrade
```

### 自动化测试
```bash
# 运行测试套件
python -m pytest tests/  # 如果有测试文件

# 性能基准测试
python evaluation/comprehensive_model_evaluation.py
```

## 📞 技术支持

### 获取帮助
1. **查看文档**: 首先查看 `README.md` 和 `docs/` 目录
2. **运行示例**: 尝试 `examples/` 目录中的示例代码
3. **检查日志**: 查看训练和推理过程中的日志输出
4. **社区支持**: 在项目仓库提交Issue

### 联系信息
- **项目仓库**: [GitHub链接]
- **技术文档**: [文档链接]
- **邮件支持**: [邮箱地址]

## 🎉 成功部署检查清单

完成以下检查确保系统正常运行：

- [ ] Python环境正确安装（3.8+）
- [ ] 所有依赖包安装成功
- [ ] 模型文件存在且可加载
- [ ] 示例推理运行成功
- [ ] 性能满足要求（推理时间<5s）
- [ ] 输出格式正确（ID;Num）

## 🏆 预期性能指标

成功部署后，您应该能够达到以下性能：

- **检测精度**: mAP50 > 94%
- **推理速度**: < 50ms/图像
- **模型加载**: < 1秒
- **内存使用**: < 4GB
- **困难类别**: 牙刷>40%, 果冻>35%

---

**🎊 恭喜！您已成功部署2025年比赛RGB-D YOLO检测系统！**

如有任何问题，请参考文档或联系技术支持。祝您在比赛中取得优异成绩！
