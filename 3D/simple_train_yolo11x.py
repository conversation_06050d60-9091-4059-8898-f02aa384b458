#!/usr/bin/env python3
"""
简化的YOLO11x训练脚本
"""

import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append('/home/<USER>/claude/SpatialVLA/3D')

try:
    from ultralytics import YOL<PERSON>
    print("✓ Ultralytics导入成功")
except ImportError as e:
    print(f"✗ Ultralytics导入失败: {e}")
    sys.exit(1)

def main():
    print("=== 简化YOLO11x训练 ===")
    
    # 检查数据集配置文件
    dataset_config = "/home/<USER>/claude/SpatialVLA/3D/yolo11x/dataset/dataset.yaml"
    if not Path(dataset_config).exists():
        print(f"✗ 数据集配置文件不存在: {dataset_config}")
        return
    
    print(f"✓ 数据集配置文件: {dataset_config}")
    
    # 创建输出目录
    output_dir = Path("/home/<USER>/claude/SpatialVLA/3D/yolo11x")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        # 初始化YOLO11x模型
        print("加载YOLO11x模型...")
        model = YOLO('yolo11x.pt')
        print("✓ 模型加载成功")
        
        # 开始训练（使用较小的参数进行测试）
        print("开始训练...")
        results = model.train(
            data=dataset_config,
            epochs=5,  # 先用少量epoch测试
            batch=4,   # 小batch size
            imgsz=640,
            device='0',
            project=str(output_dir),
            name='yolo11x_test',
            exist_ok=True,
            verbose=True
        )
        
        print("✓ 训练完成")
        print(f"结果保存在: {output_dir}/yolo11x_test")
        
    except Exception as e:
        print(f"✗ 训练失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
