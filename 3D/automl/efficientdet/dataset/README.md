This folder provides tools for converting raw coco/pascal data to tfrecord.

### 1. Convert COCO validation set to tfrecord:

    # Download coco data.
    !wget http://images.cocodataset.org/zips/val2017.zip
    !wget http://images.cocodataset.org/annotations/annotations_trainval2017.zip
    !unzip val2017.zip
    !unzip annotations_trainval2017.zip

    # convert coco data to tfrecord.
    !mkdir tfrecord
    !PYTHONPATH=".:$PYTHONPATH"  python dataset/create_coco_tfrecord.py \
      --image_dir=val2017 \
      --object_annotations_file=annotations/instances_val2017.json \
      --output_file_prefix=tfrecord/val \
      --num_shards=32

### 2. Convert Pascal VOC 2012 to tfrecord:

    # Download and convert pascal data.
    !wget http://host.robots.ox.ac.uk/pascal/VOC/voc2012/VOCtrainval_11-May-2012.tar
    !tar xf VOCtrainval_11-May-2012.tar
    !mkdir tfrecord
    !PYTHONPATH=".:$PYTHONPATH"  python dataset/create_pascal_tfrecord.py  \
        --data_dir=VOCdevkit --year=VOC2012  --output_path=tfrecord/pascal

Attention:  soure_id (or image_id) needs to be an integer due to the official COCO library requreiments. 
