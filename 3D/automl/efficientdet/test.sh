#!/usr/bin/env bash
# Copyright 2020 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================
for file in `find $PWD/efficientdet -name '*.py'`
do
  pylint --rcfile=.pylintrc $file
done

cd efficientdet
for file in `find $PWD -name '*_test.py'`
do
  PYTHONPATH=$PWD TF_CPP_MIN_LOG_LEVEL=1 python $file
done