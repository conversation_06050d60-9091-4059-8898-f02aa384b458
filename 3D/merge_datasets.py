#!/usr/bin/env python3
"""
合并reorganized_dataset和competition_2025_dataset
1. 清理reorganized_dataset中没有深度图像的RGB文件
2. 合并两个数据集到一个统一的数据集中
3. 确保所有RGB图像都有对应的深度图像
"""

import os
import shutil
import json
import yaml
from pathlib import Path
from collections import defaultdict

def analyze_datasets():
    """分析两个数据集的基本信息"""
    print("=== 分析数据集 ===")
    
    # 分析reorganized_dataset
    reorg_path = Path("/home/<USER>/claude/SpatialVLA/3D/reorganized_dataset")
    reorg_rgb = list((reorg_path / "images" / "train").glob("*.jpg"))
    reorg_depth = list((reorg_path / "depth" / "train").glob("*.png"))
    
    print(f"Reorganized Dataset:")
    print(f"  RGB: {len(reorg_rgb)} 文件")
    print(f"  Depth: {len(reorg_depth)} 文件")
    
    # 分析competition_2025_dataset
    comp_path = Path("/home/<USER>/claude/SpatialVLA/3D/competition_2025_dataset")
    comp_rgb_train = list((comp_path / "images" / "train").glob("*.jpg"))
    comp_rgb_val = list((comp_path / "images" / "val").glob("*.jpg"))
    comp_rgb_test = list((comp_path / "images" / "test").glob("*.jpg"))
    comp_rgb_total = len(comp_rgb_train) + len(comp_rgb_val) + len(comp_rgb_test)
    
    comp_depth_train = list((comp_path / "depth" / "train").glob("*.png"))
    comp_depth_val = list((comp_path / "depth" / "val").glob("*.png"))
    comp_depth_test = list((comp_path / "depth" / "test").glob("*.png"))
    comp_depth_total = len(comp_depth_train) + len(comp_depth_val) + len(comp_depth_test)
    
    print(f"Competition Dataset:")
    print(f"  RGB: {comp_rgb_total} 文件 (train:{len(comp_rgb_train)}, val:{len(comp_rgb_val)}, test:{len(comp_rgb_test)})")
    print(f"  Depth: {comp_depth_total} 文件 (train:{len(comp_depth_train)}, val:{len(comp_depth_val)}, test:{len(comp_depth_test)})")
    
    return {
        'reorg': {'rgb': reorg_rgb, 'depth': reorg_depth},
        'comp': {
            'rgb_train': comp_rgb_train, 'rgb_val': comp_rgb_val, 'rgb_test': comp_rgb_test,
            'depth_train': comp_depth_train, 'depth_val': comp_depth_val, 'depth_test': comp_depth_test
        }
    }

def find_paired_files(rgb_files, depth_files):
    """找到RGB和深度图像的配对文件"""
    print("\n=== 查找配对文件 ===")
    
    # 创建深度文件名映射
    depth_names = {}
    for depth_file in depth_files:
        name = depth_file.stem
        if name.endswith('_depth'):
            base_name = name[:-6]  # 去掉_depth
            depth_names[base_name] = depth_file
    
    # 找到配对的RGB文件
    paired_rgb = []
    unpaired_rgb = []
    
    for rgb_file in rgb_files:
        base_name = rgb_file.stem  # 去掉.jpg
        if base_name in depth_names:
            paired_rgb.append(rgb_file)
        else:
            unpaired_rgb.append(rgb_file)
    
    print(f"配对的RGB文件: {len(paired_rgb)}")
    print(f"未配对的RGB文件: {len(unpaired_rgb)}")
    
    if unpaired_rgb:
        print("未配对文件示例:", [f.name for f in unpaired_rgb[:5]])
    
    return paired_rgb, unpaired_rgb

def clean_reorganized_annotations(annotation_file, paired_rgb_files):
    """清理reorganized数据集的标注文件，只保留有配对深度图像的条目"""
    print("\n=== 清理标注文件 ===")
    
    # 获取配对文件的文件名集合
    paired_filenames = {f.name for f in paired_rgb_files}
    
    with open(annotation_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 过滤images
    original_images = len(data['images'])
    filtered_images = []
    valid_image_ids = set()
    
    for img in data['images']:
        if img['file_name'] in paired_filenames:
            filtered_images.append(img)
            valid_image_ids.add(img['id'])
    
    data['images'] = filtered_images
    
    # 过滤annotations
    original_annotations = len(data['annotations'])
    filtered_annotations = []
    
    for ann in data['annotations']:
        if ann['image_id'] in valid_image_ids:
            filtered_annotations.append(ann)
    
    data['annotations'] = filtered_annotations
    
    print(f"图像条目: {original_images} -> {len(filtered_images)}")
    print(f"标注条目: {original_annotations} -> {len(filtered_annotations)}")
    
    # 保存清理后的标注文件
    cleaned_file = annotation_file.parent / "cleaned_annotations.json"
    with open(cleaned_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    
    print(f"清理后的标注文件保存到: {cleaned_file}")
    return cleaned_file, data

def create_merged_dataset():
    """创建合并后的数据集"""
    print("\n=== 创建合并数据集 ===")
    
    # 创建目标目录
    merged_path = Path("/home/<USER>/claude/SpatialVLA/3D/merged_rgbd_dataset")
    if merged_path.exists():
        response = input(f"目标目录 {merged_path} 已存在，是否删除并重新创建？(y/n): ")
        if response.lower() == 'y':
            shutil.rmtree(merged_path)
        else:
            print("操作取消")
            return None
    
    # 创建目录结构
    directories = [
        'images/train', 'images/val', 'images/test',
        'depth/train', 'depth/val', 'depth/test',
        'labels/train', 'labels/val', 'labels/test'
    ]
    
    for dir_path in directories:
        (merged_path / dir_path).mkdir(parents=True, exist_ok=True)
    
    print(f"创建合并数据集目录: {merged_path}")
    return merged_path

def copy_competition_data(merged_path):
    """复制competition数据集到合并目录"""
    print("\n=== 复制Competition数据 ===")
    
    comp_path = Path("/home/<USER>/claude/SpatialVLA/3D/competition_2025_dataset")
    
    # 复制文件的函数
    def copy_files(src_pattern, dst_dir, prefix="comp_"):
        files = list(src_pattern.parent.glob(src_pattern.name))
        for file_path in files:
            new_name = f"{prefix}{file_path.name}"
            dst_path = dst_dir / new_name
            shutil.copy2(file_path, dst_path)
        return len(files)
    
    # 复制RGB图像
    rgb_train_count = copy_files(comp_path / "images/train/*.jpg", merged_path / "images/train")
    rgb_val_count = copy_files(comp_path / "images/val/*.jpg", merged_path / "images/val")
    rgb_test_count = copy_files(comp_path / "images/test/*.jpg", merged_path / "images/test")
    
    # 复制深度图像
    depth_train_count = copy_files(comp_path / "depth/train/*.png", merged_path / "depth/train")
    depth_val_count = copy_files(comp_path / "depth/val/*.png", merged_path / "depth/val")
    depth_test_count = copy_files(comp_path / "depth/test/*.png", merged_path / "depth/test")
    
    print(f"复制RGB图像: train={rgb_train_count}, val={rgb_val_count}, test={rgb_test_count}")
    print(f"复制深度图像: train={depth_train_count}, val={depth_val_count}, test={depth_test_count}")
    
    return {
        'rgb': rgb_train_count + rgb_val_count + rgb_test_count,
        'depth': depth_train_count + depth_val_count + depth_test_count
    }

def copy_reorganized_data(merged_path, paired_rgb_files):
    """复制reorganized数据集的配对文件到合并目录"""
    print("\n=== 复制Reorganized配对数据 ===")
    
    reorg_path = Path("/home/<USER>/claude/SpatialVLA/3D/reorganized_dataset")
    
    # 复制配对的RGB文件到train目录
    rgb_count = 0
    for rgb_file in paired_rgb_files:
        new_name = f"reorg_{rgb_file.name}"
        dst_path = merged_path / "images/train" / new_name
        shutil.copy2(rgb_file, dst_path)
        rgb_count += 1
    
    # 复制对应的深度文件
    depth_count = 0
    for rgb_file in paired_rgb_files:
        base_name = rgb_file.stem
        depth_file = reorg_path / "depth/train" / f"{base_name}_depth.png"
        if depth_file.exists():
            new_name = f"reorg_{depth_file.name}"
            dst_path = merged_path / "depth/train" / new_name
            shutil.copy2(depth_file, dst_path)
            depth_count += 1
    
    print(f"复制Reorganized数据: RGB={rgb_count}, Depth={depth_count}")
    return {'rgb': rgb_count, 'depth': depth_count}

def main():
    """主函数"""
    print("开始合并数据集...")
    
    # 1. 分析数据集
    datasets_info = analyze_datasets()
    
    # 2. 找到reorganized数据集中的配对文件
    paired_rgb, unpaired_rgb = find_paired_files(
        datasets_info['reorg']['rgb'], 
        datasets_info['reorg']['depth']
    )
    
    # 3. 清理reorganized数据集的标注文件
    reorg_annotation_file = Path("/home/<USER>/claude/SpatialVLA/3D/reorganized_dataset/labels/train/annotations.json")
    if reorg_annotation_file.exists():
        cleaned_annotation_file, cleaned_data = clean_reorganized_annotations(reorg_annotation_file, paired_rgb)
    
    # 4. 创建合并数据集目录
    merged_path = create_merged_dataset()
    if not merged_path:
        return
    
    # 5. 复制competition数据
    comp_counts = copy_competition_data(merged_path)
    
    # 6. 复制reorganized配对数据
    reorg_counts = copy_reorganized_data(merged_path, paired_rgb)
    
    # 7. 统计总数
    total_rgb = comp_counts['rgb'] + reorg_counts['rgb']
    total_depth = comp_counts['depth'] + reorg_counts['depth']
    
    print(f"\n=== 合并完成 ===")
    print(f"总RGB图像: {total_rgb}")
    print(f"总深度图像: {total_depth}")
    print(f"合并数据集路径: {merged_path}")
    
    # 8. 创建配置文件
    create_merged_config(merged_path, total_rgb, total_depth)

def create_merged_config(merged_path, total_rgb, total_depth):
    """创建合并数据集的配置文件"""
    config = {
        'path': str(merged_path),
        'train': 'images/train',
        'val': 'images/val',
        'test': 'images/test',
        'depth_train': 'depth/train',
        'depth_val': 'depth/val', 
        'depth_test': 'depth/test',
        'nc': 12,  # 假设类别数量
        'names': {i: f'class_{i}' for i in range(12)},
        'dataset_info': {
            'total_rgb_images': total_rgb,
            'total_depth_images': total_depth,
            'rgb_depth_paired': True,
            'sources': ['competition_2025_dataset', 'reorganized_dataset'],
            'created': '2025-01-12',
            'version': '1.0'
        }
    }
    
    config_file = merged_path / 'merged_dataset.yaml'
    with open(config_file, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    print(f"配置文件创建: {config_file}")

if __name__ == "__main__":
    main()
