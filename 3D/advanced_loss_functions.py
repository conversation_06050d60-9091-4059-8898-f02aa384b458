#!/usr/bin/env python3
"""
先进的IoU损失函数实现
基于2024-2025年最新研究，专门用于提升mAP50-95
"""

import torch
import torch.nn as nn
import math
import numpy as np

class WIoULoss(nn.Module):
    """
    Wise-IoU Loss (WIoU)
    论文: Wise-IoU: Bounding Box Regression Loss with Dynamic Focusing Mechanism
    特点: 动态聚焦机制，自适应调整困难样本权重
    """
    def __init__(self, monotonous=False, eps=1e-7):
        super().__init__()
        self.monotonous = monotonous
        self.eps = eps
        
    def forward(self, pred, target, anchor_points=None):
        """
        Args:
            pred: 预测边界框 [N, 4] (x1, y1, x2, y2)
            target: 真实边界框 [N, 4] (x1, y1, x2, y2)
        """
        # 计算IoU
        iou = self.bbox_iou(pred, target)
        
        # 计算中心点距离
        pred_center = (pred[:, :2] + pred[:, 2:]) / 2
        target_center = (target[:, :2] + target[:, 2:]) / 2
        center_distance = torch.norm(pred_center - target_center, dim=1)
        
        # 计算外接矩形对角线距离
        pred_wh = pred[:, 2:] - pred[:, :2]
        target_wh = target[:, 2:] - target[:, :2]
        
        # Wise-IoU的动态聚焦机制
        beta = self.compute_beta(iou)
        
        # 计算WIoU损失
        wiou = 1 - iou + beta * center_distance / (torch.norm(pred_wh + target_wh, dim=1) + self.eps)
        
        return wiou.mean()
        
    def compute_beta(self, iou):
        """计算动态聚焦系数"""
        alpha = 1.9  # 超参数
        beta = (iou.detach() / (1 - iou.detach() + self.eps)) ** alpha
        return beta
        
    def bbox_iou(self, box1, box2):
        """计算IoU"""
        # 计算交集
        inter_x1 = torch.max(box1[:, 0], box2[:, 0])
        inter_y1 = torch.max(box1[:, 1], box2[:, 1])
        inter_x2 = torch.min(box1[:, 2], box2[:, 2])
        inter_y2 = torch.min(box1[:, 3], box2[:, 3])
        
        inter_area = torch.clamp(inter_x2 - inter_x1, min=0) * torch.clamp(inter_y2 - inter_y1, min=0)
        
        # 计算并集
        box1_area = (box1[:, 2] - box1[:, 0]) * (box1[:, 3] - box1[:, 1])
        box2_area = (box2[:, 2] - box2[:, 0]) * (box2[:, 3] - box2[:, 1])
        union_area = box1_area + box2_area - inter_area
        
        iou = inter_area / (union_area + self.eps)
        return iou

class SIoULoss(nn.Module):
    """
    SIoU Loss (Scylla-IoU)
    特点: 考虑角度成本、距离成本、形状成本和IoU成本
    """
    def __init__(self, eps=1e-7):
        super().__init__()
        self.eps = eps
        
    def forward(self, pred, target):
        # 计算基础IoU
        iou = self.bbox_iou(pred, target)
        
        # 计算角度成本
        angle_cost = self.compute_angle_cost(pred, target)
        
        # 计算距离成本
        distance_cost = self.compute_distance_cost(pred, target)
        
        # 计算形状成本
        shape_cost = self.compute_shape_cost(pred, target)
        
        # 组合所有成本
        siou = 1 - iou + angle_cost + distance_cost + shape_cost
        
        return siou.mean()
        
    def compute_angle_cost(self, pred, target):
        """计算角度成本"""
        pred_center = (pred[:, :2] + pred[:, 2:]) / 2
        target_center = (target[:, :2] + target[:, 2:]) / 2
        
        # 计算中心点连线与x轴的夹角
        delta = pred_center - target_center
        angle = torch.atan2(delta[:, 1], delta[:, 0])
        
        # 角度成本
        angle_cost = 1 - 2 * torch.sin(torch.abs(angle) + math.pi/4) ** 2
        return angle_cost
        
    def compute_distance_cost(self, pred, target):
        """计算距离成本"""
        pred_center = (pred[:, :2] + pred[:, 2:]) / 2
        target_center = (target[:, :2] + target[:, 2:]) / 2
        
        # 中心点距离
        center_distance = torch.norm(pred_center - target_center, dim=1)
        
        # 外接矩形对角线距离
        c_x = torch.max(pred[:, 2], target[:, 2]) - torch.min(pred[:, 0], target[:, 0])
        c_y = torch.max(pred[:, 3], target[:, 3]) - torch.min(pred[:, 1], target[:, 1])
        c = torch.sqrt(c_x**2 + c_y**2 + self.eps)
        
        distance_cost = center_distance / c
        return distance_cost
        
    def compute_shape_cost(self, pred, target):
        """计算形状成本"""
        pred_wh = pred[:, 2:] - pred[:, :2]
        target_wh = target[:, 2:] - target[:, :2]
        
        # 宽高比差异
        w_cost = torch.abs(pred_wh[:, 0] - target_wh[:, 0]) / torch.max(pred_wh[:, 0], target_wh[:, 0])
        h_cost = torch.abs(pred_wh[:, 1] - target_wh[:, 1]) / torch.max(pred_wh[:, 1], target_wh[:, 1])
        
        shape_cost = (w_cost + h_cost) / 4
        return shape_cost
        
    def bbox_iou(self, box1, box2):
        """计算IoU"""
        inter_x1 = torch.max(box1[:, 0], box2[:, 0])
        inter_y1 = torch.max(box1[:, 1], box2[:, 1])
        inter_x2 = torch.min(box1[:, 2], box2[:, 2])
        inter_y2 = torch.min(box1[:, 3], box2[:, 3])
        
        inter_area = torch.clamp(inter_x2 - inter_x1, min=0) * torch.clamp(inter_y2 - inter_y1, min=0)
        
        box1_area = (box1[:, 2] - box1[:, 0]) * (box1[:, 3] - box1[:, 1])
        box2_area = (box2[:, 2] - box2[:, 0]) * (box2[:, 3] - box2[:, 1])
        union_area = box1_area + box2_area - inter_area
        
        iou = inter_area / (union_area + self.eps)
        return iou

class MPDIoULoss(nn.Module):
    """
    MPDIoU Loss (Minimum Point Distance IoU)
    基于最小点距离的IoU损失
    """
    def __init__(self, eps=1e-7):
        super().__init__()
        self.eps = eps
        
    def forward(self, pred, target):
        # 计算基础IoU
        iou = self.bbox_iou(pred, target)
        
        # 计算最小点距离
        mpd = self.compute_minimum_point_distance(pred, target)
        
        # 计算外接矩形对角线距离
        diagonal = self.compute_diagonal_distance(pred, target)
        
        # MPDIoU损失
        mpdiou = iou - mpd / (diagonal + self.eps)
        
        return (1 - mpdiou).mean()
        
    def compute_minimum_point_distance(self, pred, target):
        """计算最小点距离"""
        # 获取边界框的四个角点
        pred_points = self.get_corner_points(pred)
        target_points = self.get_corner_points(target)
        
        # 计算所有点对之间的距离
        min_distances = []
        for i in range(pred.shape[0]):
            pred_pts = pred_points[i]  # [4, 2]
            target_pts = target_points[i]  # [4, 2]
            
            # 计算距离矩阵
            distances = torch.cdist(pred_pts.unsqueeze(0), target_pts.unsqueeze(0)).squeeze(0)
            min_dist = torch.min(distances)
            min_distances.append(min_dist)
            
        return torch.stack(min_distances)
        
    def get_corner_points(self, boxes):
        """获取边界框的四个角点"""
        x1, y1, x2, y2 = boxes[:, 0], boxes[:, 1], boxes[:, 2], boxes[:, 3]
        
        # 四个角点: 左上、右上、右下、左下
        points = torch.stack([
            torch.stack([x1, y1], dim=1),  # 左上
            torch.stack([x2, y1], dim=1),  # 右上
            torch.stack([x2, y2], dim=1),  # 右下
            torch.stack([x1, y2], dim=1),  # 左下
        ], dim=1)  # [N, 4, 2]
        
        return points
        
    def compute_diagonal_distance(self, pred, target):
        """计算外接矩形对角线距离"""
        # 外接矩形
        x1 = torch.min(pred[:, 0], target[:, 0])
        y1 = torch.min(pred[:, 1], target[:, 1])
        x2 = torch.max(pred[:, 2], target[:, 2])
        y2 = torch.max(pred[:, 3], target[:, 3])
        
        diagonal = torch.sqrt((x2 - x1)**2 + (y2 - y1)**2 + self.eps)
        return diagonal
        
    def bbox_iou(self, box1, box2):
        """计算IoU"""
        inter_x1 = torch.max(box1[:, 0], box2[:, 0])
        inter_y1 = torch.max(box1[:, 1], box2[:, 1])
        inter_x2 = torch.min(box1[:, 2], box2[:, 2])
        inter_y2 = torch.min(box1[:, 3], box2[:, 3])
        
        inter_area = torch.clamp(inter_x2 - inter_x1, min=0) * torch.clamp(inter_y2 - inter_y1, min=0)
        
        box1_area = (box1[:, 2] - box1[:, 0]) * (box1[:, 3] - box1[:, 1])
        box2_area = (box2[:, 2] - box2[:, 0]) * (box2[:, 3] - box2[:, 1])
        union_area = box1_area + box2_area - inter_area
        
        iou = inter_area / (union_area + self.eps)
        return iou

class FocalEIoULoss(nn.Module):
    """
    Focal-EIoU Loss
    结合Focal Loss机制的EIoU损失
    """
    def __init__(self, alpha=0.25, gamma=2.0, eps=1e-7):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.eps = eps
        
    def forward(self, pred, target):
        # 计算EIoU
        eiou = self.compute_eiou(pred, target)
        
        # 应用Focal机制
        focal_weight = self.alpha * (1 - eiou) ** self.gamma
        
        # Focal-EIoU损失
        focal_eiou_loss = focal_weight * (1 - eiou)
        
        return focal_eiou_loss.mean()
        
    def compute_eiou(self, pred, target):
        """计算EIoU (Efficient IoU)"""
        # 基础IoU
        iou = self.bbox_iou(pred, target)
        
        # 中心点距离
        pred_center = (pred[:, :2] + pred[:, 2:]) / 2
        target_center = (target[:, :2] + target[:, 2:]) / 2
        center_distance = torch.norm(pred_center - target_center, dim=1)
        
        # 外接矩形对角线距离
        c_x = torch.max(pred[:, 2], target[:, 2]) - torch.min(pred[:, 0], target[:, 0])
        c_y = torch.max(pred[:, 3], target[:, 3]) - torch.min(pred[:, 1], target[:, 1])
        c = torch.sqrt(c_x**2 + c_y**2 + self.eps)
        
        # 宽高差异
        pred_wh = pred[:, 2:] - pred[:, :2]
        target_wh = target[:, 2:] - target[:, :2]
        
        w_loss = (pred_wh[:, 0] - target_wh[:, 0]) ** 2 / (c_x ** 2 + self.eps)
        h_loss = (pred_wh[:, 1] - target_wh[:, 1]) ** 2 / (c_y ** 2 + self.eps)
        
        # EIoU
        eiou = iou - center_distance**2 / (c**2 + self.eps) - w_loss - h_loss
        
        return eiou
        
    def bbox_iou(self, box1, box2):
        """计算IoU"""
        inter_x1 = torch.max(box1[:, 0], box2[:, 0])
        inter_y1 = torch.max(box1[:, 1], box2[:, 1])
        inter_x2 = torch.min(box1[:, 2], box2[:, 2])
        inter_y2 = torch.min(box1[:, 3], box2[:, 3])
        
        inter_area = torch.clamp(inter_x2 - inter_x1, min=0) * torch.clamp(inter_y2 - inter_y1, min=0)
        
        box1_area = (box1[:, 2] - box1[:, 0]) * (box1[:, 3] - box1[:, 1])
        box2_area = (box2[:, 2] - box2[:, 0]) * (box2[:, 3] - box2[:, 1])
        union_area = box1_area + box2_area - inter_area
        
        iou = inter_area / (union_area + self.eps)
        return iou

# 损失函数工厂
def get_advanced_loss_function(loss_type="wiou"):
    """
    获取先进的损失函数
    
    Args:
        loss_type: 损失函数类型 ["wiou", "siou", "mpdiou", "focal_eiou"]
    """
    loss_functions = {
        "wiou": WIoULoss(),
        "siou": SIoULoss(),
        "mpdiou": MPDIoULoss(),
        "focal_eiou": FocalEIoULoss()
    }
    
    if loss_type not in loss_functions:
        raise ValueError(f"不支持的损失函数类型: {loss_type}")
        
    return loss_functions[loss_type]

# 测试函数
def test_loss_functions():
    """测试所有损失函数"""
    # 创建测试数据
    pred = torch.tensor([[10, 10, 50, 50], [20, 20, 60, 60]], dtype=torch.float32)
    target = torch.tensor([[15, 15, 55, 55], [25, 25, 65, 65]], dtype=torch.float32)
    
    loss_types = ["wiou", "siou", "mpdiou", "focal_eiou"]
    
    print("损失函数测试结果:")
    for loss_type in loss_types:
        loss_fn = get_advanced_loss_function(loss_type)
        loss_value = loss_fn(pred, target)
        print(f"{loss_type.upper()}: {loss_value.item():.4f}")

if __name__ == "__main__":
    test_loss_functions()
