#!/usr/bin/env python3
"""
2025年比赛RGB-D YOLO训练脚本 (GPU7修复版本)
修复AMP兼容性问题，禁用自动混合精度
"""

import os
import time
import torch
from pathlib import Path
from ultralytics import YOLO
import json

class Competition2025TrainerGPU7Fixed:
    def __init__(self, dataset_path="competition_2025_dataset", output_dir="competition_2025_models_gpu7_fixed"):
        self.dataset_path = Path(dataset_path)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 比赛特定配置
        self.competition_config = {
            "target_platform": "香橙派 AI pro 8T",
            "memory_limit": "16GB",
            "camera": "奥比中光 Astra Pro Plus RGBD",
            "max_loading_time": "30s",
            "inference_time_target": "< 5s",
            "categories": 9,
        }
        
        # 类别映射
        self.category_mapping = {
            0: 'CA001_衣架',
            1: 'CA002_牙刷', 
            2: 'CB001_果冻',
            3: 'CB002_长方形状饼干',
            4: 'CC001_罐装饮料',
            5: 'CC002_瓶装饮料',
            6: 'CD001_香蕉',
            7: 'CD002_橙子',
            8: 'Wxxx_未知物品'
        }
        
    def train_model(self, model_name="yolo11n", description="GPU7修复版本", epochs=100):
        """训练单个模型（GPU7修复模式）"""
        print(f"\n{'='*60}")
        print(f"开始训练: {model_name} (GPU7修复模式)")
        print(f"描述: {description}")
        print(f"{'='*60}")
        
        # 设置GPU7
        os.environ['CUDA_VISIBLE_DEVICES'] = '7'
        torch.cuda.set_device(0)  # 在可见设备中选择第0个（即GPU7）
        
        print(f"使用GPU: {torch.cuda.get_device_name(0)}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        print("⚠️  注意: 已禁用AMP以解决兼容性问题")
        
        try:
            # 加载预训练模型
            model = YOLO(f"{model_name}.pt")
            
            # GPU7修复训练配置
            train_config = {
                'data': str(self.dataset_path / 'competition_dataset.yaml'),
                'epochs': epochs,
                'batch': 16,  # GPU适用的批次大小
                'imgsz': 640,  # 标准图像尺寸
                'device': 0,  # 使用可见设备中的第0个（GPU7）
                'project': str(self.output_dir),
                'name': f'competition_2025_{model_name}_gpu7_fixed',
                'exist_ok': True,
                'patience': 30,
                'save': True,
                'save_period': 10,
                'plots': True,
                'verbose': True,
                'workers': 8,
                'cache': False,
                'amp': False,  # 关键修复：禁用AMP解决兼容性问题
                
                # 优化器设置
                'optimizer': 'AdamW',
                'lr0': 0.001,
                'lrf': 0.01,
                'momentum': 0.937,
                'weight_decay': 0.0005,
                'warmup_epochs': 3,
                'warmup_momentum': 0.8,
                'warmup_bias_lr': 0.1,
                
                # 数据增强
                'hsv_h': 0.015,
                'hsv_s': 0.7,
                'hsv_v': 0.4,
                'degrees': 15.0,
                'translate': 0.1,
                'scale': 0.5,
                'shear': 0.0,
                'perspective': 0.0,
                'flipud': 0.0,
                'fliplr': 0.5,
                'mosaic': 1.0,
                'mixup': 0.0,
                'copy_paste': 0.0,
            }
            
            print("训练配置:")
            for key, value in train_config.items():
                print(f"  {key}: {value}")
            
            # 开始训练
            start_time = time.time()
            results = model.train(**train_config)
            training_time = time.time() - start_time
            
            print(f"\n✅ 训练完成！")
            print(f"训练时间: {training_time/3600:.2f} 小时")
            
            # 测试模型性能
            best_model_path = results.save_dir / 'weights' / 'best.pt'
            if best_model_path.exists():
                performance_results = self.evaluate_model(str(best_model_path), model_name, description, training_time)
                return performance_results
            else:
                print("⚠️ 最佳模型文件未找到")
                return None
                
        except Exception as e:
            print(f"❌ 训练失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def evaluate_model(self, model_path, model_name, description, training_time):
        """评估模型性能"""
        print(f"\n评估模型: {model_path}")
        
        try:
            # 加载训练好的模型
            start_time = time.time()
            model = YOLO(model_path)
            model.to('cuda:0')  # 确保使用GPU7（在CUDA_VISIBLE_DEVICES=7下的设备0）
            loading_time = time.time() - start_time
            
            # 创建测试图像进行推理时间测试
            import numpy as np
            test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
            
            # 预热
            _ = model(test_image, device=0, verbose=False)
            
            # 测试推理时间
            inference_times = []
            for _ in range(5):
                start_time = time.time()
                _ = model(test_image, device=0, verbose=False)
                inference_times.append(time.time() - start_time)
            
            avg_inference_time = sum(inference_times) / len(inference_times)
            
            # 在验证集上评估
            val_results = model.val(data=str(self.dataset_path / 'competition_dataset.yaml'), device=0)
            
            # 提取指标
            metrics = {
                'mAP50': float(val_results.box.map50),
                'mAP50_95': float(val_results.box.map),
                'precision': float(val_results.box.mp),
                'recall': float(val_results.box.mr)
            }
            
            # 检查比赛合规性
            compliance = {
                'loading_time_ok': loading_time < 30,
                'inference_time_ok': avg_inference_time < 5,
                'memory_efficient': True
            }
            
            # 保存结果
            results = {
                'model_name': model_name,
                'description': description,
                'training_time_hours': training_time / 3600,
                'model_loading_time_seconds': loading_time,
                'inference_time_seconds': avg_inference_time,
                'metrics': metrics,
                'competition_compliance': compliance,
                'model_path': model_path,
                'training_mode': 'GPU7_Fixed',
                'amp_disabled': True,
                'config': {
                    'model': f"{model_name}.pt",
                    'data': str(self.dataset_path / 'competition_dataset.yaml'),
                    'device': 0,
                    'batch': 16,
                    'imgsz': 640,
                    'amp': False
                }
            }
            
            # 保存到JSON文件
            results_file = self.output_dir / f'training_results_{model_name}_gpu7_fixed.json'
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            print(f"\n📊 模型性能:")
            print(f"  mAP50: {metrics['mAP50']:.4f}")
            print(f"  mAP50-95: {metrics['mAP50_95']:.4f}")
            print(f"  精确率: {metrics['precision']:.4f}")
            print(f"  召回率: {metrics['recall']:.4f}")
            print(f"  模型加载时间: {loading_time:.3f}s {'✅' if compliance['loading_time_ok'] else '❌'}")
            print(f"  推理时间: {avg_inference_time:.3f}s {'✅' if compliance['inference_time_ok'] else '❌'}")
            print(f"  结果已保存到: {results_file}")
            
            return results
            
        except Exception as e:
            print(f"❌ 模型评估失败: {e}")
            import traceback
            traceback.print_exc()
            return None

def main():
    # 检查数据集是否存在
    dataset_path = "competition_2025_dataset"
    if not Path(dataset_path).exists():
        print(f"错误: 比赛数据集不存在: {dataset_path}")
        print("请先运行 competition_2025_dataset_preparation.py")
        return

    # 检查GPU7是否可用
    if not torch.cuda.is_available():
        print("❌ CUDA不可用")
        return
    
    if torch.cuda.device_count() < 8:
        print(f"❌ 系统只有 {torch.cuda.device_count()} 个GPU，无法使用GPU7")
        return
    
    print("=== 2025年比赛RGB-D YOLO模型训练 (GPU7修复版本) ===")
    print(f"✅ 检测到 {torch.cuda.device_count()} 个GPU")
    print(f"🎯 将使用GPU7: {torch.cuda.get_device_name(7)}")
    print("🔧 修复: 禁用AMP解决兼容性问题")

    # 创建训练器
    trainer = Competition2025TrainerGPU7Fixed(dataset_path)

    # 训练YOLOv11n模型
    print("开始训练YOLOv11n模型 (GPU7修复模式)...")
    result = trainer.train_model("yolo11n", "2025年比赛GPU7修复版YOLOv11n", epochs=150)

    if result:
        print(f"\n🎉 GPU7修复版训练完成!")
        print(f"mAP50: {result['metrics']['mAP50']:.4f}")
        print(f"模型加载时间: {result['model_loading_time_seconds']:.2f}s")
        print(f"推理时间: {result['inference_time_seconds']:.3f}s")
        print(f"训练时间: {result['training_time_hours']:.2f} 小时")
    else:
        print("❌ 训练失败")

    print("\n=== GPU7修复版训练完成 ===")
    print("模型已训练完成，可以开始实现推理系统")

if __name__ == "__main__":
    main()
