#!/usr/bin/env python3
"""
OCR集成测试器
测试OCR关键词映射功能与现有系统的集成
"""

import time
import json
from typing import List, Dict, Any

# 导入OCR关键词映射器
from ocr_keyword_mapping import OCRKeywordMapper

class OCRIntegrationTester:
    """OCR集成测试器"""
    
    def __init__(self):
        self.mapper = None
        self.test_results = {
            'mapper_loading': False,
            'integration_tests': [],
            'performance_tests': {},
            'compatibility_tests': [],
            'overall_success': False
        }
    
    def test_mapper_loading(self):
        """测试映射器加载"""
        print("🔄 测试OCR关键词映射器加载...")
        
        try:
            self.mapper = OCRKeywordMapper()
            
            # 尝试加载配置文件
            config_path = "config/ocr_categories.json"
            self.mapper.load_mapping_config(config_path)
            
            print("✅ OCR关键词映射器加载成功")
            self.test_results['mapper_loading'] = True
            return True
            
        except Exception as e:
            print(f"❌ 映射器加载失败: {e}")
            return False
    
    def test_enhanced_classification(self):
        """测试增强分类功能"""
        print("\n📝 测试增强分类功能...")
        
        if self.mapper is None:
            print("⚠️  映射器未加载，跳过分类测试")
            return False
        
        # 模拟OCR识别结果
        test_cases = [
            # (OCR文本, 期望类别)
            ("高等数学教程", "W001"),
            ("大学物理实验", "W002"),
            ("布洛芬胶囊说明书", "W003"),
            ("xiaomi小米充电宝", "W004"),
            ("雅思写作指南", "W005"),
            ("毛泽东思想概论", "W006"),
            ("香辣牛肉方便面", "W007"),
            ("晨光墨水笔芯", "W008"),
            ("不相关的随机文本", None)  # 应该返回None
        ]
        
        successful_classifications = 0
        total_time = 0
        
        for ocr_text, expected_category in test_cases:
            start_time = time.time()
            
            # 使用映射器进行分类
            predicted_category, confidence = self.mapper.classify_text(ocr_text)
            
            classification_time = time.time() - start_time
            total_time += classification_time
            
            # 判断分类是否正确
            is_correct = (predicted_category == expected_category)
            if is_correct:
                successful_classifications += 1
            
            # 记录测试结果
            test_result = {
                'ocr_text': ocr_text,
                'expected_category': expected_category,
                'predicted_category': predicted_category,
                'confidence': confidence,
                'classification_time': classification_time,
                'is_correct': is_correct
            }
            
            self.test_results['integration_tests'].append(test_result)
            
            # 打印结果
            status = "✅" if is_correct else "❌"
            if predicted_category:
                category_name = self.mapper.get_category_info(predicted_category).get('name', predicted_category)
                print(f"  {status} '{ocr_text}' -> {predicted_category} ({category_name}) [置信度: {confidence:.3f}]")
            else:
                print(f"  {status} '{ocr_text}' -> 未分类 [置信度: {confidence:.3f}]")
        
        # 计算性能指标
        accuracy = successful_classifications / len(test_cases)
        avg_time = total_time / len(test_cases)
        
        self.test_results['performance_tests'] = {
            'total_tests': len(test_cases),
            'successful_classifications': successful_classifications,
            'accuracy': accuracy,
            'total_time': total_time,
            'average_time': avg_time
        }
        
        print(f"\n📊 分类测试结果:")
        print(f"  总测试数: {len(test_cases)}")
        print(f"  成功分类: {successful_classifications}")
        print(f"  准确率: {accuracy:.1%}")
        print(f"  平均分类时间: {avg_time:.4f}s")
        
        return accuracy >= 0.8  # 80%准确率阈值
    
    def test_compatibility_with_existing_system(self):
        """测试与现有系统的兼容性"""
        print("\n🔗 测试与现有系统的兼容性...")
        
        if self.mapper is None:
            print("⚠️  映射器未加载，跳过兼容性测试")
            return False
        
        # 模拟现有系统的classify_unknown_object方法的输入格式
        test_texts_lists = [
            ["高等数学"],
            ["大学", "物理"],
            ["布洛芬", "胶囊"],
            ["xiaomi", "充电宝"],
            ["雅思", "写作"],
            ["毛泽东", "思想"],
            ["方便面"],
            ["墨水", "笔芯"]
        ]
        
        compatibility_success = 0
        
        for texts in test_texts_lists:
            try:
                # 模拟现有系统的调用方式
                combined_text = ' '.join(texts)
                category, confidence = self.mapper.classify_text(combined_text)
                
                # 检查返回格式是否符合预期
                format_ok = (
                    (category is None or isinstance(category, str)) and
                    isinstance(confidence, (int, float)) and
                    0 <= confidence <= 1
                )
                
                if format_ok and category is not None:
                    compatibility_success += 1
                    status = "✅"
                else:
                    status = "❌"
                
                self.test_results['compatibility_tests'].append({
                    'input_texts': texts,
                    'combined_text': combined_text,
                    'output_category': category,
                    'output_confidence': confidence,
                    'format_ok': format_ok,
                    'success': format_ok and category is not None
                })
                
                print(f"  {status} {texts} -> {category} (置信度: {confidence:.3f})")
                
            except Exception as e:
                print(f"  ❌ {texts} -> 异常: {e}")
                self.test_results['compatibility_tests'].append({
                    'input_texts': texts,
                    'error': str(e),
                    'success': False
                })
        
        compatibility_rate = compatibility_success / len(test_texts_lists)
        print(f"\n📊 兼容性测试结果:")
        print(f"  兼容性成功率: {compatibility_rate:.1%} ({compatibility_success}/{len(test_texts_lists)})")
        
        return compatibility_rate >= 0.9  # 90%兼容性阈值
    
    def test_performance_requirements(self):
        """测试性能要求"""
        print("\n⏱️  测试性能要求...")
        
        perf = self.test_results.get('performance_tests', {})
        if not perf:
            print("⚠️  没有性能数据，跳过性能测试")
            return False
        
        # 检查分类时间要求（平均<0.01秒，因为不包含实际OCR）
        time_ok = perf['average_time'] <= 0.01
        
        # 检查分类准确率要求（≥90%）
        accuracy_ok = perf['accuracy'] >= 0.9
        
        print(f"  平均分类时间 ≤ 0.01s: {'✅' if time_ok else '❌'} ({perf['average_time']:.4f}s)")
        print(f"  分类准确率 ≥ 90%: {'✅' if accuracy_ok else '❌'} ({perf['accuracy']:.1%})")
        
        return time_ok and accuracy_ok
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🚀 开始OCR集成综合测试")
        print("="*60)
        
        # 1. 测试映射器加载
        loading_ok = self.test_mapper_loading()
        
        # 2. 测试增强分类功能
        classification_ok = self.test_enhanced_classification()
        
        # 3. 测试兼容性
        compatibility_ok = self.test_compatibility_with_existing_system()
        
        # 4. 测试性能要求
        performance_ok = self.test_performance_requirements()
        
        # 综合评估
        overall_success = loading_ok and classification_ok and compatibility_ok and performance_ok
        self.test_results['overall_success'] = overall_success
        
        print("\n" + "="*60)
        print("📈 综合测试结果")
        print("="*60)
        print(f"映射器加载: {'✅' if loading_ok else '❌'}")
        print(f"分类功能: {'✅' if classification_ok else '❌'}")
        print(f"系统兼容性: {'✅' if compatibility_ok else '❌'}")
        print(f"性能要求: {'✅' if performance_ok else '❌'}")
        print(f"综合评估: {'✅ 测试通过' if overall_success else '❌ 测试未通过'}")
        print("="*60)
        
        return overall_success
    
    def save_test_results(self, output_path: str = "ocr_integration_test_results.json"):
        """保存测试结果"""
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        print(f"📄 测试结果已保存到: {output_path}")

def main():
    """主函数"""
    tester = OCRIntegrationTester()
    
    try:
        success = tester.run_comprehensive_test()
        tester.save_test_results()
        return success
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
