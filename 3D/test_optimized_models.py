#!/usr/bin/env python3
"""
测试优化后的模型性能
对比基线模型和优化模型的实际推理效果
"""

import os
import time
import json
import cv2
import numpy as np
from pathlib import Path
from ultralytics import YOLO
import matplotlib.pyplot as plt
import matplotlib.patches as patches

class ModelPerformanceTester:
    def __init__(self, test_image_dir="competition_2025_dataset/images/test"):
        self.test_image_dir = Path(test_image_dir)
        self.models = {}
        self.results = {}
        
        # 比赛类别映射
        self.category_mapping = {
            0: 'CA001_衣架',
            1: 'CA002_牙刷', 
            2: 'CB001_果冻',
            3: 'CB002_长方形状饼干',
            4: 'CC001_罐装饮料',
            5: 'CC002_瓶装饮料',
            6: 'CD001_香蕉',
            7: 'CD002_橙子',
            8: 'Wxxx_未知物品'
        }
        
    def load_models(self):
        """加载所有可用的模型"""
        model_paths = {
            "基线_YOLOv11n": "competition_2025_models/competition_2025_yolo11n/weights/best.pt",
            "优化_YOLOv11n": "optimized_models/class_weighted_yolo11n_thin_small/weights/best.pt",
            "优化_YOLOv11s": "optimized_models_s/class_weighted_yolo11s_thin_small/weights/best.pt"
        }
        
        for model_name, model_path in model_paths.items():
            if os.path.exists(model_path):
                print(f"加载模型: {model_name}")
                start_time = time.time()
                self.models[model_name] = YOLO(model_path)
                loading_time = time.time() - start_time
                print(f"  加载时间: {loading_time:.3f}s")
                
                # 预热模型
                dummy_image = np.zeros((640, 640, 3), dtype=np.uint8)
                _ = self.models[model_name](dummy_image, verbose=False)
            else:
                print(f"模型不存在: {model_path}")
                
        print(f"\n成功加载 {len(self.models)} 个模型")
        
    def test_single_image(self, image_path, conf_threshold=0.5):
        """测试单张图像在所有模型上的表现"""
        image = cv2.imread(str(image_path))
        if image is None:
            return None
            
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        image_name = Path(image_path).name
        
        results = {
            "image_name": image_name,
            "models": {}
        }
        
        for model_name, model in self.models.items():
            start_time = time.time()
            
            # 运行推理
            model_results = model(image_rgb, conf=conf_threshold, verbose=False)
            
            inference_time = time.time() - start_time
            
            # 解析结果
            detections = []
            if model_results[0].boxes is not None:
                boxes = model_results[0].boxes.xyxy.cpu().numpy()
                confidences = model_results[0].boxes.conf.cpu().numpy()
                classes = model_results[0].boxes.cls.cpu().numpy().astype(int)
                
                for box, conf, cls in zip(boxes, confidences, classes):
                    detection = {
                        'category_id': int(cls),
                        'category_name': self.category_mapping.get(int(cls), 'Unknown'),
                        'confidence': float(conf),
                        'bbox': [float(x) for x in box.tolist()]
                    }
                    detections.append(detection)
            
            # 统计各类别数量
            category_counts = {}
            for detection in detections:
                cat_name = detection['category_name']
                category_counts[cat_name] = category_counts.get(cat_name, 0) + 1
            
            results["models"][model_name] = {
                "inference_time": inference_time,
                "total_detections": len(detections),
                "detections": detections,
                "category_counts": category_counts
            }
            
        return results
        
    def test_multiple_images(self, num_images=10):
        """测试多张图像"""
        if not self.test_image_dir.exists():
            print(f"测试图像目录不存在: {self.test_image_dir}")
            return
            
        test_images = list(self.test_image_dir.glob("*.jpg"))[:num_images]
        
        if not test_images:
            print("没有找到测试图像")
            return
            
        print(f"\n开始测试 {len(test_images)} 张图像...")
        
        all_results = []
        
        for i, image_path in enumerate(test_images, 1):
            print(f"\n处理图像 {i}/{len(test_images)}: {image_path.name}")
            
            result = self.test_single_image(image_path)
            if result:
                all_results.append(result)
                
                # 显示每个模型的检测结果
                for model_name, model_result in result["models"].items():
                    print(f"  {model_name}: {model_result['total_detections']}个物体, "
                          f"{model_result['inference_time']:.3f}s")
                    
                    # 显示困难类别的检测情况
                    difficult_categories = ['CA002_牙刷', 'CB001_果冻']
                    for cat in difficult_categories:
                        count = model_result['category_counts'].get(cat, 0)
                        if count > 0:
                            print(f"    {cat}: {count}个")
        
        self.results = all_results
        return all_results
        
    def generate_comparison_report(self):
        """生成对比报告"""
        if not self.results:
            print("没有测试结果可供分析")
            return
            
        print(f"\n{'='*80}")
        print("模型性能对比报告")
        print(f"{'='*80}")
        
        # 统计各模型的平均性能
        model_stats = {}
        
        for model_name in self.models.keys():
            inference_times = []
            total_detections = []
            difficult_detections = {'CA002_牙刷': [], 'CB001_果冻': []}
            
            for result in self.results:
                if model_name in result["models"]:
                    model_result = result["models"][model_name]
                    inference_times.append(model_result['inference_time'])
                    total_detections.append(model_result['total_detections'])
                    
                    for cat in difficult_detections.keys():
                        count = model_result['category_counts'].get(cat, 0)
                        difficult_detections[cat].append(count)
            
            model_stats[model_name] = {
                "avg_inference_time": np.mean(inference_times),
                "avg_total_detections": np.mean(total_detections),
                "avg_ca002_detections": np.mean(difficult_detections['CA002_牙刷']),
                "avg_cb001_detections": np.mean(difficult_detections['CB001_果冻'])
            }
        
        # 生成表格
        print(f"{'模型':<20} {'平均推理时间(s)':<15} {'平均检测数':<12} {'牙刷检测':<10} {'果冻检测':<10}")
        print("-" * 80)
        
        for model_name, stats in model_stats.items():
            print(f"{model_name:<20} {stats['avg_inference_time']:<15.3f} "
                  f"{stats['avg_total_detections']:<12.1f} "
                  f"{stats['avg_ca002_detections']:<10.1f} "
                  f"{stats['avg_cb001_detections']:<10.1f}")
        
        # 分析改进效果
        if "基线_YOLOv11n" in model_stats and "优化_YOLOv11n" in model_stats:
            baseline = model_stats["基线_YOLOv11n"]
            optimized = model_stats["优化_YOLOv11n"]
            
            print(f"\n🎯 优化效果分析:")
            
            ca002_improvement = optimized['avg_ca002_detections'] - baseline['avg_ca002_detections']
            cb001_improvement = optimized['avg_cb001_detections'] - baseline['avg_cb001_detections']
            
            print(f"CA002_牙刷检测改进: {ca002_improvement:+.1f} 个/图像")
            print(f"CB001_果冻检测改进: {cb001_improvement:+.1f} 个/图像")
            
            if ca002_improvement > 0 or cb001_improvement > 0:
                print("✅ 优化策略有效！困难类别检测能力提升")
            else:
                print("⚠️  需要进一步调整优化策略")
        
        # 保存详细报告
        report_file = "model_comparison_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                "model_stats": model_stats,
                "detailed_results": self.results
            }, f, indent=2, ensure_ascii=False)
            
        print(f"\n详细报告已保存: {report_file}")
        
    def visualize_detection_comparison(self, image_index=0):
        """可视化检测结果对比"""
        if not self.results or image_index >= len(self.results):
            print("没有可用的结果进行可视化")
            return
            
        result = self.results[image_index]
        image_name = result["image_name"]
        
        # 加载原始图像
        image_path = self.test_image_dir / image_name
        image = cv2.imread(str(image_path))
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 创建子图
        num_models = len(result["models"])
        fig, axes = plt.subplots(1, num_models, figsize=(6*num_models, 6))
        if num_models == 1:
            axes = [axes]
            
        colors = plt.cm.Set3(np.linspace(0, 1, 9))
        
        for i, (model_name, model_result) in enumerate(result["models"].items()):
            ax = axes[i]
            ax.imshow(image_rgb)
            ax.set_title(f"{model_name}\n检测数: {model_result['total_detections']}, "
                        f"时间: {model_result['inference_time']:.3f}s")
            
            # 绘制检测框
            for detection in model_result['detections']:
                bbox = detection['bbox']
                x1, y1, x2, y2 = bbox
                width = x2 - x1
                height = y2 - y1
                
                category_id = detection['category_id']
                category_name = detection['category_name']
                confidence = detection['confidence']
                
                # 绘制边界框
                rect = patches.Rectangle(
                    (x1, y1), width, height,
                    linewidth=2, edgecolor=colors[category_id], facecolor='none'
                )
                ax.add_patch(rect)
                
                # 添加标签
                label = f"{category_name}: {confidence:.2f}"
                ax.text(x1, y1-5, label,
                       bbox=dict(boxstyle="round,pad=0.3", 
                               facecolor=colors[category_id], alpha=0.7),
                       fontsize=8, color='black')
            
            ax.axis('off')
        
        plt.tight_layout()
        plt.savefig(f"detection_comparison_{image_name}", dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"可视化结果已保存: detection_comparison_{image_name}")

def main():
    """主函数"""
    print("=== 优化模型性能测试 ===")
    
    # 创建测试器
    tester = ModelPerformanceTester()
    
    # 加载模型
    tester.load_models()
    
    if not tester.models:
        print("没有可用的模型进行测试")
        return
    
    # 测试多张图像
    tester.test_multiple_images(num_images=10)
    
    # 生成对比报告
    tester.generate_comparison_report()
    
    # 可视化第一张图像的检测结果
    if tester.results:
        print("\n生成可视化对比图...")
        tester.visualize_detection_comparison(0)
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
