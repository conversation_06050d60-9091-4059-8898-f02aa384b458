#!/usr/bin/env python3
"""
针对细长物体和小物体优化的训练配置
基于Kaggle竞赛和学术界的最佳实践
"""

import os
import time
import torch
import yaml
from pathlib import Path
from ultralytics import YOLO
import json
import numpy as np

class OptimizedTrainerForThinSmallObjects:
    def __init__(self, dataset_path="competition_2025_dataset", output_dir="optimized_models"):
        self.dataset_path = Path(dataset_path)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
    def create_optimized_config(self, model_name="yolo11n"):
        """创建针对细长和小物体优化的训练配置"""
        config = {
            # 基础配置
            "model": f"{model_name}.pt",
            "data": str(self.dataset_path / "competition_dataset.yaml"),
            
            # 训练参数 - 针对困难物体优化
            "epochs": 250,  # 增加训练轮数
            "batch": 16,
            "imgsz": [640, 672, 704, 736, 768, 800],  # 多尺度训练
            "device": 0 if torch.cuda.is_available() else "cpu",
            
            # 优化器配置
            "optimizer": "AdamW",
            "lr0": 0.001,
            "lrf": 0.01,
            "momentum": 0.937,
            "weight_decay": 0.0005,
            "warmup_epochs": 5,  # 增加预热轮数
            "warmup_momentum": 0.8,
            "warmup_bias_lr": 0.1,
            
            # 数据增强 - 针对细长和小物体优化
            "hsv_h": 0.015,    # 适度的色调变化
            "hsv_s": 0.7,      # 饱和度变化
            "hsv_v": 0.4,      # 亮度变化
            "degrees": 5.0,    # 减少旋转角度（细长物体敏感）
            "translate": 0.1,  # 平移
            "scale": 0.9,      # 增大缩放范围
            "shear": 0.0,      # 避免剪切（保持形状）
            "perspective": 0.0, # 避免透视变换
            "flipud": 0.0,     # 避免上下翻转（细长物体）
            "fliplr": 0.5,     # 保持左右翻转
            "mosaic": 1.0,     # 马赛克增强（小物体有效）
            "mixup": 0.15,     # 混合增强
            "copy_paste": 0.3, # Copy-paste增强（小物体关键）
            
            # 损失函数优化
            "cls": 1.0,        # 分类损失
            "box": 10.0,       # 增加边界框损失权重
            "dfl": 2.0,        # 增加分布焦点损失权重
            
            # 验证和保存
            "val": True,
            "save": True,
            "save_period": 10,
            "plots": True,
            "patience": 50,    # 增加早停耐心
            
            # 项目配置
            "project": str(self.output_dir),
            "name": f"optimized_{model_name}_thin_small",
            "exist_ok": True,
        }
        
        return config
        
    def create_class_weighted_config(self, model_name="yolo11n"):
        """创建带类别权重的配置，重点优化CA002和CB001"""
        config = self.create_optimized_config(model_name)
        
        # 为困难类别增加权重
        config.update({
            "name": f"class_weighted_{model_name}_thin_small",
            # 注意：YOLO的类别权重需要在数据集配置中设置
        })
        
        return config
        
    def create_weighted_dataset_config(self):
        """创建带类别权重的数据集配置"""
        config_content = f"""# 优化的比赛数据集配置（带类别权重）
path: {self.dataset_path.absolute()}
train: images/train
val: images/val
test: images/test

# 类别配置
nc: 9
names: ['CA001_衣架', 'CA002_牙刷', 'CB001_果冻', 'CB002_长方形状饼干',
        'CC001_罐装饮料', 'CC002_瓶装饮料', 'CD001_香蕉', 'CD002_橙子', 'Wxxx_未知物品']

# 类别权重 - 为困难类别增加权重
class_weights: [1.0, 3.0, 3.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.5]
# 对应: [衣架, 牙刷, 果冻, 饼干, 罐装饮料, 瓶装饮料, 香蕉, 橙子, 未知物品]

# 深度图像路径
depth_train: depth/train
depth_val: depth/val
depth_test: depth/test

# 针对小物体和细长物体的特殊配置
small_object_threshold: 32  # 小物体阈值
thin_object_aspect_ratio: 3.0  # 细长物体长宽比阈值

# 数据增强特殊配置
augment_small_objects: true
augment_thin_objects: true
preserve_aspect_ratio: true
"""
        
        config_file = self.dataset_path / "optimized_dataset.yaml"
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
            
        return config_file
        
    def train_optimized_model(self, model_name="yolo11n", use_class_weights=True):
        """训练优化模型"""
        print(f"\n{'='*60}")
        print(f"开始优化训练: {model_name}")
        print(f"针对细长物体和小物体优化")
        print(f"{'='*60}")
        
        try:
            # 创建优化的数据集配置
            if use_class_weights:
                dataset_config = self.create_weighted_dataset_config()
                config = self.create_class_weighted_config(model_name)
                config["data"] = str(dataset_config)
            else:
                config = self.create_optimized_config(model_name)
            
            # 加载模型
            model = YOLO(config["model"])
            
            # 记录开始时间
            start_time = time.time()
            
            # 开始训练
            results = model.train(**{k: v for k, v in config.items() 
                                   if k not in ["model"]})
            
            # 记录结束时间
            end_time = time.time()
            training_time = end_time - start_time
            
            # 测试模型性能
            model_path = self.output_dir / config["name"] / "weights" / "best.pt"
            
            # 加载最佳模型进行评估
            best_model = YOLO(str(model_path))
            
            # 在验证集上评估
            val_results = best_model.val(data=config["data"], split='val')
            
            # 测试推理速度
            test_image = self.dataset_path / "images" / "val"
            test_images = list(test_image.glob("*.jpg"))
            if test_images:
                inference_start = time.time()
                _ = best_model(str(test_images[0]), verbose=False)
                inference_time = time.time() - inference_start
            else:
                inference_time = 0
            
            # 保存训练结果
            training_results = {
                "model_name": model_name,
                "optimization_type": "thin_small_objects",
                "use_class_weights": use_class_weights,
                "training_time_hours": training_time / 3600,
                "inference_time_seconds": inference_time,
                "metrics": {
                    "mAP50": float(val_results.box.map50),
                    "mAP50_95": float(val_results.box.map),
                    "precision": float(val_results.box.mp),
                    "recall": float(val_results.box.mr),
                },
                "class_metrics": {},
                "model_path": str(model_path),
                "config": config
            }
            
            # 获取各类别性能
            if hasattr(val_results.box, 'maps') and val_results.box.maps is not None:
                class_names = ['CA001_衣架', 'CA002_牙刷', 'CB001_果冻', 'CB002_长方形状饼干',
                              'CC001_罐装饮料', 'CC002_瓶装饮料', 'CD001_香蕉', 'CD002_橙子', 'Wxxx_未知物品']
                
                for i, class_map in enumerate(val_results.box.maps):
                    if i < len(class_names):
                        training_results["class_metrics"][class_names[i]] = float(class_map)
            
            # 保存结果
            result_file = self.output_dir / f"optimized_results_{model_name}.json"
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(training_results, f, indent=2, ensure_ascii=False)
            
            print(f"\n优化训练完成: {model_name}")
            print(f"mAP50: {training_results['metrics']['mAP50']:.4f}")
            print(f"mAP50-95: {training_results['metrics']['mAP50_95']:.4f}")
            
            # 重点关注困难类别的改进
            if training_results["class_metrics"]:
                print(f"\n困难类别性能:")
                print(f"CA002_牙刷: {training_results['class_metrics'].get('CA002_牙刷', 0):.4f}")
                print(f"CB001_果冻: {training_results['class_metrics'].get('CB001_果冻', 0):.4f}")
            
            print(f"训练时间: {training_time/3600:.2f}小时")
            
            return training_results
            
        except Exception as e:
            print(f"优化训练失败: {e}")
            return None
    
    def compare_with_baseline(self, baseline_results_file="competition_2025_models/training_results_yolo11n.json"):
        """与基线模型对比"""
        if not os.path.exists(baseline_results_file):
            print("基线结果文件不存在，无法对比")
            return
            
        with open(baseline_results_file, 'r', encoding='utf-8') as f:
            baseline = json.load(f)
            
        optimized_file = self.output_dir / "optimized_results_yolo11n.json"
        if not optimized_file.exists():
            print("优化结果文件不存在，请先运行优化训练")
            return
            
        with open(optimized_file, 'r', encoding='utf-8') as f:
            optimized = json.load(f)
            
        print(f"\n{'='*60}")
        print("基线 vs 优化模型对比")
        print(f"{'='*60}")
        
        print(f"{'指标':<15} {'基线':<10} {'优化':<10} {'改进':<10}")
        print("-" * 50)
        
        baseline_map50 = baseline['metrics']['mAP50']
        optimized_map50 = optimized['metrics']['mAP50']
        improvement_map50 = optimized_map50 - baseline_map50
        
        baseline_map50_95 = baseline['metrics']['mAP50_95']
        optimized_map50_95 = optimized['metrics']['mAP50_95']
        improvement_map50_95 = optimized_map50_95 - baseline_map50_95
        
        print(f"{'mAP50':<15} {baseline_map50:<10.4f} {optimized_map50:<10.4f} {improvement_map50:<+10.4f}")
        print(f"{'mAP50-95':<15} {baseline_map50_95:<10.4f} {optimized_map50_95:<10.4f} {improvement_map50_95:<+10.4f}")
        
        # 困难类别对比
        if optimized.get("class_metrics"):
            print(f"\n困难类别改进:")
            for class_name in ['CA002_牙刷', 'CB001_果冻']:
                if class_name in optimized["class_metrics"]:
                    opt_score = optimized["class_metrics"][class_name]
                    print(f"{class_name}: {opt_score:.4f}")

def main():
    """主函数"""
    print("=== 针对细长和小物体的优化训练 ===")
    
    # 检查数据集
    dataset_path = "competition_2025_dataset"
    if not Path(dataset_path).exists():
        print(f"错误: 数据集不存在: {dataset_path}")
        return
    
    # 创建优化训练器
    trainer = OptimizedTrainerForThinSmallObjects(dataset_path)
    
    # 运行优化训练
    print("开始针对细长物体和小物体的优化训练...")
    result = trainer.train_optimized_model("yolo11n", use_class_weights=True)
    
    if result:
        print("\n优化训练完成!")
        
        # 与基线对比
        trainer.compare_with_baseline()
        
        print(f"\n建议:")
        print(f"1. 如果CA002_牙刷和CB001_果冻性能有显著提升，说明优化有效")
        print(f"2. 可以尝试更大的模型(yolo11s)进一步提升")
        print(f"3. 考虑使用TTA(Test Time Augmentation)进一步提升推理性能")

if __name__ == "__main__":
    main()
