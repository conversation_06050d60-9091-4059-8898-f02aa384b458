#!/usr/bin/env python3
"""
简化版推理系统测试
测试最佳性能模型的推理功能
"""

import os
import time
import cv2
import numpy as np
from pathlib import Path
from ultralytics import YOLO

class SimpleInferenceSystem:
    def __init__(self, model_path):
        self.model_path = model_path
        self.model = None
        
        # 比赛类别映射
        self.category_mapping = {
            0: 'CA001',  # 衣架
            1: 'CA002',  # 牙刷
            2: 'CB001',  # 果冻
            3: 'CB002',  # 长方形状饼干
            4: 'CC001',  # 罐装饮料
            5: 'CC002',  # 瓶装饮料
            6: 'CD001',  # 香蕉
            7: 'CD002',  # 橙子
            8: 'Wxxx'    # 未知物品
        }
    
    def load_model(self):
        print(f'正在加载模型: {self.model_path}')
        start_time = time.time()
        
        self.model = YOLO(self.model_path)
        self.model.to('cpu')  # 强制使用CPU
        
        loading_time = time.time() - start_time
        print(f'✅ 模型加载完成，耗时: {loading_time:.3f}s')
        
        return loading_time
    
    def process_image(self, image_path, conf_threshold=0.25):
        if self.model is None:
            raise RuntimeError('模型未加载')
        
        print(f'处理图像: {os.path.basename(image_path)}')
        
        # 进行推理
        start_time = time.time()
        results = self.model(image_path, conf=conf_threshold, device='cpu', verbose=False)
        inference_time = time.time() - start_time
        
        # 解析结果
        detections = []
        if len(results[0].boxes) > 0:
            boxes = results[0].boxes
            for box in boxes:
                cls_id = int(box.cls[0])
                conf = float(box.conf[0])
                category = self.category_mapping[cls_id]
                
                detections.append({
                    'category': category,
                    'confidence': conf,
                    'bbox': box.xyxy[0].tolist()
                })
        
        # 统计数量
        counts = {}
        for category in self.category_mapping.values():
            counts[category] = sum(1 for d in detections if d['category'] == category)
        
        # 生成比赛格式输出
        output_lines = []
        for category, count in counts.items():
            if count > 0:
                output_lines.append(f'{category};{count}')
        
        results_text = '\n'.join(output_lines) if output_lines else 'No objects detected'
        
        return {
            'detections': detections,
            'counts': counts,
            'results_text': results_text,
            'inference_time': inference_time,
            'total_objects': len(detections)
        }

def main():
    print('=== 简化版推理系统测试 ===')
    
    # 使用最佳模型
    best_model_path = 'optimized_models/class_weighted_yolo11n_thin_small/weights/best.pt'
    
    if not os.path.exists(best_model_path):
        print(f'❌ 模型文件不存在: {best_model_path}')
        print('尝试使用基础模型...')
        best_model_path = 'competition_2025_models/competition_2025_yolo11n/weights/best.pt'
        
        if not os.path.exists(best_model_path):
            print(f'❌ 基础模型也不存在: {best_model_path}')
            return
    
    # 创建推理系统
    system = SimpleInferenceSystem(best_model_path)
    
    # 加载模型
    loading_time = system.load_model()
    
    # 测试图像
    test_images_dir = 'competition_2025_dataset/images/test'
    if os.path.exists(test_images_dir):
        test_images = [f for f in os.listdir(test_images_dir) if f.endswith(('.jpg', '.png'))][:3]  # 测试前3张
        
        print(f'\n=== 测试 {len(test_images)} 张图像 ===')
        
        total_inference_time = 0
        total_objects = 0
        
        for i, img_name in enumerate(test_images, 1):
            img_path = os.path.join(test_images_dir, img_name)
            
            try:
                results = system.process_image(img_path)
                total_inference_time += results['inference_time']
                total_objects += results['total_objects']
                
                print(f'\n图像 {i}: {img_name}')
                print(f'  推理时间: {results["inference_time"]:.3f}s')
                print(f'  检测物体: {results["total_objects"]}')
                if results['total_objects'] > 0:
                    print(f'  物体详情:')
                    for det in results['detections']:
                        print(f'    {det["category"]} (置信度: {det["confidence"]:.3f})')
                print(f'  比赛格式输出: {results["results_text"]}')
                
            except Exception as e:
                print(f'  ❌ 处理失败: {e}')
        
        print(f'\n=== 总结 ===')
        print(f'模型路径: {best_model_path}')
        print(f'模型加载时间: {loading_time:.3f}s')
        print(f'平均推理时间: {total_inference_time/len(test_images):.3f}s')
        print(f'总检测物体数: {total_objects}')
        print(f'✅ 推理系统运行正常')
        
    else:
        print('❌ 测试图像目录不存在')

if __name__ == "__main__":
    main()
