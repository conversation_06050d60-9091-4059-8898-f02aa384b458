#!/usr/bin/env python3
"""
测试GPU7 AMP修复方案
"""

import os
import torch
from ultralytics import YOLO
import numpy as np

def test_gpu7_training():
    """测试GPU7训练是否能正常启动"""
    
    # 设置GPU7
    os.environ['CUDA_VISIBLE_DEVICES'] = '7'
    
    print("=== GPU7 AMP修复测试 ===")
    print(f"CUDA_VISIBLE_DEVICES: {os.environ.get('CUDA_VISIBLE_DEVICES')}")
    print(f"可见GPU数量: {torch.cuda.device_count()}")
    
    if torch.cuda.device_count() > 0:
        print(f"GPU 0 (实际GPU7): {torch.cuda.get_device_name(0)}")
    
    try:
        # 加载YOLOv11n模型
        print("\n1. 加载YOLOv11n模型...")
        model = YOLO('yolo11n.pt')
        print("✅ 模型加载成功")
        
        # 创建虚拟数据集配置
        print("\n2. 创建测试配置...")
        
        # 检查数据集是否存在
        dataset_path = "competition_2025_dataset/competition_dataset.yaml"
        if not os.path.exists(dataset_path):
            print(f"❌ 数据集不存在: {dataset_path}")
            print("请先运行数据集准备脚本")
            return False
        
        # 测试训练配置（只训练1个epoch）
        train_config = {
            'data': dataset_path,
            'epochs': 1,  # 只训练1个epoch用于测试
            'batch': 4,   # 小批次
            'imgsz': 320, # 小图像尺寸
            'device': 0,  # 使用可见设备中的第0个（GPU7）
            'project': 'test_gpu7_fix',
            'name': 'test_run',
            'exist_ok': True,
            'patience': 10,
            'save': False,  # 不保存模型
            'plots': False, # 不生成图表
            'verbose': True,
            'workers': 2,
            'cache': False,
            'amp': False,  # 关键：禁用AMP
        }
        
        print("训练配置:")
        for key, value in train_config.items():
            print(f"  {key}: {value}")
        
        print("\n3. 开始测试训练...")
        results = model.train(**train_config)
        
        print("✅ 训练测试成功完成！")
        print("🎉 GPU7 AMP修复方案有效")
        return True
        
    except Exception as e:
        print(f"❌ 训练测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_gpu7_training()
    if success:
        print("\n=== 测试结论 ===")
        print("✅ GPU7可以正常训练（禁用AMP）")
        print("✅ 可以使用修复版本的训练脚本")
        print("✅ 建议使用 competition_2025_training_gpu7_fixed.py")
    else:
        print("\n=== 测试结论 ===")
        print("❌ GPU7训练仍有问题")
        print("❌ 建议使用CPU训练版本")
