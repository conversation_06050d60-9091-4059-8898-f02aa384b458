#!/usr/bin/env python3
"""
增强OCR分类器
基于现有competition_2025_inference_system.py中的OCR功能，扩展实现增强的未知物品分类系统
支持W001-W008子类别识别，集成OCR关键词映射字典
"""

import cv2
import numpy as np
import time
import logging
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
# 导入我们的OCR关键词映射器
from ocr_keyword_mapping import OCRKeywordMapper

# 尝试导入EasyOCR，如果失败则使用模拟模式
try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False
    print("⚠️  EasyOCR未安装，将使用模拟模式进行测试")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedOCRClassifier:
    """增强OCR分类器"""
    
    def __init__(self, languages=['ch_sim', 'en'], gpu=True):
        self.ocr_reader = None
        self.keyword_mapper = None
        self.languages = languages
        self.gpu = gpu
        
        # OCR配置参数
        self.ocr_config = {
            'confidence_threshold': 0.5,
            'text_threshold': 0.7,
            'low_text': 0.4,
            'link_threshold': 0.4,
            'canvas_size': 2560,
            'mag_ratio': 1.5
        }
        
        # 图像预处理参数
        self.preprocess_config = {
            'contrast_alpha': 1.2,
            'brightness_beta': 10,
            'gaussian_blur_ksize': (3, 3),
            'morphology_kernel_size': (2, 2),
            'min_roi_area': 100
        }
        
        # 性能统计
        self.performance_stats = {
            'ocr_times': [],
            'classification_times': [],
            'preprocessing_times': [],
            'total_classifications': 0,
            'successful_classifications': 0
        }
        
    def load_models(self):
        """加载OCR模型和关键词映射器"""
        print("🔄 加载增强OCR分类器...")
        start_time = time.time()
        
        # 加载EasyOCR
        if EASYOCR_AVAILABLE:
            try:
                print("  加载EasyOCR模型...")
                self.ocr_reader = easyocr.Reader(
                    self.languages,
                    gpu=self.gpu,
                    verbose=False
                )
                print("  ✅ EasyOCR模型加载成功")
            except Exception as e:
                print(f"  ❌ EasyOCR模型加载失败: {e}")
                raise
        else:
            print("  ⚠️  EasyOCR不可用，使用模拟模式")
            self.ocr_reader = None
        
        # 加载关键词映射器
        try:
            print("  加载OCR关键词映射器...")
            self.keyword_mapper = OCRKeywordMapper()
            
            # 尝试加载配置文件
            config_path = Path("config/ocr_categories.json")
            if config_path.exists():
                self.keyword_mapper.load_mapping_config(str(config_path))
                print("  ✅ 关键词映射配置加载成功")
            else:
                print("  ⚠️  使用默认关键词映射配置")
                
        except Exception as e:
            print(f"  ❌ 关键词映射器加载失败: {e}")
            raise
        
        loading_time = time.time() - start_time
        print(f"✅ 增强OCR分类器加载完成，耗时: {loading_time:.3f}s")
        
    def enhance_image_for_ocr(self, image: np.ndarray) -> np.ndarray:
        """图像增强预处理，提升OCR识别效果"""
        start_time = time.time()
        
        try:
            # 1. 对比度和亮度调整
            enhanced = cv2.convertScaleAbs(
                image, 
                alpha=self.preprocess_config['contrast_alpha'], 
                beta=self.preprocess_config['brightness_beta']
            )
            
            # 2. 高斯模糊去噪
            blurred = cv2.GaussianBlur(
                enhanced, 
                self.preprocess_config['gaussian_blur_ksize'], 
                0
            )
            
            # 3. 转换为灰度图
            if len(blurred.shape) == 3:
                gray = cv2.cvtColor(blurred, cv2.COLOR_BGR2GRAY)
            else:
                gray = blurred
            
            # 4. 自适应阈值处理
            adaptive_thresh = cv2.adaptiveThreshold(
                gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                cv2.THRESH_BINARY, 11, 2
            )
            
            # 5. 形态学操作去除噪声
            kernel = np.ones(self.preprocess_config['morphology_kernel_size'], np.uint8)
            cleaned = cv2.morphologyEx(adaptive_thresh, cv2.MORPH_CLOSE, kernel)
            
            # 6. 转换回BGR格式（EasyOCR需要）
            if len(cleaned.shape) == 2:
                result = cv2.cvtColor(cleaned, cv2.COLOR_GRAY2BGR)
            else:
                result = cleaned
                
        except Exception as e:
            logger.warning(f"图像预处理失败，使用原图: {e}")
            result = image
        
        preprocessing_time = time.time() - start_time
        self.performance_stats['preprocessing_times'].append(preprocessing_time)
        
        return result
    
    def extract_roi_for_ocr(self, image: np.ndarray, bbox: List[float], 
                           expand_ratio: float = 0.1) -> Optional[np.ndarray]:
        """提取并优化OCR识别的ROI区域"""
        try:
            h, w = image.shape[:2]
            x1, y1, x2, y2 = map(int, bbox)
            
            # 扩展边界框
            expand_w = int((x2 - x1) * expand_ratio)
            expand_h = int((y2 - y1) * expand_ratio)
            
            x1 = max(0, x1 - expand_w)
            y1 = max(0, y1 - expand_h)
            x2 = min(w, x2 + expand_w)
            y2 = min(h, y2 + expand_h)
            
            # 提取ROI
            roi = image[y1:y2, x1:x2]
            
            # 检查ROI大小
            if roi.size < self.preprocess_config['min_roi_area']:
                return None
                
            return roi
            
        except Exception as e:
            logger.warning(f"ROI提取失败: {e}")
            return None
    
    def perform_ocr(self, image: np.ndarray) -> List[Tuple[str, float]]:
        """执行OCR识别，返回文本和置信度列表"""
        if not EASYOCR_AVAILABLE or self.ocr_reader is None:
            # 模拟模式：返回空结果
            logger.warning("OCR模型未加载或不可用，返回空结果")
            return []

        start_time = time.time()

        try:
            # 执行OCR识别
            ocr_results = self.ocr_reader.readtext(
                image,
                detail=1,
                paragraph=False,
                width_ths=0.7,
                height_ths=0.7
            )

            # 提取文本和置信度
            texts_with_confidence = []
            for (bbox_coords, text, confidence) in ocr_results:
                if confidence >= self.ocr_config['confidence_threshold']:
                    # 清理文本
                    cleaned_text = self.clean_ocr_text(text)
                    if cleaned_text:
                        texts_with_confidence.append((cleaned_text, confidence))

        except Exception as e:
            logger.error(f"OCR识别失败: {e}")
            texts_with_confidence = []

        ocr_time = time.time() - start_time
        self.performance_stats['ocr_times'].append(ocr_time)

        return texts_with_confidence
    
    def clean_ocr_text(self, text: str) -> str:
        """清理OCR识别的文本"""
        if not text:
            return ""
        
        # 去除多余空格和换行
        cleaned = text.strip().replace('\n', ' ').replace('\r', ' ')
        cleaned = ' '.join(cleaned.split())
        
        # 过滤过短的文本
        if len(cleaned) < 2:
            return ""
        
        return cleaned
    
    def classify_unknown_object_enhanced(self, texts_with_confidence: List[Tuple[str, float]]) -> Tuple[Optional[str], float, Dict[str, Any]]:
        """增强的未知物品分类"""
        if self.keyword_mapper is None:
            logger.error("关键词映射器未加载")
            return None, 0.0, {}
        
        start_time = time.time()
        self.performance_stats['total_classifications'] += 1
        
        if not texts_with_confidence:
            return None, 0.0, {'reason': 'no_text_detected'}
        
        # 合并所有文本
        all_texts = [text for text, _ in texts_with_confidence]
        combined_text = ' '.join(all_texts)
        
        # 使用关键词映射器进行分类
        category, confidence = self.keyword_mapper.classify_text(combined_text)
        
        # 构建详细信息
        classification_details = {
            'input_texts': all_texts,
            'combined_text': combined_text,
            'ocr_confidences': [conf for _, conf in texts_with_confidence],
            'avg_ocr_confidence': np.mean([conf for _, conf in texts_with_confidence]),
            'classification_method': 'enhanced_keyword_mapping'
        }
        
        if category:
            self.performance_stats['successful_classifications'] += 1
            category_info = self.keyword_mapper.get_category_info(category)
            classification_details['category_name'] = category_info.get('name', category)
        
        classification_time = time.time() - start_time
        self.performance_stats['classification_times'].append(classification_time)
        
        return category, confidence, classification_details
    
    def process_unknown_detection(self, image: np.ndarray, detection: Dict[str, Any]) -> Dict[str, Any]:
        """处理单个未知物品检测结果"""
        bbox = detection.get('bbox', [])
        if not bbox:
            return detection
        
        # 提取ROI
        roi = self.extract_roi_for_ocr(image, bbox)
        if roi is None:
            detection['ocr_text'] = ''
            detection['refined_category'] = 'Wxxx'
            detection['classification_confidence'] = 0.0
            return detection
        
        # 图像增强
        enhanced_roi = self.enhance_image_for_ocr(roi)
        
        # OCR识别
        texts_with_confidence = self.perform_ocr(enhanced_roi)
        
        # 分类
        category, confidence, details = self.classify_unknown_object_enhanced(texts_with_confidence)
        
        # 更新检测结果
        detection['ocr_text'] = ' '.join([text for text, _ in texts_with_confidence])
        detection['refined_category'] = category if category else 'Wxxx'
        detection['classification_confidence'] = confidence
        detection['classification_details'] = details
        
        return detection

    def process_multiple_unknown_detections(self, image: np.ndarray, unknown_detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量处理多个未知物品检测结果"""
        if not unknown_detections:
            return unknown_detections

        print(f"🔍 处理 {len(unknown_detections)} 个未知物品...")

        processed_detections = []
        for i, detection in enumerate(unknown_detections):
            try:
                processed_detection = self.process_unknown_detection(image, detection)
                processed_detections.append(processed_detection)

                # 打印处理结果
                category = processed_detection.get('refined_category', 'Wxxx')
                confidence = processed_detection.get('classification_confidence', 0.0)
                ocr_text = processed_detection.get('ocr_text', '')

                if category != 'Wxxx':
                    category_name = processed_detection.get('classification_details', {}).get('category_name', category)
                    print(f"  [{i+1}] ✅ {category} ({category_name}) - 置信度: {confidence:.3f} - 文本: '{ocr_text}'")
                else:
                    print(f"  [{i+1}] ⚠️  未分类 - 文本: '{ocr_text}'")

            except Exception as e:
                logger.error(f"处理第{i+1}个未知物品失败: {e}")
                processed_detections.append(detection)

        return processed_detections

    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能统计摘要"""
        stats = self.performance_stats

        avg_ocr_time = np.mean(stats['ocr_times']) if stats['ocr_times'] else 0
        avg_classification_time = np.mean(stats['classification_times']) if stats['classification_times'] else 0
        avg_preprocessing_time = np.mean(stats['preprocessing_times']) if stats['preprocessing_times'] else 0

        success_rate = (stats['successful_classifications'] / stats['total_classifications']
                       if stats['total_classifications'] > 0 else 0)

        return {
            'average_ocr_time': avg_ocr_time,
            'average_classification_time': avg_classification_time,
            'average_preprocessing_time': avg_preprocessing_time,
            'total_processing_time': avg_ocr_time + avg_classification_time + avg_preprocessing_time,
            'total_classifications': stats['total_classifications'],
            'successful_classifications': stats['successful_classifications'],
            'success_rate': success_rate,
            'total_ocr_calls': len(stats['ocr_times'])
        }

    def print_performance_summary(self):
        """打印性能统计摘要"""
        summary = self.get_performance_summary()

        print("\n" + "="*50)
        print("📊 增强OCR分类器性能统计")
        print("="*50)
        print(f"平均OCR时间: {summary['average_ocr_time']:.3f}s")
        print(f"平均分类时间: {summary['average_classification_time']:.3f}s")
        print(f"平均预处理时间: {summary['average_preprocessing_time']:.3f}s")
        print(f"总处理时间: {summary['total_processing_time']:.3f}s")
        print(f"分类成功率: {summary['success_rate']:.1%} ({summary['successful_classifications']}/{summary['total_classifications']})")
        print(f"OCR调用次数: {summary['total_ocr_calls']}")
        print("="*50)

    def validate_performance_requirements(self) -> bool:
        """验证是否满足性能要求"""
        summary = self.get_performance_summary()

        # 检查单次OCR处理时间是否控制在1秒内
        ocr_time_ok = summary['average_ocr_time'] <= 1.0

        # 检查分类准确率是否达到90%以上
        accuracy_ok = summary['success_rate'] >= 0.9

        print(f"\n🔍 性能要求验证:")
        print(f"  OCR处理时间 ≤ 1s: {'✅' if ocr_time_ok else '❌'} ({summary['average_ocr_time']:.3f}s)")
        print(f"  分类准确率 ≥ 90%: {'✅' if accuracy_ok else '❌'} ({summary['success_rate']:.1%})")

        return ocr_time_ok and accuracy_ok

def create_enhanced_ocr_classifier(languages=['ch_sim', 'en'], gpu=True) -> EnhancedOCRClassifier:
    """创建增强OCR分类器实例"""
    classifier = EnhancedOCRClassifier(languages=languages, gpu=gpu)
    try:
        classifier.load_models()
    except Exception as e:
        print(f"⚠️  模型加载失败，将使用有限功能模式: {e}")
    return classifier

def test_enhanced_ocr_classifier():
    """测试增强OCR分类器"""
    print("🧪 测试增强OCR分类器")

    try:
        # 创建分类器
        classifier = create_enhanced_ocr_classifier()

        # 模拟测试数据
        test_texts = [
            ("高等数学", 0.9),
            ("大学物理", 0.8),
            ("布洛芬胶囊", 0.95),
            ("xiaomi充电宝", 0.85),
            ("雅思写作", 0.9),
            ("毛泽东思想", 0.8),
            ("方便面", 0.9),
            ("墨水笔芯", 0.85)
        ]

        print("\n📝 测试分类功能:")
        for text, confidence in test_texts:
            category, class_confidence, details = classifier.classify_unknown_object_enhanced([(text, confidence)])

            if category:
                category_name = details.get('category_name', category)
                print(f"  '{text}' -> {category} ({category_name}) [置信度: {class_confidence:.3f}]")
            else:
                print(f"  '{text}' -> 未分类 [置信度: {class_confidence:.3f}]")

        # 打印性能统计
        classifier.print_performance_summary()

        # 验证性能要求
        performance_ok = classifier.validate_performance_requirements()

        print(f"\n{'✅ 测试通过' if performance_ok else '❌ 测试未通过'}")

        return performance_ok

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_enhanced_ocr_classifier()
    exit(0 if success else 1)
