#!/usr/bin/env python3
"""
增强版推理系统 - 集成OCR识别未知物品
结合YOLO检测和PaddleOCR文字识别，完整实现比赛要求
"""

import os
import time
import json
import cv2
import numpy as np
from pathlib import Path
from ultralytics import YOLO

# OCR相关导入
try:
    from paddleocr import PaddleOCR
    PADDLEOCR_AVAILABLE = True
except ImportError:
    print("⚠️  PaddleOCR未安装，将使用EasyOCR作为备选")
    PADDLEOCR_AVAILABLE = False

try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    print("⚠️  EasyOCR未安装")
    EASYOCR_AVAILABLE = False

class EnhancedInferenceWithOCR:
    def __init__(self, model_path="competition_2025_models/competition_2025_yolo11n/weights/best.pt"):
        self.model_path = model_path
        self.model = None
        self.ocr_engine = None
        
        # 比赛类别映射
        self.category_mapping = {
            0: 'CA001_衣架',
            1: 'CA002_牙刷', 
            2: 'CB001_果冻',
            3: 'CB002_长方形状饼干',
            4: 'CC001_罐装饮料',
            5: 'CC002_瓶装饮料',
            6: 'CD001_香蕉',
            7: 'CD002_橙子',
            8: 'Wxxx_未知物品'
        }
        
        # 未知物品分类规则 (基于比赛说明)
        self.unknown_categories = {
            '数学': ['数学', '高等数学', '微积分', '线性代数', '概率论', 'math'],
            '物理': ['物理', '力学', '电磁学', '光学', 'physics'],
            '化学': ['化学', '有机化学', '无机化学', 'chemistry'],
            '计算机': ['计算机', '编程', 'python', 'java', 'computer'],
            '英语': ['英语', 'english', '词汇', '语法'],
            '文学': ['文学', '小说', '诗歌', '散文', 'literature'],
            '历史': ['历史', 'history', '古代史', '近代史'],
            '地理': ['地理', 'geography', '地图', '地球'],
            '生物': ['生物', 'biology', '细胞', '遗传'],
            '经济': ['经济', 'economics', '金融', '管理']
        }
        
        # 性能统计
        self.stats = {
            'total_images': 0,
            'total_detections': 0,
            'unknown_items': 0,
            'ocr_success': 0,
            'inference_times': [],
            'ocr_times': [],
            'category_counts': {cat: 0 for cat in self.category_mapping.values()}
        }
    
    def load_model(self):
        """加载YOLO模型"""
        print(f"正在加载YOLO模型: {self.model_path}")
        
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型文件不存在: {self.model_path}")
        
        start_time = time.time()
        self.model = YOLO(self.model_path)
        loading_time = time.time() - start_time
        
        print(f"✅ YOLO模型加载完成，耗时: {loading_time:.3f}s")
        
        # 预热模型
        dummy_image = np.zeros((640, 640, 3), dtype=np.uint8)
        _ = self.model(dummy_image, verbose=False)
        print("✅ YOLO模型预热完成")
        
        return loading_time
    
    def load_ocr_engine(self):
        """加载OCR引擎"""
        print("正在加载OCR引擎...")
        start_time = time.time()
        
        if PADDLEOCR_AVAILABLE:
            try:
                # 使用PaddleOCR (推荐)
                self.ocr_engine = PaddleOCR(
                    use_angle_cls=True,  # 启用文字方向分类
                    lang='ch',          # 中文+英文
                    use_gpu=True,       # 使用GPU加速
                    show_log=False      # 不显示日志
                )
                ocr_type = "PaddleOCR"
                print("✅ 使用PaddleOCR引擎")
            except Exception as e:
                print(f"PaddleOCR加载失败: {e}")
                self.ocr_engine = None
        
        if self.ocr_engine is None and EASYOCR_AVAILABLE:
            try:
                # 备选方案：EasyOCR
                self.ocr_engine = easyocr.Reader(['ch_sim', 'en'], gpu=True)
                ocr_type = "EasyOCR"
                print("✅ 使用EasyOCR引擎")
            except Exception as e:
                print(f"EasyOCR加载失败: {e}")
                self.ocr_engine = None
        
        if self.ocr_engine is None:
            raise RuntimeError("无法加载任何OCR引擎，请安装PaddleOCR或EasyOCR")
        
        loading_time = time.time() - start_time
        print(f"✅ OCR引擎加载完成，耗时: {loading_time:.3f}s")
        
        return loading_time, ocr_type
    
    def extract_text_from_bbox(self, image, bbox):
        """从边界框中提取文字"""
        try:
            # 提取边界框区域
            x1, y1, x2, y2 = map(int, bbox)
            
            # 添加一些边距
            margin = 5
            h, w = image.shape[:2]
            x1 = max(0, x1 - margin)
            y1 = max(0, y1 - margin)
            x2 = min(w, x2 + margin)
            y2 = min(h, y2 + margin)
            
            roi = image[y1:y2, x1:x2]
            
            if roi.size == 0:
                return ""
            
            # 图像预处理
            roi = self.preprocess_for_ocr(roi)
            
            start_time = time.time()
            
            if isinstance(self.ocr_engine, PaddleOCR):
                # PaddleOCR
                result = self.ocr_engine.ocr(roi, cls=True)
                texts = []
                if result and result[0]:
                    for line in result[0]:
                        if line and len(line) > 1:
                            texts.append(line[1][0])
                text = ' '.join(texts)
            else:
                # EasyOCR
                result = self.ocr_engine.readtext(roi)
                texts = [item[1] for item in result if item[2] > 0.5]  # 置信度 > 0.5
                text = ' '.join(texts)
            
            ocr_time = time.time() - start_time
            self.stats['ocr_times'].append(ocr_time)
            
            return text.strip()
            
        except Exception as e:
            print(f"OCR识别失败: {e}")
            return ""
    
    def preprocess_for_ocr(self, image):
        """OCR预处理"""
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # 增强对比度
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)
        
        # 去噪
        denoised = cv2.medianBlur(enhanced, 3)
        
        # 二值化
        _, binary = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        return binary
    
    def classify_unknown_item(self, text):
        """根据OCR文字对未知物品进行分类"""
        if not text:
            return "Wxxx_未分类"
        
        text_lower = text.lower()
        
        # 遍历分类规则
        for category, keywords in self.unknown_categories.items():
            for keyword in keywords:
                if keyword.lower() in text_lower:
                    return f"W{category[:3]}_{category}"
        
        # 如果没有匹配到，返回原始文字
        return f"Wxxx_{text[:10]}"  # 截取前10个字符
    
    def run_enhanced_inference(self, image_path, conf_threshold=0.25):
        """运行增强推理（YOLO + OCR）"""
        # 读取图像
        image = cv2.imread(str(image_path))
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        start_time = time.time()
        
        # YOLO推理
        results = self.model(image, conf=conf_threshold, verbose=False)
        
        inference_time = time.time() - start_time
        self.stats['inference_times'].append(inference_time)
        
        # 解析检测结果
        detections = []
        if results[0].boxes is not None:
            boxes = results[0].boxes.xyxy.cpu().numpy()
            confidences = results[0].boxes.conf.cpu().numpy()
            classes = results[0].boxes.cls.cpu().numpy().astype(int)
            
            for box, conf, cls in zip(boxes, confidences, classes):
                category_name = self.category_mapping.get(cls, 'Unknown')
                
                detection = {
                    'category_id': int(cls),
                    'category_name': category_name,
                    'confidence': float(conf),
                    'bbox': box.tolist(),
                    'ocr_text': '',
                    'final_category': category_name
                }
                
                # 如果是未知物品，进行OCR识别
                if cls == 8:  # Wxxx_未知物品
                    self.stats['unknown_items'] += 1
                    ocr_text = self.extract_text_from_bbox(image, box)
                    
                    if ocr_text:
                        self.stats['ocr_success'] += 1
                        detection['ocr_text'] = ocr_text
                        detection['final_category'] = self.classify_unknown_item(ocr_text)
                        print(f"  🔍 OCR识别: '{ocr_text}' -> {detection['final_category']}")
                
                detections.append(detection)
                
                # 统计类别
                self.stats['category_counts'][detection['final_category']] = \
                    self.stats['category_counts'].get(detection['final_category'], 0) + 1
        
        self.stats['total_detections'] += len(detections)
        
        return {
            'image_path': str(image_path),
            'inference_time': inference_time,
            'detections': detections,
            'detection_count': len(detections)
        }
    
    def run_batch_inference(self, test_dir="competition_2025_dataset/images/test", max_images=10):
        """批量增强推理测试"""
        test_path = Path(test_dir)
        if not test_path.exists():
            raise FileNotFoundError(f"测试目录不存在: {test_dir}")
        
        # 获取测试图像
        image_files = list(test_path.glob("*.jpg"))[:max_images]
        
        if not image_files:
            raise FileNotFoundError(f"在 {test_dir} 中未找到图像文件")
        
        print(f"\n🚀 开始增强推理测试 (YOLO + OCR)")
        print(f"测试图像数量: {len(image_files)}")
        print("=" * 60)
        
        all_results = []
        
        for i, img_path in enumerate(image_files, 1):
            try:
                result = self.run_enhanced_inference(img_path)
                all_results.append(result)
                
                print(f"[{i:2d}/{len(image_files)}] {img_path.name}")
                print(f"  检测数量: {result['detection_count']}")
                print(f"  推理时间: {result['inference_time']:.3f}s")
                
                if result['detections']:
                    print("  检测结果:")
                    for det in result['detections']:
                        if det['ocr_text']:
                            print(f"    - {det['final_category']}: {det['confidence']:.3f} [OCR: '{det['ocr_text']}']")
                        else:
                            print(f"    - {det['category_name']}: {det['confidence']:.3f}")
                else:
                    print("  未检测到物体")
                print("-" * 40)
                
            except Exception as e:
                print(f"❌ 处理图像失败 {img_path.name}: {e}")
        
        self.stats['total_images'] = len(all_results)
        
        return all_results
    
    def generate_enhanced_report(self, results):
        """生成增强版报告"""
        if not results:
            print("❌ 没有推理结果")
            return
        
        # 计算统计信息
        avg_inference_time = np.mean(self.stats['inference_times'])
        avg_ocr_time = np.mean(self.stats['ocr_times']) if self.stats['ocr_times'] else 0
        total_detections = self.stats['total_detections']
        unknown_items = self.stats['unknown_items']
        ocr_success_rate = (self.stats['ocr_success'] / unknown_items * 100) if unknown_items > 0 else 0
        
        print("\n" + "=" * 60)
        print("📊 增强推理测试汇总报告 (YOLO + OCR)")
        print("=" * 60)
        
        print(f"🖼️  测试图像数量: {len(results)}")
        print(f"🎯 总检测数量: {total_detections}")
        print(f"🔍 未知物品数量: {unknown_items}")
        print(f"📝 OCR成功数量: {self.stats['ocr_success']}")
        print(f"📈 OCR成功率: {ocr_success_rate:.1f}%")
        print(f"⏱️  平均推理时间: {avg_inference_time:.3f}s")
        print(f"🔤 平均OCR时间: {avg_ocr_time:.3f}s")
        print(f"🚀 总体速度: {1/(avg_inference_time + avg_ocr_time):.1f} FPS")
        
        print(f"\n📋 最终类别统计:")
        for category, count in sorted(self.stats['category_counts'].items()):
            if count > 0:
                percentage = (count / total_detections) * 100 if total_detections > 0 else 0
                print(f"  {category}: {count} ({percentage:.1f}%)")
        
        # 保存详细结果
        report = {
            'summary': {
                'total_images': len(results),
                'total_detections': total_detections,
                'unknown_items': unknown_items,
                'ocr_success': self.stats['ocr_success'],
                'ocr_success_rate': float(ocr_success_rate),
                'avg_inference_time': float(avg_inference_time),
                'avg_ocr_time': float(avg_ocr_time),
                'total_fps': float(1/(avg_inference_time + avg_ocr_time))
            },
            'category_statistics': self.stats['category_counts'],
            'detailed_results': results
        }
        
        report_file = "enhanced_inference_results.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 详细结果已保存: {report_file}")
        
        return report

def install_ocr_dependencies():
    """安装OCR依赖"""
    print("🔧 检查并安装OCR依赖...")
    
    try:
        import subprocess
        import sys
        
        # 安装PaddleOCR
        if not PADDLEOCR_AVAILABLE:
            print("正在安装PaddleOCR...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "paddlepaddle", "paddleocr"])
        
        # 安装EasyOCR作为备选
        if not EASYOCR_AVAILABLE:
            print("正在安装EasyOCR...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "easyocr"])
        
        print("✅ OCR依赖安装完成")
        
    except Exception as e:
        print(f"❌ OCR依赖安装失败: {e}")
        print("请手动安装: pip install paddlepaddle paddleocr easyocr")

def main():
    print("=== 2025年比赛增强推理系统 (YOLO + OCR) ===")
    
    # 检查模型是否存在
    model_path = "competition_2025_models/competition_2025_yolo11n/weights/best.pt"
    if not Path(model_path).exists():
        print(f"❌ 模型文件不存在: {model_path}")
        print("请先运行训练脚本生成模型")
        return
    
    # 检查OCR依赖
    if not PADDLEOCR_AVAILABLE and not EASYOCR_AVAILABLE:
        print("❌ 未安装OCR引擎")
        install_ocr_dependencies()
        return
    
    try:
        # 创建增强推理系统
        system = EnhancedInferenceWithOCR(model_path)
        
        # 加载模型
        yolo_loading_time = system.load_model()
        ocr_loading_time, ocr_type = system.load_ocr_engine()
        
        print(f"\n📊 系统加载完成:")
        print(f"  YOLO加载时间: {yolo_loading_time:.3f}s")
        print(f"  OCR加载时间: {ocr_loading_time:.3f}s")
        print(f"  OCR引擎: {ocr_type}")
        
        # 运行增强推理测试
        results = system.run_batch_inference(max_images=10)  # 测试10张图像
        
        # 生成增强报告
        report = system.generate_enhanced_report(results)
        
        print(f"\n🎉 增强推理测试完成!")
        print(f"📊 测试了 {len(results)} 张图像")
        print(f"🎯 检测到 {system.stats['total_detections']} 个物体")
        print(f"🔍 识别了 {system.stats['unknown_items']} 个未知物品")
        print(f"📝 OCR成功 {system.stats['ocr_success']} 次")
        
    except Exception as e:
        print(f"❌ 增强推理测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
