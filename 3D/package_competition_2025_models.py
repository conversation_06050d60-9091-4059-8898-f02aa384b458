#!/usr/bin/env python3
"""
2025年比赛模型打包脚本
将所有相关的源代码、训练结果、文档整理打包
"""

import os
import shutil
import tarfile
import json
from pathlib import Path
from datetime import datetime

class Competition2025Packager:
    def __init__(self, base_dir=".", package_name="competition_2025_complete_package"):
        self.base_dir = Path(base_dir)
        self.package_name = package_name
        self.package_dir = self.base_dir / package_name
        
        # 需要打包的源代码文件
        self.source_files = [
            "competition_2025_training.py",
            "competition_2025_dataset_preparation.py", 
            "competition_2025_inference_system.py",
            "optimize_confidence_threshold.py",
            "comprehensive_model_evaluation.py",
            "test_optimized_models.py",
            "ultimate_competition_system.py",
            "adaptive_inference_system.py",
            "run_complete_pipeline.py"
        ]
        
        # 需要打包的目录
        self.source_dirs = [
            "competition_2025_models",
            "utils",
            "our_methods"
        ]
        
        # 文档文件
        self.doc_files = [
            "README.md",
            "赛事说明"
        ]
    
    def create_package_structure(self):
        """创建打包目录结构"""
        print("创建打包目录结构...")
        
        # 清理并创建主目录
        if self.package_dir.exists():
            shutil.rmtree(self.package_dir)
        self.package_dir.mkdir(parents=True)
        
        # 创建子目录
        subdirs = [
            "源代码",
            "训练结果", 
            "比赛文档",
            "工具脚本",
            "配置文件"
        ]
        
        for subdir in subdirs:
            (self.package_dir / subdir).mkdir()
        
        print(f"✅ 打包目录创建完成: {self.package_dir}")
    
    def copy_source_files(self):
        """复制源代码文件"""
        print("复制源代码文件...")
        
        source_dir = self.package_dir / "源代码"
        copied_count = 0
        
        for file_name in self.source_files:
            source_file = self.base_dir / file_name
            if source_file.exists():
                shutil.copy2(source_file, source_dir / file_name)
                print(f"  ✅ {file_name}")
                copied_count += 1
            else:
                print(f"  ⚠️  文件不存在: {file_name}")
        
        print(f"✅ 复制了 {copied_count} 个源代码文件")
    
    def copy_directories(self):
        """复制重要目录"""
        print("复制重要目录...")
        
        for dir_name in self.source_dirs:
            source_dir = self.base_dir / dir_name
            if source_dir.exists():
                if dir_name == "competition_2025_models":
                    target_dir = self.package_dir / "训练结果" / dir_name
                elif dir_name in ["utils", "our_methods"]:
                    target_dir = self.package_dir / "工具脚本" / dir_name
                else:
                    target_dir = self.package_dir / "源代码" / dir_name
                
                shutil.copytree(source_dir, target_dir)
                print(f"  ✅ {dir_name} -> {target_dir.relative_to(self.package_dir)}")
            else:
                print(f"  ⚠️  目录不存在: {dir_name}")
    
    def copy_documentation(self):
        """复制文档文件"""
        print("复制文档文件...")
        
        doc_dir = self.package_dir / "比赛文档"
        
        for doc_name in self.doc_files:
            source_doc = self.base_dir / doc_name
            if source_doc.exists():
                if source_doc.is_file():
                    shutil.copy2(source_doc, doc_dir / doc_name)
                else:
                    shutil.copytree(source_doc, doc_dir / doc_name)
                print(f"  ✅ {doc_name}")
            else:
                print(f"  ⚠️  文档不存在: {doc_name}")
    
    def create_readme(self):
        """创建详细的README文件"""
        print("创建项目说明文档...")
        
        readme_content = f"""# 2025年中国机器人大赛 3D识别项目完整包

## 📋 项目概述

这是2025年中国机器人大赛暨RoboCup机器人世界杯中国赛"机器人先进视觉赛项-3D识别项目"的完整代码包。

**比赛背景:**
- 赛项：机器人先进视觉
- 项目：3D识别  
- 硬件平台：香橙派 AI pro 8T (16GB内存)
- 相机：奥比中光 Astra Pro Plus RGBD相机
- 识别目标：8个基本类别 + 未知物品

## 📁 目录结构

```
{self.package_name}/
├── README.md                    # 本文件
├── 使用指南.md                  # 详细使用说明
├── 源代码/                      # 核心源代码
│   ├── competition_2025_training.py              # 🔥 主训练脚本
│   ├── competition_2025_dataset_preparation.py   # 数据集准备
│   ├── competition_2025_inference_system.py      # 推理系统
│   ├── optimize_confidence_threshold.py          # 置信度优化
│   ├── comprehensive_model_evaluation.py         # 模型评估
│   ├── ultimate_competition_system.py            # 终极比赛系统
│   └── ...                                      # 其他工具脚本
├── 训练结果/                    # 训练产出
│   └── competition_2025_models/                 # 🎯 训练好的模型
│       └── competition_2025_yolo11n/           # YOLOv11n模型
│           ├── weights/                         # 模型权重
│           │   ├── best.pt                     # 🏆 最佳模型
│           │   ├── last.pt                     # 最后一轮模型
│           │   └── epoch*.pt                   # 各轮次模型
│           ├── args.yaml                       # 训练配置
│           ├── results.csv                     # 训练日志
│           ├── confusion_matrix.png            # 混淆矩阵
│           └── ...                            # 其他评估图表
├── 工具脚本/                    # 辅助工具
│   ├── utils/                                  # 通用工具
│   └── our_methods/                           # 自研方法
├── 比赛文档/                    # 比赛相关文档
│   ├── README.md                              # 项目说明
│   └── 赛事说明                               # 比赛规则文档
└── 配置文件/                    # 配置文件
```

## 🚀 快速开始

### 1. 环境准备
```bash
# 创建conda环境
conda create -n competition2025 python=3.9
conda activate competition2025

# 安装依赖
pip install ultralytics opencv-python easyocr numpy torch torchvision
```

### 2. 使用训练好的模型
```python
from ultralytics import YOLO

# 加载最佳模型
model = YOLO('训练结果/competition_2025_models/competition_2025_yolo11n/weights/best.pt')

# 进行推理
results = model('your_image.jpg')
```

### 3. 运行完整推理系统
```bash
cd 源代码/
python competition_2025_inference_system.py
```

## 📊 模型性能

根据 `训练结果/training_results_yolo11n.json`:

- **mAP50**: 92.42%
- **mAP50-95**: 67.97%  
- **精确率**: 91.25%
- **召回率**: 87.61%
- **模型加载时间**: 0.08秒 ✅
- **推理时间**: 0.033秒 ✅
- **训练时间**: 0.31小时

✅ **符合比赛要求**: 模型加载时间 < 30秒，推理时间 < 5秒

## 🎯 比赛类别映射

| 编号 | 类别名称 | 描述 |
|------|----------|------|
| CA001 | 衣架 | 日用品类 |
| CA002 | 牙刷 | 日用品类 |
| CB001 | 果冻 | 副食品类 |
| CB002 | 长方形状饼干 | 副食品类 |
| CC001 | 罐装饮料 | 饮料类 |
| CC002 | 瓶装饮料 | 饮料类 |
| CD001 | 香蕉 | 水果类 |
| CD002 | 橙子 | 水果类 |
| Wxxx | 未知物品 | 需OCR识别 |

## 🔧 核心文件说明

### 训练相关
- `competition_2025_training.py`: **主训练脚本** - 生成所有模型文件
- `competition_2025_dataset_preparation.py`: 数据集准备和格式转换

### 推理相关  
- `competition_2025_inference_system.py`: **完整推理系统** - 包含RGB-D融合和OCR
- `ultimate_competition_system.py`: 终极比赛系统 - 最优化版本

### 评估工具
- `comprehensive_model_evaluation.py`: 全面模型评估
- `optimize_confidence_threshold.py`: 置信度阈值优化

## 📈 训练结果文件详解

`训练结果/competition_2025_models/` 目录包含:

1. **模型权重** (`weights/`):
   - `best.pt`: 验证集上表现最佳的模型 🏆
   - `last.pt`: 训练结束时的模型
   - `epoch*.pt`: 各个训练轮次的模型

2. **训练配置** (`args.yaml`):
   - 完整的训练参数配置
   - 可用于复现训练过程

3. **评估结果**:
   - `results.csv`: 详细训练日志
   - `confusion_matrix.png`: 混淆矩阵
   - `*_curve.png`: 各种性能曲线

4. **性能统计** (`training_results_yolo11n.json`):
   - 模型性能指标
   - 比赛合规性检查结果

## 🏆 比赛部署建议

1. **使用最佳模型**: `weights/best.pt`
2. **推理系统**: 使用 `ultimate_competition_system.py`
3. **置信度阈值**: 根据 `optimize_confidence_threshold.py` 结果调整
4. **性能监控**: 确保加载时间 < 30s，推理时间 < 5s

## 📞 技术支持

如有问题，请参考:
1. `比赛文档/赛事说明` - 完整比赛规则
2. `使用指南.md` - 详细使用说明
3. 源代码中的注释和文档字符串

---

**打包时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**项目版本**: Competition 2025 v1.0
"""
        
        readme_file = self.package_dir / "README.md"
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"✅ README文件创建完成")
    
    def create_usage_guide(self):
        """创建使用指南"""
        print("创建使用指南...")
        
        guide_content = """# 使用指南

## 🔥 核心文件说明

### competition_2025_training.py
**这是生成 competition_2025_models 目录的主要脚本！**

功能:
- 训练YOLOv11n模型
- 自动创建 competition_2025_models 目录
- 生成所有权重文件和配置文件
- 保存训练结果和性能统计

运行方法:
```bash
python competition_2025_training.py
```

### 生成的文件结构
运行训练脚本后会生成:
```
competition_2025_models/
├── competition_2025_yolo11n/
│   ├── weights/
│   │   ├── best.pt      # 最佳模型
│   │   ├── last.pt      # 最后模型  
│   │   └── epoch*.pt    # 各轮次模型
│   ├── args.yaml        # 训练配置
│   ├── results.csv      # 训练日志
│   └── *.png           # 评估图表
└── training_results_yolo11n.json  # 性能统计
```

## 🚀 快速使用

### 1. 直接使用训练好的模型
```python
from ultralytics import YOLO

# 加载最佳模型
model = YOLO('训练结果/competition_2025_models/competition_2025_yolo11n/weights/best.pt')

# 推理
results = model('test_image.jpg')
```

### 2. 运行完整推理系统
```bash
python 源代码/competition_2025_inference_system.py
```

### 3. 重新训练模型
```bash
# 确保数据集存在
python 源代码/competition_2025_dataset_preparation.py

# 开始训练
python 源代码/competition_2025_training.py
```

## 📊 文件来源追踪

所有 `competition_2025_models` 目录下的文件都是由以下脚本生成:

1. **主生成脚本**: `competition_2025_training.py`
   - 创建目录结构
   - 训练模型并保存权重
   - 生成配置文件和统计结果

2. **数据准备**: `competition_2025_dataset_preparation.py`
   - 准备训练数据集
   - 格式转换和类别映射

3. **评估工具**: `comprehensive_model_evaluation.py`
   - 生成详细评估报告
   - 创建可视化图表

## 🎯 比赛部署流程

1. **环境准备**
2. **模型部署** - 使用 `weights/best.pt`
3. **推理系统** - 运行 `ultimate_competition_system.py`
4. **性能验证** - 确保符合比赛要求

---
更多详细信息请参考源代码注释和比赛文档。
"""
        
        guide_file = self.package_dir / "使用指南.md"
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        print(f"✅ 使用指南创建完成")
    
    def create_file_manifest(self):
        """创建文件清单"""
        print("创建文件清单...")
        
        manifest = {
            "package_info": {
                "name": self.package_name,
                "created_time": datetime.now().isoformat(),
                "description": "2025年中国机器人大赛3D识别项目完整包"
            },
            "file_sources": {
                "competition_2025_models": {
                    "generated_by": "competition_2025_training.py",
                    "description": "训练脚本生成的完整模型目录",
                    "key_files": [
                        "weights/best.pt - 最佳模型权重",
                        "args.yaml - 训练配置",
                        "training_results_yolo11n.json - 性能统计"
                    ]
                },
                "source_files": {
                    "competition_2025_training.py": "主训练脚本 - 生成所有模型文件",
                    "competition_2025_inference_system.py": "推理系统 - 使用训练好的模型",
                    "competition_2025_dataset_preparation.py": "数据集准备脚本"
                }
            }
        }
        
        manifest_file = self.package_dir / "文件清单.json"
        with open(manifest_file, 'w', encoding='utf-8') as f:
            json.dump(manifest, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 文件清单创建完成")
    
    def create_archive(self):
        """创建压缩包"""
        print("创建压缩包...")
        
        archive_name = f"{self.package_name}.tar.gz"
        archive_path = self.base_dir / archive_name
        
        with tarfile.open(archive_path, "w:gz") as tar:
            tar.add(self.package_dir, arcname=self.package_name)
        
        # 计算大小
        size_mb = archive_path.stat().st_size / (1024 * 1024)
        
        print(f"✅ 压缩包创建完成: {archive_name}")
        print(f"   大小: {size_mb:.1f} MB")
        print(f"   路径: {archive_path}")
        
        return archive_path
    
    def run_packaging(self):
        """执行完整打包流程"""
        print(f"🚀 开始打包 2025年比赛项目...")
        print(f"目标目录: {self.package_dir}")
        print("=" * 60)
        
        try:
            # 1. 创建目录结构
            self.create_package_structure()
            
            # 2. 复制文件
            self.copy_source_files()
            self.copy_directories() 
            self.copy_documentation()
            
            # 3. 创建文档
            self.create_readme()
            self.create_usage_guide()
            self.create_file_manifest()
            
            # 4. 创建压缩包
            archive_path = self.create_archive()
            
            print("=" * 60)
            print("🎉 打包完成!")
            print(f"📦 压缩包: {archive_path}")
            print(f"📁 目录: {self.package_dir}")
            print("\n📋 包含内容:")
            print("  ✅ 完整源代码")
            print("  ✅ 训练好的模型 (competition_2025_models)")
            print("  ✅ 比赛文档和规则")
            print("  ✅ 详细使用说明")
            print("  ✅ 文件来源追踪")
            
            return archive_path
            
        except Exception as e:
            print(f"❌ 打包失败: {e}")
            return None

def main():
    print("=== 2025年比赛项目打包工具 ===")
    
    packager = Competition2025Packager()
    result = packager.run_packaging()
    
    if result:
        print(f"\n🎯 打包成功! 文件位置: {result}")
        print("\n💡 使用说明:")
        print("1. 解压缩包到目标位置")
        print("2. 阅读 README.md 了解项目结构") 
        print("3. 查看 使用指南.md 获取详细使用方法")
        print("4. 直接使用 训练结果/competition_2025_models 中的模型")
    else:
        print("\n❌ 打包失败!")

if __name__ == "__main__":
    main()
