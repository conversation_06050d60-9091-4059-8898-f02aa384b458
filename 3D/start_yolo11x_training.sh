#!/bin/bash

# YOLO11x训练启动脚本
# 设置环境变量和启动训练

echo "=== YOLO11x训练启动脚本 ==="
echo "时间: $(date)"
echo "工作目录: $(pwd)"

# 检查Python环境
echo "Python版本: $(python --version)"
echo "PyTorch版本: $(python -c 'import torch; print(torch.__version__)')"

# 检查GPU
echo "GPU信息:"
nvidia-smi --query-gpu=name,memory.total,memory.free --format=csv,noheader,nounits

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0
export PYTHONPATH=/home/<USER>/claude/SpatialVLA/3D:$PYTHONPATH

# 创建日志目录
mkdir -p /home/<USER>/claude/SpatialVLA/3D/yolo11x/logs

# 启动训练（后台运行并记录日志）
echo "开始训练YOLO11x..."
cd /home/<USER>/claude/SpatialVLA/3D

# 运行训练脚本
python train_yolo11x.py 2>&1 | tee /home/<USER>/claude/SpatialVLA/3D/yolo11x/logs/training_$(date +%Y%m%d_%H%M%S).log

echo "训练脚本执行完成"
echo "日志文件保存在: /home/<USER>/claude/SpatialVLA/3D/yolo11x/logs/"
