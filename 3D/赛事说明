中国机器人大赛暨RoboCup机器人世界杯中国赛 
2025 年度赛事规则 
(选拔赛) 
赛项：机器人先进视觉 
项目：3D识别 
机器人先进视觉赛项技术委员会 
2025 年 1 月 
中国机器人大赛暨RoboCup机器人世界杯中国赛机器人先进视觉赛项3D识别项目竞赛规则 
 
 
目录 
一、项目背景 .................................................................................................................................... 3 
二、技术委员会与组织委员会 ................................................................................................... 3 
2.1技术委员会 ........................................................................................................................ 3 
2.2组织委员会 ........................................................................................................................ 3 
三、资格认证要求 ........................................................................................................................... 4 
3.1参赛队伍要求 ................................................................................................................... 4 
3.2参赛软硬件要求............................................................................................................... 4 
3.3技术认证文档要求 .......................................................................................................... 5 
四、参赛人员要求 ........................................................................................................................... 5 
五、技术与竞赛组织讨论群 ........................................................................................................ 5 
六、比赛场地及器材 ...................................................................................................................... 6 
6.1比赛场地说明 ................................................................................................................... 6 
6.2 比赛器材说明 .................................................................................................................. 6 
七、赛事规则要求与评分标准 ................................................................................................. 11 
7.1竞赛过程 ........................................................................................................................... 11 
7.2竞赛注意事项 ................................................................................................................. 12 
7.3 评分标准 .......................................................................................................................... 12 
7.4 评分表 .............................................................................................................................. 12 
八、赛程赛制 .................................................................................................................................. 15 
8.1调试时间 ........................................................................................................................... 15 
8.2比赛轮次 ........................................................................................................................... 15 
附件：参赛队伍资格认证模板 ................................................................................................. 16 
 
  
中国机器人大赛暨RoboCup机器人世界杯中国赛机器人先进视觉赛项3D识别项目竞赛规则 
一、项目背景 
机器人视觉系统相当于机器人的“眼”，赋予了机器人对环境的感知和理解
能力。通过各种图像/点云传感器获取环境信息，能够实现机器人定位、目标识
别、物体操控和人机交互等。不仅提高了机器人在制造、物流和服务等领域的工
作效率，还在一些紧急救援和危险环境作业中发挥关键作用。机器人视觉系统的
进步推动着自动化技术的发展，也为未来智能机器人的广泛应用奠定基础。 
机器人先进视觉赛项旨在激发大学生科学研究的热情，提高技术攻关能力，
进而研制出低成本、高性能的智能视觉处理模块。当前，本赛项采用指定型号的
标准硬件平台（RGBD相机+嵌入式系统板），以考察参赛队相机参数优化调整、
标定、软件算法为主。今后，也会设立自制视觉处理模块的比赛子项目，进一步
考察视觉硬件的研制能力。 
3D 识别项目是机器人先进视觉赛项的子项目之一，主要任务是物体的识别。
鼓励参赛队，综合应用传统算法和人工智能算法，充分发挥两类算法的优势完成
指定任务。本项目主要考察在静态场景和动态场景下的物体识别能力，包括识别
的准确性和效率。 
较去年的比赛，2025年比赛主要有以下改变： 
1. 计算平台修改为香橙派 AI pro 8T算力16G内存； 
2. 考察赛队模型大小，将模型加载时间计入总的识别分； 
3. 未知物品增加文本识别的考察，通过识别物品表面文本区分小类，且赛前
公布小类进行适配，比赛现场不提供提前训练； 
4. 针对3D识别，国赛将物体的桌台编号也计入分数，进一步避免队伍盲猜。 
二、技术委员会与组织委员会 
2.1 技术委员会 
负责人：朱笑笑，助理研究员/博士，15921155665、<EMAIL> 
成  员：王景川，上海交通大学 
高大志，东北大学 
2.2 组织委员会 
负责人：朱笑笑，助理研究员/博士，15921155665、<EMAIL> 
成  员：罗扉，洛阳理工学院  
中国机器人大赛暨RoboCup机器人世界杯中国赛机器人先进视觉赛项3D识别项目竞赛规则 
三、资格认证要求 
3.1 参赛队伍要求 
每一学校参赛队伍限制参考大赛统一规定，参赛队员应为全日制在校学生。 
3.2 参赛软硬件要求 
(1) 硬件要求： 
⚫ 比赛使用组织委员会提供的3D摄像头，型号为奥比中光（ORBBEC）的
Astra Pro Plus（0.6-8m）摄像头。 
⚫ 计算平台使用香橙派OrangePi AIpro（算力8T，内存16G）。比赛时各
参赛队使用各自的计算平台。 
(2) 软件要求： 
⚫ 操作系统：不限； 
⚫ 软件环境：视觉识别软件开发运行环境不限； 
⚫ 软件不允许在裁判开始比赛前提前进行识别。在开始运行程序前，裁判
将遮住相机镜头； 
⚫ 通讯功能：能通过网络实现与裁判盒的数据通讯，包括向裁判盒发送开
始识别信号，发送识别结束信号，发送识别结果文本等等。具体的通讯
协议将在后续与裁判盒软件一起发布；（新版本发布前可以参考QQ群
中往年版本进行开发测试） 
⚫ 需在桌面放置一个“识别.sh”脚本能够一键启动参赛程序、加载模型识
别、输出软件界面、保存和向裁判软件发送文本结果。软件开启后不允
许做任何参数修改，在比赛过程中不允许任何的人工操作或遥控操作；
软件界面必须提供强行关闭程序的按钮。 
⚫ 识别结果输出： 
除通过通讯方式发送识别结果外，软件还需将识别结果按格式以文本文
中国机器人大赛暨RoboCup机器人世界杯中国赛机器人先进视觉赛项3D识别项目竞赛规则 
件形式保存，文件名为：报名单位英文缩写-队伍名英文缩写-Rx.txt，请将R
之后的“x”替换为具体轮次（1和2），txt为后缀名，保存的内容格式如图2
所示，第一行为字符START，之后的每一行显示各个识别物体的检测结果，
每个识别目标用换行隔开，各目标物中的每一分项结果用英文分号(;)隔开，
行与行之间通过回车键分隔，最后一行为字符END。该结果文本文件在正常
通讯失败时用于裁判盒软件快速计算比赛得分。 
图2识别结果文本保存格式 
⚫ 结果文件输出目录为桌面下result_r文件夹。 
3.3 技术认证文档要求 
3D 识别项目有资格认证环节，每支报名的参赛队伍必须在报名的同时提交
资格认证材料到指定邮箱（<EMAIL>）。不提交资格认证材料的队伍不
具备比赛资格；资格认证所需提交材料内容以及评分标准详见本文档最后的附件
（3D识别项目参赛队伍资格认证模板）；在比赛成绩出现相同情况下，由资格认
证评分来决定队伍排名，资格认证排名靠前的最终比赛排名靠前。 
四、参赛人员要求 
每个参赛队指导老师不超过2人，参赛队员不超过5人。 
五、技术与竞赛组织讨论群 
为方便赛前赛后的信息沟通，使用先进视觉赛技术交流QQ群：1027375571。
在群内将会由技术委员会与组织委员会对感兴趣的参赛队解答疑问，发布裁判软
件等。  
中国机器人大赛暨RoboCup机器人世界杯中国赛机器人先进视觉赛项3D识别项目竞赛规则 
六、比赛场地及器材 
6.1 比赛场地说明 
场地地面保留原样，不做特别的改动，技术委员会在得到场地信息后及时公
布。 
比赛场地包括测试台和目标台，均由大赛组委会提供。示意图如下： 
0.7 – 1.2m
测试台
0.55m
目标台
图3 3D识别比赛示意图 
6.2 比赛器材说明 
6.2.1 目标台 
0.7 – 1.2m
 0.55m
用于放置目标物体，分为方形和圆形两种，其中方形目标台为宜家LACK拉
克方桌，边长为0.55m，如下图所示，购买链接为： 
https://www.ikea.cn/cn/zh/p/lack-la-ke-bian-zhuo-bai-se-10449909/；  
注：比赛时目标台的桌腿颜色根据现场情况决定，可能与下面图片有颜色差别。 
0c
 0.55m
测试台
0.55m
目标台
Y
图4 方形目标台 
中国机器人大赛暨RoboCup机器人世界杯中国赛机器人先进视觉赛项3D识别项目竞赛规则 
圆形目标台具有自动旋转的功能，其转动装置的最快转速为达到5秒/圈，直径
25cm 左右，白色，最大承重达到25kg。 
图5 圆形转动装置 
比赛时，圆形转台上放置一块半径30cm，厚度为5mm，双面磨砂的亚克力
板。整个转动装置放置于一个支架上，亚克力板放置在转动装置上，亚克力板的
中心与转动装置的中心重合，背景纸贴在亚克力板上，物品放置在背景纸上，实
物图如下所示，目标台整体覆盖住支架上表面和转动装置： 
图6 圆形目标台实物图 
目标台背景颜色不限定（可能为混合色），由裁判或志愿者现场决定。 
6.2.2 干扰 
（1）干扰贴图：赛前不再发布指定的贴纸。比赛时由志愿者拍摄实物图片、或
从网上查找的图片，并进行彩色打印，随机贴于目标台背景纸上作为干扰项。 
（2）干扰物：比赛时裁判将随机选择一些不局限于比赛指定的目标物，放置于
目标台周围地面上作为干扰物，干扰物摆放高度和位姿由裁判决定。 
6.2.3 光源 
（1）比赛过程中未说明使用特定光源，则皆使用比赛场地自带光源。 
（2）特定光源：采用常见的夹式台灯，光源颜色为黄色和白色，功率为7瓦，
示意图如图7所示。  
中国机器人大赛暨RoboCup机器人世界杯中国赛机器人先进视觉赛项3D识别项目竞赛规则 
图7 夹式台灯（左：黄光，右：白光） 
6.2.4 赛场布局 
比赛场地包括测试台和目标台，均由大赛组委会提供，布局情况如图8所示。 
（1）第一轮比赛时，有1张方形目标台，无特定光源。 
（2）第二轮比赛时，有1张圆形转动目标台，有特定光源、照射角度和光源
颜色由裁判临场决定。圆形目标台的旋转方向为顺时针，转速由裁判临场决定。 
（3）两轮比赛目标台中心到摄像头中心的距离都在1.0米-1.8米之间，目标台
上均覆盖随机的背景纸。 
a） 第一轮比赛 
b）第二轮比赛 
图8 实物布局示意图 
中国机器人大赛暨RoboCup机器人世界杯中国赛机器人先进视觉赛项3D识别项目竞赛规则 
6.2.5 目标物 
目标物为常见日用品、副食品、饮料和水果及未知物品，所有物品由组织委
员会负责采购并在赛前调试当天公布部分物品，比赛时从所有物品中随机挑选。
所有目标物的编号说明如表1所示：采用字母与后三位数字结合的编码方式。其
中大类物品为两个字母，例如CA001，首字母C代表为常见大类；第二字母表
示大类中的具体细类，A代表日用品、B代表副食品、C代表饮料、D代表水果；
未知物品首字母 W，未知物品不提前公布，在赛前会公布未知物品类别，比赛
中要通过识别未知物品表面的文字进行分类，不提供未知物品外观训练（比如未
知物小类为数学类书籍，则《高等数学》课本就可以归为该类。未知物可以通过
表面的文字直观的进行分类，但不限于书籍）。后三位数字为物体在该类中的具
体编号（未知物品编号在比赛现场公布）。常见大类物品见表2至表5。其中常
见大类物品，每类物品都有许多实际形式，图片仅为示意参考，组委会采购的物
品的品牌及外观均可与表中图片有差异，但所属类别不会改变。另外，比赛过程
中，所有物品实际摆放的姿态任意（只要物品相对台面静止即可），由裁判临场
决定。 
比赛中为了减轻转台负载，一些目标物有可能是空瓶或者有部分水。 
表1目标编号说明 
编号 
物类 
CAxxx 
大类日用品 
CBxxx 
大类副食品 
CCxxx 
大类饮料 
CDxxx 
大类水果 
Wxxx 
未知物品 
表2 大类日用品列表（2种） 
中国机器人大赛暨RoboCup机器人世界杯中国赛机器人先进视觉赛项3D识别项目竞赛规则 
 
 
编号：CA001 
名称：衣架 
  
 
 
 
编号：CA002 
名称：牙刷 
 
 
 
 
 
 
表3 大类副食品列表（2种） 
编号：CB001 
名称：果冻 
 
   
编号：CB002 
名称：长方形状饼干 
 
   
 
表4 大类饮料列表（2种） 
编号：CC001 
名称：罐装饮料 
   
 
编号：CC002 
名称：瓶装饮料 
     
     
中国机器人大赛暨RoboCup机器人世界杯中国赛机器人先进视觉赛项3D识别项目竞赛规则 
表5 大类水果列表（2种） 
编号：CD001 
名称：香蕉 
编号：CD002 
名称：橙子 
物体摆放姿态不限，并可能出现物体间叠放的可能。 
七、赛事规则要求与评分标准 
7.1 竞赛过程 
a) 每轮比赛前，各参赛队上交参赛计算设备，不允许再调试代码。 
b) 抽签决定比赛顺序。 
c) 由裁判随机决定每张目标台的背景纸类型。 
d) 裁判根据比赛场地要求放置目标台、测试台和摄像头；摄像头与目标台
的距离和视角由裁判根据摆放后的物品临场决定，确保摄像头能完整拍
摄到整个桌面及所有物品。 
e) 裁判临场决定比赛中光源投射以及投射的角度。 
f) 
物品的选取和摆放：每轮比赛的物品种类及其数量由裁判通过抓阄等方
式随机决定，同类物品最多为3个；物品的具体摆放位置和姿态由裁判
临场决定；用于干扰的物品图片的种类、数量及在桌面上的位置由裁判
临场决定；场地干扰物放置由裁判临场决定。 
g) 在摆放好所有物品之后，裁判或志愿者将物品真值录入裁判盒软件。 
h) 由裁判临场决定转台速度设置。 
i) 
识别过程：每一轮相机固定不动，由裁判按回车执行识别脚本，在规定
时间内，识别程序须自动停止，并上传结果，裁判盒软件自动计分。 
j） 裁判打印得分明细表，裁判和参赛队员一起签字确认。 
中国机器人大赛暨RoboCup机器人世界杯中国赛机器人先进视觉赛项3D识别项目竞赛规则 
7.2 竞赛注意事项 
a) 比赛过程中禁止手动设置相机参数，可以利用相机SDK进行修改（比赛
前将会重置相机参数，参赛队需自行确认参数的正确性）。 
b) 比赛过程中，软件第一次识别成功则不再给与重试机会，第一次程序启
动失败给予一次重开机会。最多两次机会，且如果裁判盒软件出了（通
过通讯或者生成了结果文件）成绩就不允许重试了。 
c) 3D 识别干扰物包括图片干扰物和桌子之外的实物干扰物；图片干扰物，
现场随机打印，摆放不限个数；桌子之外的实物干扰物，随机选择，调
试时不提前公布，摆放方式不限（可能堆叠）。 
d) 物品不再提前公布，直到在赛前调试时，将会拿出少量的比赛物品供大
家测试，另一部分在比赛时才公开。 
e) 在规定时间（MaxTime）内不能自动结束的，由裁判强行关闭软件。若没
有上传结果至裁判盒则使用生成的结果文件进行算分，具体计分规则见
第7.3 小节；若没有结果文件则不得分。 
7.3 评分标准 
总分=识别准度分+识别时间分。识别准度分按下表计算。 
表6 评分标准 
分项标识 分项解释 
得分说明 
目标物品的ID号及
其数量 
Goal_ID
及Num 
识别到的物品ID和Num都与真值相同，则
得3分；物品ID不在真值列表中，则扣3
分；如果物品ID正确，Num<真实值，则得
分=（Num/真实值）*3；如果物品ID正确，
Num>真实值，则该物品不得分；其余情况均
不得分也不扣分。 
注：识别时间分见第7.4节。 
7.4 评分表 
在每轮比赛中，实物真值由裁判输入到裁判盒软件，按表7样式生成表格，
打印后由裁判签字确认；每支队伍的得分表由裁判盒软件生成，打印后由队员签
字确认，如表8所示。 
中国机器人大赛暨RoboCup机器人世界杯中国赛机器人先进视觉赛项3D识别项目竞赛规则 
表7 单轮实物真值表 
比赛轮次：                
裁判签字： 
物品真值 
物品名称 
实物1   
ID Num 
实物2   
实物3   
实物4   
实物5   
实物6   
实物7   
实物8   
表8 单轮队伍得分表 
结果文件名：                                              
队员签字： 
裁判签字： 
实测值  
ID Num 
得分 
物品名称 
实物1    
实物2    
实物3    
实物4    
实物5    
实物6    
实物7    
实物8    
本轮识别总得分  
本轮识别平均分  
本轮识别总时间  
本轮识别时间分  
本轮总分  
注1：本轮识别平均分由本轮识别总得分除以实物种类得到。 
注2：本轮总分为表中识别平均分与识别时间分TimeScore之和，TimeScore
按下式计算： 
中国机器人大赛暨RoboCup机器人世界杯中国赛机器人先进视觉赛项3D识别项目竞赛规则 
 
 
, 
0, 
,
 MaxAvg TimeWeight if TotalTime MinTime
 TimeScore if TotalTime MaxTime
 MaxTime TotalTime others MaxAvg TimeWeight MaxTime MinTime
 
     =  
  −    − 
 
,
 0,
 MeasureScore MeasureScore if MinProp TimeWeight FullScore FullScore
 others
    =
  
 
式中MaxAvg为最大识别平均分，即识别总得分满分情况下的平均分；
MeasureScore为识别总得分；FullScore为识别满分值；TimeWeight为时间分权
重，当识别总得分占到识别满分的比值到达MinProp时，才具有时间得分，否则
得分为0；MaxTime为最大允许识别时间，MinTime为最快识别时间，当TotalTime
小于等于该值，则识别时间分设为MaxAvg TimeWeight 。 
注3：两轮比赛的MinTime、MaxTime、MinProp取值，及计分方法见后续
QQ群里公布的补充说明。 
注4：识别结果文件必须通过网络通讯发送给裁判盒软件进行自动计分，若
通过结果文件手动输入到裁判盒软件计分，则该轮总分扣除10%。 
注5：识别时间从参赛软件运行开始计时，要求参赛软件打开时立即向裁
判盒发送开始识别信号。如果助理裁判发现明显延迟，将使用手动计时，并按
注4进行扣分。 
 
 
 
 
 
中国机器人大赛暨RoboCup机器人世界杯中国赛机器人先进视觉赛项3D识别项目竞赛规则 
八、赛程赛制 
8.1 调试时间 
正式比赛之前组织委员会安排各参赛队进行调试，具体调试时间发布请见赛
场消息栏或技术交流群。 
8.2 比赛轮次 
正常比赛共有两轮。总成绩=第一轮*40%+第二轮*60%，总成绩相同则根据
第二轮的成绩进行排名，若还是相同，则根据技术资格认证的分数进行排名。每
轮的比赛说明如下： 
➢ 第一轮：待识别物品为表2至表5中的物品以及未知物品，按照图8(a)
所示的布局，该目标台上共有6~12个物品，物品间可能存在遮挡；存在
若干张物品贴图的干扰，存在场地干扰物，存在物体间的叠放，无特定
光源照射。 
➢ 第二轮：待识别物品为表2至表5中的物品以及未知物品，按照图8(b)
所示的布局，目标台上共有6~12个物品，物品间可能存在遮挡；存在若
干张物品贴图的干扰，存在场地干扰物，存在物体间的叠放；目标台要
求在特定光源的照射下进行识别。 
中国机器人大赛暨RoboCup机器人世界杯中国赛机器人先进视觉赛项3D识别项目竞赛规则 
附件：参赛队伍资格认证模板 
2025 中国机器人大赛暨RoboCup机器
人世界杯中国赛 
机器人先进视觉赛项参赛队资格认证 
参 赛 学 校                          
队 伍 名 称                          
参 赛 队 员               
指 导 教 师 
（姓名/联系方式）                           
参 赛 项 目                   
中国机器人大赛暨RoboCup中国赛 
机器人先进视觉赛项技术委员会 
2025 年1月 
中国机器人大赛暨RoboCup机器人世界杯中国赛机器人先进视觉赛项3D识别项目竞赛规则 
资格认证材料提交说明 
一 技术认证文档要求 
特别注意：每支报名的参赛队伍必须在报名的同时提交资格认证材料到指定
邮箱（<EMAIL>）， 不提交资格认证材料的队伍不具备比赛资格；资
格认证材料内容包括三个部分（ 着重声明：资格认证材料中必须包含第一部分，
如果提交的材料没有第一部分，不能获得比赛资格）： 
第一部分： 必须提交材料 
①队伍介绍，主要包括成员介绍，以前的参赛介绍等，正文字体为宋体小四， 
1.5 倍行距，应尽量保证排版美观且不少于 4页。 
②机器人功能展示视频，时长应在 2 分钟到 3 分钟之间，主要内容为： 
➢ 目标台不同随机背景下的识别 
➢ 目标台与相机不同距离下的识别 
➢ 实物与贴纸的辨识 
➢ 运动中物品的识别 
③参赛软硬件系统介绍相关材料，特别强调，技术委员会关注各参赛队队员
的自我创新，不能抄袭，不能与他队雷同，否则有可能被取消比赛资格。主要内
容为硬件配置说明；视觉软件界面及功能说明；参赛视觉软件的处理流程、主要
算法、测试结果、相关软件技术等，（正文字体为宋体小四，1.5 倍行距）应尽
量保证排版美观且不少于 4页。 
第二部分： 过往参赛证明 
近 3 年（ 即 2022，2023，2024 年） 参加中国自动化学会组织的中国机
器人大赛机器人先进视觉项目的获奖情况说明，同时需提供相应证明材料图片。 
第三部分： 贡献证明材料 
近 3 年来团队或团队成员公开发表的与此机器人涉及技术相关的论文、申
请的专利与软件著作权等情况说明，同时需提供相应证明材料图片。 
二 技术认证文档评分 
技术认证文档评分由技术委员会评定。 
资格认证材料中必须包含第一部分，如果无法提供其他两部分材料，需提交
中国机器人大赛暨RoboCup机器人世界杯中国赛机器人先进视觉赛项3D识别项目竞赛规则 
一份说明文档，对情况予以说明；资格认证材料由先进视觉赛机器人技术委员会
进行评分并排序；在比赛成绩出现相同情况下，由资格认证评分来决定队伍排名， 
资格认证排名靠前的最终比赛排名靠前。 
资格认证材料评分依据如下： 
（1） 对于必须提交材料：此项材料不计分，如果不提交此项材料，直接取消
比赛资格；如果提交的材料不合要求，从资格认证总分中扣除相应分数，队伍介
绍（扣10分），机器人功能展示视频（扣10分），参赛软硬件系统介绍相关材
料（扣10分）。 
（2） 对于过往参赛证明材料：一项一等奖20分，一项二等奖 15分，一项三
等奖 10分。  
（3） 对于贡献证明材料：与机器人视觉抓取、物体识别等相关的1篇论文、
1 项发明专利授权得10分，1项发明专利申请受理、1项软件著作权、1项实用
新型专利授权得3分。 
注 1：材料在提交时压缩包统一命名为： XX单位_3D识别_资格认证材料；
压缩包内包括一个技术认证文档，及一个视频文件夹。 
注 2：每队上传的资格认证材料严格控制在40M以内，若大于40M的扣20
分。 
注 3：参赛队在提交资格认证时指出上一年度比赛的贡献，由技术委员会决
定是否给予20加分。  
中国机器人大赛暨RoboCup机器人世界杯中国赛机器人先进视觉赛项3D识别项目竞赛规则 
关于技术报告使用授权的说明  
本人完全了解2025中国机器人大赛暨RoboCup中国赛关于保留、
使用技术报告和研究论文的规定，即：参赛作品著作权归参赛者本人
所有，比赛组委会可以在相关主页上收录并公开参赛作品的设计方案、
技术报告以及参赛模型的视频、图像资料，并将相关内容编纂收录在
组委会出版论文集中。  
参赛队员签名：                           
带队教师签名：                         
日        
期：                              
中国机器人大赛暨RoboCup机器人世界杯中国赛机器人先进视觉赛项3D识别项目竞赛规则 
 
 
一、基本信息（必填） 
1、队伍介绍 
 
 
 
 
 
 
 
 
2、机器人功能展示视频介绍 
 
 
 
 
 
 
 
3、参赛软硬件系统介绍 
 
中国机器人大赛暨RoboCup机器人世界杯中国赛机器人先进视觉赛项3D识别项目竞赛规则 
 
 
二、过往参赛证明 
 
 
 
中国机器人大赛暨RoboCup机器人世界杯中国赛机器人先进视觉赛项3D识别项目竞赛规则 
 
1 
 
三、贡献证明 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
中国机器人大赛暨RoboCup机器人世界杯中国赛机器人先进视觉赛项3D识别项目竞赛规则 
 
2 
 
四、对2024先进视觉赛项比赛的贡献 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 