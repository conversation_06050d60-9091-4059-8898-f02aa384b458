# 优化的比赛数据集配置（带类别权重）
path: /home/<USER>/homes/ch/claude/SpatialVLA/3D/competition_2025_dataset
train: images/train
val: images/val
test: images/test

# 类别配置
nc: 9
names: ['CA001_衣架', 'CA002_牙刷', 'CB001_果冻', 'CB002_长方形状饼干',
        'CC001_罐装饮料', 'CC002_瓶装饮料', 'CD001_香蕉', 'CD002_橙子', 'Wxxx_未知物品']

# 类别权重 - 为困难类别增加权重
class_weights: [1.0, 3.0, 3.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.5]
# 对应: [衣架, 牙刷, 果冻, 饼干, 罐装饮料, 瓶装饮料, 香蕉, 橙子, 未知物品]

# 深度图像路径
depth_train: depth/train
depth_val: depth/val
depth_test: depth/test

# 针对小物体和细长物体的特殊配置
small_object_threshold: 32  # 小物体阈值
thin_object_aspect_ratio: 3.0  # 细长物体长宽比阈值

# 数据增强特殊配置
augment_small_objects: true
augment_thin_objects: true
preserve_aspect_ratio: true
