#!/usr/bin/env python3
"""
YOLO11x训练脚本
复刻YOLO11n的训练过程，在merged_rgbd_dataset上训练YOLO11x模型
"""

import os
import time
import json
import yaml
from pathlib import Path
from ultralytics import YOLO
import torch

def setup_training_environment():
    """设置训练环境"""
    print("=== 设置训练环境 ===")
    
    # 创建输出目录
    output_dir = Path("/home/<USER>/claude/SpatialVLA/3D/yolo11x")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 检查GPU
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    if device == 'cuda':
        print(f"GPU: {torch.cuda.get_device_name(0)}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    
    return output_dir, device

def load_yolo11n_config():
    """加载YOLO11n的训练配置作为参考"""
    print("=== 加载YOLO11n配置 ===")
    
    config_file = Path("/home/<USER>/claude/SpatialVLA/3D/competition_2025_models/competition_2025_yolo11n/args.yaml")
    
    if config_file.exists():
        with open(config_file, 'r', encoding='utf-8') as f:
            yolo11n_config = yaml.safe_load(f)
        print("✓ 成功加载YOLO11n配置")
        return yolo11n_config
    else:
        print("⚠ 未找到YOLO11n配置，使用默认配置")
        return {}

def create_yolo11x_config(yolo11n_config, output_dir):
    """创建YOLO11x的训练配置"""
    print("=== 创建YOLO11x配置 ===")
    
    # 基于YOLO11n配置，调整为YOLO11x
    yolo11x_config = {
        # 基本配置
        'task': 'detect',
        'mode': 'train',
        'model': 'yolo11x.pt',  # 使用YOLO11x模型
        'data': '/home/<USER>/claude/SpatialVLA/3D/yolo11x/dataset/dataset.yaml',
        
        # 训练参数（基于YOLO11n，适当调整）
        'epochs': 200,  # 增加训练轮数，因为数据更多
        'patience': 50,  # 增加早停耐心
        'batch': 8,     # 减小batch size，因为YOLO11x更大
        'imgsz': 640,
        'device': '0',
        'workers': 8,
        
        # 保存和项目配置
        'project': str(output_dir),
        'name': 'yolo11x_training',
        'exist_ok': True,
        'save': True,
        'save_period': 10,
        'plots': True,
        
        # 优化器配置（复制YOLO11n的成功配置）
        'optimizer': yolo11n_config.get('optimizer', 'AdamW'),
        'lr0': yolo11n_config.get('lr0', 0.001),
        'lrf': yolo11n_config.get('lrf', 0.01),
        'momentum': yolo11n_config.get('momentum', 0.937),
        'weight_decay': yolo11n_config.get('weight_decay', 0.0005),
        'warmup_epochs': yolo11n_config.get('warmup_epochs', 3),
        'warmup_momentum': yolo11n_config.get('warmup_momentum', 0.8),
        'warmup_bias_lr': yolo11n_config.get('warmup_bias_lr', 0.1),
        
        # 损失函数权重
        'box': yolo11n_config.get('box', 7.5),
        'cls': yolo11n_config.get('cls', 0.5),
        'dfl': yolo11n_config.get('dfl', 1.5),
        
        # 数据增强（复制YOLO11n的成功配置）
        'hsv_h': yolo11n_config.get('hsv_h', 0.015),
        'hsv_s': yolo11n_config.get('hsv_s', 0.7),
        'hsv_v': yolo11n_config.get('hsv_v', 0.4),
        'degrees': yolo11n_config.get('degrees', 15.0),
        'translate': yolo11n_config.get('translate', 0.1),
        'scale': yolo11n_config.get('scale', 0.5),
        'shear': yolo11n_config.get('shear', 0.0),
        'perspective': yolo11n_config.get('perspective', 0.0),
        'flipud': yolo11n_config.get('flipud', 0.0),
        'fliplr': yolo11n_config.get('fliplr', 0.5),
        'mosaic': yolo11n_config.get('mosaic', 1.0),
        'mixup': yolo11n_config.get('mixup', 0.0),
        'copy_paste': yolo11n_config.get('copy_paste', 0.0),
        
        # 其他配置
        'val': True,
        'cache': False,
        'amp': True,
        'close_mosaic': 10,
        'verbose': True,
        'seed': 0,
        'deterministic': True,
    }
    
    # 保存配置文件
    config_file = output_dir / 'yolo11x_config.yaml'
    with open(config_file, 'w', encoding='utf-8') as f:
        yaml.dump(yolo11x_config, f, default_flow_style=False, allow_unicode=True)
    
    print(f"✓ YOLO11x配置保存到: {config_file}")
    return yolo11x_config

def train_yolo11x(config, output_dir):
    """训练YOLO11x模型"""
    print("=== 开始训练YOLO11x ===")
    
    # 记录开始时间
    start_time = time.time()
    
    try:
        # 初始化模型
        print("加载YOLO11x模型...")
        model_start_time = time.time()
        model = YOLO('yolo11x.pt')
        model_load_time = time.time() - model_start_time
        print(f"✓ 模型加载完成，耗时: {model_load_time:.2f}秒")
        
        # 开始训练
        print("开始训练...")
        results = model.train(
            data=config['data'],
            epochs=config['epochs'],
            patience=config['patience'],
            batch=config['batch'],
            imgsz=config['imgsz'],
            device=config['device'],
            workers=config['workers'],
            project=config['project'],
            name=config['name'],
            exist_ok=config['exist_ok'],
            save=config['save'],
            save_period=config['save_period'],
            plots=config['plots'],
            optimizer=config['optimizer'],
            lr0=config['lr0'],
            lrf=config['lrf'],
            momentum=config['momentum'],
            weight_decay=config['weight_decay'],
            warmup_epochs=config['warmup_epochs'],
            warmup_momentum=config['warmup_momentum'],
            warmup_bias_lr=config['warmup_bias_lr'],
            box=config['box'],
            cls=config['cls'],
            dfl=config['dfl'],
            hsv_h=config['hsv_h'],
            hsv_s=config['hsv_s'],
            hsv_v=config['hsv_v'],
            degrees=config['degrees'],
            translate=config['translate'],
            scale=config['scale'],
            shear=config['shear'],
            perspective=config['perspective'],
            flipud=config['flipud'],
            fliplr=config['fliplr'],
            mosaic=config['mosaic'],
            mixup=config['mixup'],
            copy_paste=config['copy_paste'],
            val=config['val'],
            cache=config['cache'],
            amp=config['amp'],
            close_mosaic=config['close_mosaic'],
            verbose=config['verbose'],
            seed=config['seed'],
            deterministic=config['deterministic']
        )
        
        # 计算训练时间
        training_time = time.time() - start_time
        
        print(f"✓ 训练完成！总耗时: {training_time/3600:.2f}小时")
        
        return results, training_time, model_load_time
        
    except Exception as e:
        print(f"✗ 训练过程中出现错误: {e}")
        return None, 0, 0

def evaluate_model(output_dir):
    """评估训练好的模型"""
    print("=== 评估模型性能 ===")
    
    # 查找最佳模型
    best_model_path = output_dir / 'yolo11x_training' / 'weights' / 'best.pt'
    
    if not best_model_path.exists():
        print("✗ 未找到训练好的模型")
        return {}
    
    try:
        # 加载模型进行评估
        model = YOLO(str(best_model_path))
        
        # 在验证集上评估
        val_results = model.val()
        
        # 提取关键指标
        metrics = {
            'mAP50': float(val_results.box.map50),
            'mAP50_95': float(val_results.box.map),
            'precision': float(val_results.box.mp),
            'recall': float(val_results.box.mr)
        }
        
        print(f"✓ 模型评估完成:")
        print(f"  mAP@0.5: {metrics['mAP50']:.4f}")
        print(f"  mAP@0.5:0.95: {metrics['mAP50_95']:.4f}")
        print(f"  Precision: {metrics['precision']:.4f}")
        print(f"  Recall: {metrics['recall']:.4f}")
        
        return metrics
        
    except Exception as e:
        print(f"✗ 模型评估失败: {e}")
        return {}

def save_training_results(output_dir, config, training_time, model_load_time, metrics):
    """保存训练结果"""
    print("=== 保存训练结果 ===")
    
    results = {
        'model_name': 'yolo11x',
        'description': '基于merged_rgbd_dataset训练的YOLO11x模型',
        'training_time_hours': training_time / 3600,
        'model_loading_time_seconds': model_load_time,
        'dataset': 'merged_rgbd_dataset',
        'total_images': 1711,
        'train_images': 1419,
        'val_images': 194,
        'test_images': 98,
        'metrics': metrics,
        'model_path': str(output_dir / 'yolo11x_training' / 'weights' / 'best.pt'),
        'config': config,
        'training_date': time.strftime('%Y-%m-%d %H:%M:%S'),
        'comparison_with_yolo11n': {
            'model_size': 'YOLO11x (更大)',
            'dataset_size': '1711 vs 971 (更多数据)',
            'expected_performance': '更高精度，更长训练时间'
        }
    }
    
    # 保存结果
    results_file = output_dir / 'training_results_yolo11x.json'
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"✓ 训练结果保存到: {results_file}")
    return results

def main():
    """主函数"""
    print("开始YOLO11x训练流程...")
    print("=" * 60)
    
    # 1. 设置环境
    output_dir, device = setup_training_environment()
    
    # 2. 加载YOLO11n配置作为参考
    yolo11n_config = load_yolo11n_config()
    
    # 3. 创建YOLO11x配置
    yolo11x_config = create_yolo11x_config(yolo11n_config, output_dir)
    
    # 4. 训练模型
    results, training_time, model_load_time = train_yolo11x(yolo11x_config, output_dir)
    
    if results is None:
        print("✗ 训练失败，退出程序")
        return
    
    # 5. 评估模型
    metrics = evaluate_model(output_dir)
    
    # 6. 保存结果
    final_results = save_training_results(
        output_dir, yolo11x_config, training_time, model_load_time, metrics
    )
    
    # 7. 总结
    print("\n" + "=" * 60)
    print("=== 训练完成总结 ===")
    print(f"模型: YOLO11x")
    print(f"数据集: merged_rgbd_dataset ({final_results['total_images']} 图像)")
    print(f"训练时间: {final_results['training_time_hours']:.2f} 小时")
    if metrics:
        print(f"mAP@0.5: {metrics['mAP50']:.4f}")
        print(f"mAP@0.5:0.95: {metrics['mAP50_95']:.4f}")
    print(f"模型保存路径: {final_results['model_path']}")
    print(f"结果文件: {output_dir}/training_results_yolo11x.json")

if __name__ == "__main__":
    main()
