#!/usr/bin/env python3
"""
项目打包脚本
将完整的项目打包为可分发的格式
"""

import os
import shutil
import zipfile
import tarfile
from pathlib import Path
import json
import time

class ProjectPackager:
    def __init__(self, project_root=None):
        self.project_root = Path(project_root) if project_root else Path(__file__).parent
        self.package_name = "competition_2025_rgbd_yolo_system"
        self.version = "1.2.0"
        
    def create_package_structure(self, output_dir):
        """创建打包目录结构"""
        package_dir = output_dir / self.package_name
        package_dir.mkdir(parents=True, exist_ok=True)
        
        # 核心目录结构
        dirs_to_create = [
            "data_preparation",
            "training", 
            "inference",
            "evaluation",
            "utils",
            "models",
            "datasets",
            "results/evaluation_reports",
            "results/training_logs", 
            "results/inference_results",
            "docs",
            "examples"
        ]
        
        for dir_name in dirs_to_create:
            (package_dir / dir_name).mkdir(parents=True, exist_ok=True)
            
        return package_dir
        
    def copy_source_files(self, package_dir):
        """复制源代码文件"""
        # 核心脚本文件
        core_files = [
            # 根目录文件
            "README.md",
            "requirements.txt", 
            "setup.py",
            "run_complete_pipeline.py",
            
            # 数据准备
            "competition_2025_dataset_preparation.py",
            
            # 训练脚本
            "competition_2025_training.py",
            "optimized_training_for_thin_small_objects.py",
            "refined_training_system.py",
            "train_yolo11s_optimized.py",
            
            # 推理脚本
            "competition_2025_inference_system.py",
            "adaptive_inference_system.py", 
            "ultimate_competition_system.py",
            
            # 评估脚本
            "comprehensive_model_evaluation.py",
            "test_optimized_models.py",
            "optimize_confidence_threshold.py"
        ]
        
        # 复制核心文件
        for file_name in core_files:
            src_file = self.project_root / file_name
            if src_file.exists():
                if file_name in ["competition_2025_dataset_preparation.py"]:
                    dst_file = package_dir / "data_preparation" / file_name
                elif file_name in ["competition_2025_training.py", "optimized_training_for_thin_small_objects.py", 
                                  "refined_training_system.py", "train_yolo11s_optimized.py"]:
                    dst_file = package_dir / "training" / file_name
                elif file_name in ["competition_2025_inference_system.py", "adaptive_inference_system.py",
                                  "ultimate_competition_system.py"]:
                    dst_file = package_dir / "inference" / file_name
                elif file_name in ["comprehensive_model_evaluation.py", "test_optimized_models.py",
                                  "optimize_confidence_threshold.py"]:
                    dst_file = package_dir / "evaluation" / file_name
                else:
                    dst_file = package_dir / file_name
                    
                shutil.copy2(src_file, dst_file)
                print(f"复制文件: {file_name}")
        
        # 复制utils目录
        utils_src = self.project_root / "utils"
        if utils_src.exists():
            shutil.copytree(utils_src, package_dir / "utils", dirs_exist_ok=True)
            print("复制utils目录")
            
    def copy_models(self, package_dir, include_models=True):
        """复制模型文件"""
        if not include_models:
            print("跳过模型文件复制（文件过大）")
            return
            
        model_dirs = [
            "competition_2025_models",
            "optimized_models", 
            "refined_models",
            "optimized_models_s"
        ]
        
        for model_dir in model_dirs:
            src_dir = self.project_root / model_dir
            if src_dir.exists():
                dst_dir = package_dir / "models" / model_dir
                
                # 只复制最佳模型文件
                for model_subdir in src_dir.iterdir():
                    if model_subdir.is_dir():
                        weights_dir = model_subdir / "weights"
                        if weights_dir.exists():
                            best_pt = weights_dir / "best.pt"
                            if best_pt.exists():
                                dst_weights_dir = dst_dir / model_subdir.name / "weights"
                                dst_weights_dir.mkdir(parents=True, exist_ok=True)
                                shutil.copy2(best_pt, dst_weights_dir / "best.pt")
                                print(f"复制模型: {model_dir}/{model_subdir.name}/weights/best.pt")
                                
    def copy_sample_data(self, package_dir):
        """复制示例数据"""
        # 复制少量示例图像
        dataset_dir = self.project_root / "competition_2025_dataset"
        if dataset_dir.exists():
            sample_dir = package_dir / "datasets" / "sample_data"
            sample_dir.mkdir(parents=True, exist_ok=True)
            
            # 复制配置文件
            config_file = dataset_dir / "competition_dataset.yaml"
            if config_file.exists():
                shutil.copy2(config_file, sample_dir / "competition_dataset.yaml")
                
            # 复制少量示例图像
            test_images_dir = dataset_dir / "images" / "test"
            if test_images_dir.exists():
                sample_images_dir = sample_dir / "images" / "test"
                sample_images_dir.mkdir(parents=True, exist_ok=True)
                
                # 只复制前5张图像作为示例
                for i, img_file in enumerate(test_images_dir.glob("*.jpg")):
                    if i >= 5:
                        break
                    shutil.copy2(img_file, sample_images_dir / img_file.name)
                    
                print("复制示例数据")
                
    def create_documentation(self, package_dir):
        """创建文档"""
        docs_dir = package_dir / "docs"
        
        # 创建快速开始指南
        quick_start = """# 快速开始指南

## 1. 环境安装
```bash
pip install -r requirements.txt
```

## 2. 数据准备
```bash
python data_preparation/competition_2025_dataset_preparation.py
```

## 3. 模型训练
```bash
python training/refined_training_system.py
```

## 4. 模型评估
```bash
python evaluation/comprehensive_model_evaluation.py
```

## 5. 推理测试
```bash
python inference/ultimate_competition_system.py
```

## 一键运行
```bash
python run_complete_pipeline.py --mode full
```
"""
        
        with open(docs_dir / "QUICK_START.md", 'w', encoding='utf-8') as f:
            f.write(quick_start)
            
        # 创建API文档
        api_doc = """# API 文档

## 核心类和函数

### CompetitionConfig
配置管理类，统一管理所有配置参数。

### DetectionVisualizer
检测结果可视化器。

### UltimateCompetitionSystem
终极比赛系统，提供完整的推理功能。

详细API文档请参考源代码注释。
"""
        
        with open(docs_dir / "API.md", 'w', encoding='utf-8') as f:
            f.write(api_doc)
            
        print("创建文档")
        
    def create_examples(self, package_dir):
        """创建示例代码"""
        examples_dir = package_dir / "examples"
        
        # 简单推理示例
        simple_inference = '''#!/usr/bin/env python3
"""
简单推理示例
"""

from pathlib import Path
import sys
sys.path.append(str(Path(__file__).parent.parent))

from inference.ultimate_competition_system import UltimateCompetitionSystem

def main():
    # 创建推理系统
    system = UltimateCompetitionSystem()
    
    # 初始化
    system.initialize_system()
    
    # 处理示例图像
    test_image = "datasets/sample_data/images/test/sample.jpg"
    if Path(test_image).exists():
        result = system.process_competition_image(test_image)
        print("检测完成!")
        print(result['output_text'])
    else:
        print("示例图像不存在")

if __name__ == "__main__":
    main()
'''
        
        with open(examples_dir / "simple_inference.py", 'w', encoding='utf-8') as f:
            f.write(simple_inference)
            
        # 批量处理示例
        batch_processing = '''#!/usr/bin/env python3
"""
批量处理示例
"""

from pathlib import Path
import sys
sys.path.append(str(Path(__file__).parent.parent))

from inference.ultimate_competition_system import UltimateCompetitionSystem

def main():
    # 创建推理系统
    system = UltimateCompetitionSystem()
    system.initialize_system()
    
    # 批量处理图像
    image_dir = Path("datasets/sample_data/images/test")
    if image_dir.exists():
        for img_file in image_dir.glob("*.jpg"):
            result = system.process_competition_image(img_file)
            print(f"处理完成: {img_file.name}")
    else:
        print("图像目录不存在")

if __name__ == "__main__":
    main()
'''
        
        with open(examples_dir / "batch_processing.py", 'w', encoding='utf-8') as f:
            f.write(batch_processing)
            
        print("创建示例代码")
        
    def create_package_info(self, package_dir):
        """创建包信息文件"""
        package_info = {
            "name": self.package_name,
            "version": self.version,
            "description": "2025年比赛RGB-D YOLO目标检测系统",
            "created_at": time.strftime("%Y-%m-%d %H:%M:%S"),
            "python_version": ">=3.8",
            "dependencies": [
                "torch>=2.0.0",
                "ultralytics>=8.3.0", 
                "opencv-python>=4.8.0",
                "numpy>=1.21.0",
                "matplotlib>=3.6.0"
            ],
            "models": {
                "highest_accuracy": "models/refined_models/refined_yolo11n_stage1_balanced/weights/best.pt",
                "difficult_categories": "models/optimized_models_s/class_weighted_yolo11s_thin_small/weights/best.pt",
                "balanced_performance": "models/optimized_models/class_weighted_yolo11n_thin_small/weights/best.pt"
            },
            "performance": {
                "highest_mAP50": 95.54,
                "difficult_categories_improvement": 33.0,
                "inference_time": "< 0.05s",
                "loading_time": "< 1s"
            }
        }
        
        with open(package_dir / "package_info.json", 'w', encoding='utf-8') as f:
            json.dump(package_info, f, indent=2, ensure_ascii=False)
            
        print("创建包信息文件")
        
    def create_archive(self, package_dir, format='zip'):
        """创建压缩包"""
        output_dir = package_dir.parent
        archive_name = f"{self.package_name}_v{self.version}"
        
        if format == 'zip':
            archive_path = output_dir / f"{archive_name}.zip"
            with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file_path in package_dir.rglob('*'):
                    if file_path.is_file():
                        arcname = file_path.relative_to(package_dir.parent)
                        zipf.write(file_path, arcname)
        else:
            archive_path = output_dir / f"{archive_name}.tar.gz"
            with tarfile.open(archive_path, 'w:gz') as tarf:
                tarf.add(package_dir, arcname=package_dir.name)
                
        print(f"创建压缩包: {archive_path}")
        return archive_path
        
    def package_project(self, output_dir="dist", include_models=False, format='zip'):
        """打包项目"""
        output_dir = Path(output_dir)
        output_dir.mkdir(exist_ok=True)
        
        print(f"开始打包项目: {self.package_name} v{self.version}")
        
        # 创建包目录结构
        package_dir = self.create_package_structure(output_dir)
        
        # 复制文件
        self.copy_source_files(package_dir)
        self.copy_models(package_dir, include_models)
        self.copy_sample_data(package_dir)
        
        # 创建文档和示例
        self.create_documentation(package_dir)
        self.create_examples(package_dir)
        self.create_package_info(package_dir)
        
        # 创建压缩包
        archive_path = self.create_archive(package_dir, format)
        
        print(f"✅ 项目打包完成: {archive_path}")
        print(f"📦 包大小: {archive_path.stat().st_size / 1024 / 1024:.2f} MB")
        
        return archive_path

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="项目打包脚本")
    parser.add_argument("--output", "-o", default="dist", help="输出目录")
    parser.add_argument("--include-models", action="store_true", help="包含模型文件")
    parser.add_argument("--format", choices=['zip', 'tar'], default='zip', help="压缩格式")
    
    args = parser.parse_args()
    
    packager = ProjectPackager()
    archive_path = packager.package_project(
        output_dir=args.output,
        include_models=args.include_models,
        format=args.format
    )
    
    print(f"\n🎉 打包完成!")
    print(f"📁 输出文件: {archive_path}")
    print(f"\n📋 使用说明:")
    print(f"1. 解压文件")
    print(f"2. 进入目录: cd {packager.package_name}")
    print(f"3. 安装依赖: pip install -r requirements.txt")
    print(f"4. 运行系统: python run_complete_pipeline.py --mode full")

if __name__ == "__main__":
    main()
