#!/usr/bin/env python3
"""
一键运行快速改进测试
先进损失函数 + TTA测试
"""

import os
import sys
import time
import json
import argparse
from pathlib import Path

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="快速mAP50-95改进测试")
    parser.add_argument("--test", choices=["loss", "tta", "both"], default="both", 
                       help="测试类型: loss(损失函数), tta(测试时增强), both(两者)")
    parser.add_argument("--quick", action="store_true", help="快速模式（减少测试时间）")
    
    args = parser.parse_args()
    
    print("🚀 启动快速mAP50-95改进测试")
    print(f"📊 测试类型: {args.test}")
    print(f"⚡ 快速模式: {'是' if args.quick else '否'}")
    print("=" * 60)
    
    results = {}
    start_time = time.time()
    
    # 测试1: 损失函数优化
    if args.test in ["loss", "both"]:
        print("\n🔧 第一步: 测试先进损失函数")
        print("-" * 40)
        
        try:
            from quick_loss_function_test import QuickLossFunctionTester
            
            loss_tester = QuickLossFunctionTester()
            
            # 如果是快速模式，减少训练轮数
            if args.quick:
                loss_tester.config["test_epochs"] = 10
                print("⚡ 快速模式: 减少到10轮训练")
            
            loss_results = loss_tester.test_loss_functions()
            results["loss_function_test"] = loss_results
            
            if "error" not in loss_results:
                print("✅ 损失函数测试完成")
            else:
                print(f"❌ 损失函数测试失败: {loss_results['error']}")
                
        except Exception as e:
            print(f"❌ 损失函数测试异常: {e}")
            results["loss_function_test"] = {"error": str(e)}
    
    # 测试2: TTA优化
    if args.test in ["tta", "both"]:
        print("\n🔄 第二步: 测试时增强(TTA)")
        print("-" * 40)
        
        try:
            from quick_tta_test import QuickTTATester
            
            tta_tester = QuickTTATester()
            
            # 如果是快速模式，减少测试图像数量
            if args.quick:
                tta_tester.config["max_test_images"] = 10
                print("⚡ 快速模式: 减少到10张测试图像")
            
            tta_results = tta_tester.test_tta_effects()
            results["tta_test"] = tta_results
            
            if "error" not in tta_results:
                print("✅ TTA测试完成")
            else:
                print(f"❌ TTA测试失败: {tta_results['error']}")
                
        except Exception as e:
            print(f"❌ TTA测试异常: {e}")
            results["tta_test"] = {"error": str(e)}
    
    # 生成综合报告
    total_time = time.time() - start_time
    
    print("\n" + "🎯" * 20)
    print("综合改进测试报告")
    print("🎯" * 20)
    
    print(f"⏱️  总测试时间: {total_time/60:.1f} 分钟")
    
    # 损失函数测试结果
    if "loss_function_test" in results:
        loss_result = results["loss_function_test"]
        if "error" not in loss_result and "analysis" in loss_result:
            analysis = loss_result["analysis"]
            if analysis["best_loss_function"]:
                print(f"\n🏆 最佳损失函数: {analysis['best_loss_function']}")
                print(f"📈 最佳mAP50-95: {analysis['best_map50_95']:.4f}")
                
                # 计算改进
                improvements = analysis.get("improvements", {})
                best_improvement = improvements.get(analysis["best_loss_function"], {})
                if "improvement_percentage" in best_improvement:
                    improvement_pct = best_improvement["improvement_percentage"]
                    print(f"📊 相对改进: {improvement_pct:.1f}%")
        else:
            print("\n❌ 损失函数测试未成功完成")
    
    # TTA测试结果
    if "tta_test" in results:
        tta_result = results["tta_test"]
        if "error" not in tta_result and "analysis" in tta_result:
            analysis = tta_result["analysis"]
            if analysis["best_model"] and analysis["best_tta_config"]:
                print(f"\n🔄 最佳TTA组合: {analysis['best_model']} + {analysis['best_tta_config']}")
                
                # 找到最佳改进
                improvements = analysis.get("improvements", {})
                best_model_improvements = improvements.get(analysis["best_model"], {})
                best_tta_improvement = best_model_improvements.get(analysis["best_tta_config"], {})
                
                if "improvement_percentage" in best_tta_improvement:
                    improvement_pct = best_tta_improvement["improvement_percentage"]
                    print(f"📊 检测数量改进: {improvement_pct:.1f}%")
                    
                    # 估算mAP50-95改进
                    estimated_map_improvement = improvement_pct * 0.02  # 经验公式
                    print(f"📈 估计mAP50-95改进: +{estimated_map_improvement:.3f}")
        else:
            print("\n❌ TTA测试未成功完成")
    
    # 综合建议
    print("\n💡 综合建议:")
    
    # 损失函数建议
    if "loss_function_test" in results:
        loss_result = results["loss_function_test"]
        if "analysis" in loss_result:
            for rec in loss_result["analysis"].get("recommendations", []):
                print(f"  🔧 {rec}")
    
    # TTA建议
    if "tta_test" in results:
        tta_result = results["tta_test"]
        if "analysis" in tta_result:
            for rec in tta_result["analysis"].get("recommendations", []):
                print(f"  🔄 {rec}")
    
    # 下一步建议
    print("\n📋 下一步行动:")
    
    # 基于结果给出具体建议
    has_loss_improvement = False
    has_tta_improvement = False
    
    if "loss_function_test" in results:
        loss_result = results["loss_function_test"]
        if "analysis" in loss_result:
            best_loss = loss_result["analysis"].get("best_loss_function")
            if best_loss and best_loss != "baseline_ciou":
                has_loss_improvement = True
                print(f"  1. 使用 {best_loss} 损失函数重新训练模型")
    
    if "tta_test" in results:
        tta_result = results["tta_test"]
        if "analysis" in tta_result:
            best_tta = tta_result["analysis"].get("best_tta_config")
            if best_tta and best_tta != "baseline":
                has_tta_improvement = True
                print(f"  2. 在推理时启用 {best_tta} TTA配置")
    
    if has_loss_improvement or has_tta_improvement:
        print("  3. 运行完整评估验证改进效果")
        print("  4. 考虑将两种改进结合使用")
    else:
        print("  1. 尝试更多的损失函数配置")
        print("  2. 调整TTA参数（尺度、阈值等）")
        print("  3. 考虑其他优化策略（数据增强、模型集成等）")
    
    # 保存综合结果
    save_comprehensive_results(results, total_time, args)
    
    print(f"\n🎉 快速改进测试完成! 总耗时: {total_time/60:.1f} 分钟")
    
    return 0

def save_comprehensive_results(results: dict, total_time: float, args):
    """保存综合结果"""
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    results_dir = Path("quick_improvement_results")
    results_dir.mkdir(exist_ok=True)
    
    comprehensive_results = {
        "timestamp": timestamp,
        "test_config": {
            "test_type": args.test,
            "quick_mode": args.quick,
            "total_time_minutes": total_time / 60
        },
        "results": results,
        "summary": generate_summary(results)
    }
    
    results_file = results_dir / f"comprehensive_improvement_test_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(comprehensive_results, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\n💾 综合结果已保存: {results_file}")

def generate_summary(results: dict) -> dict:
    """生成结果摘要"""
    summary = {
        "loss_function": {
            "tested": "loss_function_test" in results,
            "successful": False,
            "best_function": None,
            "improvement": 0
        },
        "tta": {
            "tested": "tta_test" in results,
            "successful": False,
            "best_config": None,
            "improvement": 0
        },
        "overall_recommendation": "需要进一步测试"
    }
    
    # 损失函数摘要
    if "loss_function_test" in results:
        loss_result = results["loss_function_test"]
        if "analysis" in loss_result:
            analysis = loss_result["analysis"]
            summary["loss_function"]["successful"] = True
            summary["loss_function"]["best_function"] = analysis.get("best_loss_function")
            
            if analysis["best_loss_function"]:
                improvements = analysis.get("improvements", {})
                best_improvement = improvements.get(analysis["best_loss_function"], {})
                summary["loss_function"]["improvement"] = best_improvement.get("improvement_percentage", 0)
    
    # TTA摘要
    if "tta_test" in results:
        tta_result = results["tta_test"]
        if "analysis" in tta_result:
            analysis = tta_result["analysis"]
            summary["tta"]["successful"] = True
            summary["tta"]["best_config"] = analysis.get("best_tta_config")
            
            if analysis["best_tta_config"]:
                # 计算最佳改进
                improvements = analysis.get("improvements", {})
                best_improvement = 0
                for model_improvements in improvements.values():
                    for tta_improvement in model_improvements.values():
                        improvement_pct = tta_improvement.get("improvement_percentage", 0)
                        if improvement_pct > best_improvement:
                            best_improvement = improvement_pct
                
                summary["tta"]["improvement"] = best_improvement
    
    # 综合建议
    loss_improvement = summary["loss_function"]["improvement"]
    tta_improvement = summary["tta"]["improvement"]
    
    if loss_improvement > 2 or tta_improvement > 5:
        summary["overall_recommendation"] = "有显著改进，建议立即采用"
    elif loss_improvement > 0.5 or tta_improvement > 2:
        summary["overall_recommendation"] = "有小幅改进，可以考虑采用"
    else:
        summary["overall_recommendation"] = "改进不明显，需要尝试其他策略"
    
    return summary

if __name__ == "__main__":
    exit(main())
