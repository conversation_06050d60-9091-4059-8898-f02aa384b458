#!/usr/bin/env python3
"""
YOLO11x最终训练脚本
基于YOLO11n的成功配置，在merged_rgbd_dataset上训练YOLO11x模型
"""

import os
import time
import json
import yaml
from pathlib import Path
from ultralytics import YOLO
import torch

def main():
    """主训练函数"""
    print("=== YOLO11x训练开始 ===")
    print(f"时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查GPU
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    if device == 'cuda':
        print(f"GPU: {torch.cuda.get_device_name(0)}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    
    # 创建输出目录
    output_dir = Path("/home/<USER>/claude/SpatialVLA/3D/yolo11x")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 数据集配置
    dataset_config = "/home/<USER>/claude/SpatialVLA/3D/yolo11x/dataset/dataset.yaml"
    
    try:
        # 初始化YOLO11x模型
        print("加载YOLO11x模型...")
        start_time = time.time()
        model = YOLO('yolo11x.pt')
        load_time = time.time() - start_time
        print(f"✓ 模型加载完成，耗时: {load_time:.2f}秒")
        
        # 开始训练
        print("开始训练...")
        train_start_time = time.time()
        
        # 基于YOLO11n的成功配置进行训练
        results = model.train(
            # 基本配置
            data=dataset_config,
            epochs=200,           # 增加训练轮数
            patience=50,          # 增加早停耐心
            batch=8,              # 适合YOLO11x的batch size
            imgsz=640,
            device='0',
            workers=8,
            
            # 保存配置
            project=str(output_dir),
            name='yolo11x_merged_dataset',
            exist_ok=True,
            save=True,
            save_period=10,       # 每10个epoch保存一次
            plots=True,
            
            # 优化器配置（复制YOLO11n的成功配置）
            optimizer='AdamW',
            lr0=0.001,            # 学习率
            lrf=0.01,             # 最终学习率比例
            momentum=0.937,
            weight_decay=0.0005,
            warmup_epochs=3,
            warmup_momentum=0.8,
            warmup_bias_lr=0.1,
            
            # 损失函数权重
            box=7.5,
            cls=0.5,
            dfl=1.5,
            
            # 数据增强（复制YOLO11n的成功配置）
            hsv_h=0.015,
            hsv_s=0.7,
            hsv_v=0.4,
            degrees=15.0,
            translate=0.1,
            scale=0.5,
            shear=0.0,
            perspective=0.0,
            flipud=0.0,
            fliplr=0.5,
            mosaic=1.0,
            mixup=0.0,
            copy_paste=0.0,
            
            # 其他配置
            val=True,
            cache=False,
            amp=True,
            close_mosaic=10,
            verbose=True,
            seed=0,
            deterministic=True
        )
        
        train_time = time.time() - train_start_time
        print(f"✓ 训练完成！总耗时: {train_time/3600:.2f}小时")
        
        # 评估模型
        print("=== 评估模型性能 ===")
        best_model_path = output_dir / 'yolo11x_merged_dataset' / 'weights' / 'best.pt'
        
        if best_model_path.exists():
            # 加载最佳模型进行评估
            best_model = YOLO(str(best_model_path))
            val_results = best_model.val()
            
            # 提取关键指标
            metrics = {
                'mAP50': float(val_results.box.map50),
                'mAP50_95': float(val_results.box.map),
                'precision': float(val_results.box.mp),
                'recall': float(val_results.box.mr)
            }
            
            print(f"✓ 模型评估完成:")
            print(f"  mAP@0.5: {metrics['mAP50']:.4f}")
            print(f"  mAP@0.5:0.95: {metrics['mAP50_95']:.4f}")
            print(f"  Precision: {metrics['precision']:.4f}")
            print(f"  Recall: {metrics['recall']:.4f}")
        else:
            print("⚠ 未找到最佳模型文件")
            metrics = {}
        
        # 保存训练结果
        training_results = {
            'model_name': 'yolo11x',
            'description': '基于merged_rgbd_dataset训练的YOLO11x模型，复刻YOLO11n成功配置',
            'dataset': 'merged_rgbd_dataset',
            'training_time_hours': train_time / 3600,
            'model_loading_time_seconds': load_time,
            'total_images': 1418,  # 修正后的数据集大小
            'train_images': 1213,
            'val_images': 205,
            'metrics': metrics,
            'model_path': str(best_model_path) if best_model_path.exists() else None,
            'training_date': time.strftime('%Y-%m-%d %H:%M:%S'),
            'comparison_with_yolo11n': {
                'model_size': 'YOLO11x (更大，更高精度)',
                'dataset_size': '1418 vs 971 (更多数据)',
                'expected_performance': '更高精度，更长训练时间',
                'configuration': '复制YOLO11n的成功配置'
            },
            'training_config': {
                'epochs': 200,
                'batch_size': 8,
                'optimizer': 'AdamW',
                'learning_rate': 0.001,
                'data_augmentation': 'enabled',
                'early_stopping': 'patience=50'
            }
        }
        
        # 保存结果到JSON文件
        results_file = output_dir / 'training_results_yolo11x.json'
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(training_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n=== 训练完成总结 ===")
        print(f"模型: YOLO11x")
        print(f"数据集: merged_rgbd_dataset ({training_results['total_images']} 图像)")
        print(f"训练时间: {training_results['training_time_hours']:.2f} 小时")
        if metrics:
            print(f"mAP@0.5: {metrics['mAP50']:.4f}")
            print(f"mAP@0.5:0.95: {metrics['mAP50_95']:.4f}")
        print(f"模型保存路径: {training_results['model_path']}")
        print(f"结果文件: {results_file}")
        print("✅ 训练流程完成！")
        
    except Exception as e:
        print(f"✗ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
