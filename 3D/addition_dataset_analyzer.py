#!/usr/bin/env python3
"""
Addition数据集分析器
解压并分析Addition文件夹中的3个数据集，生成详细的分析报告
"""

import os
import json
import zipfile
import shutil
from pathlib import Path
import cv2
import numpy as np
from typing import Dict, List, Tuple, Any
import datetime

class AdditionDatasetAnalyzer:
    def __init__(self, addition_path: str = "Addition"):
        self.addition_path = Path(addition_path)
        self.analysis_results = {}
        self.temp_extract_dir = Path("temp_extracted")
        
        # 确保临时目录存在
        self.temp_extract_dir.mkdir(exist_ok=True)
        
    def extract_zip_files(self):
        """解压所有zip文件"""
        print("🔄 开始解压Addition数据集...")
        
        zip_files = [
            # Task2 旋转物体数据集
            ("Task2/19组.zip", "task2_19组"),
            ("Task2/8.2标注.zip", "task2_8.2标注"),
            ("Task2/分组标注.zip", "task2_分组标注"),
            # small 小样本数据集
            ("small/8.6标注.zip", "small_8.6标注"),
            ("small/dataset_20250807_coco(小目标）.zip", "small_coco"),
            # unknown 未知物体数据集
            ("unkown/50未知物标注(1).zip", "unknown_50标注")
        ]
        
        extracted_info = {}
        
        for zip_path, extract_name in zip_files:
            full_zip_path = self.addition_path / zip_path
            extract_dir = self.temp_extract_dir / extract_name
            
            if full_zip_path.exists():
                try:
                    print(f"  解压: {zip_path}")
                    
                    # 创建解压目录
                    extract_dir.mkdir(exist_ok=True)
                    
                    # 解压文件
                    with zipfile.ZipFile(full_zip_path, 'r') as zip_ref:
                        zip_ref.extractall(extract_dir)
                    
                    # 记录解压信息
                    extracted_info[extract_name] = {
                        'source_zip': str(full_zip_path),
                        'extract_dir': str(extract_dir),
                        'files_count': len(list(extract_dir.rglob('*'))),
                        'size_mb': self._get_directory_size(extract_dir) / (1024 * 1024)
                    }
                    
                    print(f"    ✅ 成功解压到: {extract_dir}")
                    
                except Exception as e:
                    print(f"    ❌ 解压失败: {e}")
                    extracted_info[extract_name] = {'error': str(e)}
            else:
                print(f"    ⚠️  文件不存在: {zip_path}")
                extracted_info[extract_name] = {'error': 'File not found'}
        
        self.analysis_results['extraction_info'] = extracted_info
        return extracted_info
    
    def _get_directory_size(self, directory: Path) -> int:
        """计算目录大小"""
        total_size = 0
        for file_path in directory.rglob('*'):
            if file_path.is_file():
                total_size += file_path.stat().st_size
        return total_size
    
    def analyze_dataset_format(self, extract_dir: Path) -> Dict[str, Any]:
        """分析数据集格式"""
        analysis = {
            'format_type': 'unknown',
            'has_coco_annotation': False,
            'has_yolo_annotation': False,
            'image_formats': set(),
            'annotation_files': [],
            'directory_structure': [],
            'sample_count': 0
        }
        
        # 检查目录结构
        for item in extract_dir.rglob('*'):
            if item.is_file():
                # 检查文件类型
                suffix = item.suffix.lower()
                if suffix in ['.jpg', '.jpeg', '.png', '.bmp']:
                    analysis['image_formats'].add(suffix)
                    analysis['sample_count'] += 1
                elif suffix == '.json':
                    analysis['annotation_files'].append(str(item.relative_to(extract_dir)))
                    # 检查是否为COCO格式
                    if 'coco' in item.name.lower() or '_annotations' in item.name:
                        analysis['has_coco_annotation'] = True
                elif suffix == '.txt':
                    analysis['annotation_files'].append(str(item.relative_to(extract_dir)))
                    analysis['has_yolo_annotation'] = True
            elif item.is_dir():
                analysis['directory_structure'].append(str(item.relative_to(extract_dir)))
        
        # 转换set为list以便JSON序列化
        analysis['image_formats'] = list(analysis['image_formats'])
        
        # 判断格式类型
        if analysis['has_coco_annotation']:
            analysis['format_type'] = 'COCO'
        elif analysis['has_yolo_annotation']:
            analysis['format_type'] = 'YOLO'
        
        return analysis
    
    def analyze_coco_annotation(self, coco_file: Path) -> Dict[str, Any]:
        """分析COCO标注文件"""
        try:
            with open(coco_file, 'r', encoding='utf-8') as f:
                coco_data = json.load(f)
            
            analysis = {
                'images_count': len(coco_data.get('images', [])),
                'annotations_count': len(coco_data.get('annotations', [])),
                'categories': [],
                'category_distribution': {}
            }
            
            # 分析类别
            categories = coco_data.get('categories', [])
            for cat in categories:
                analysis['categories'].append({
                    'id': cat.get('id'),
                    'name': cat.get('name'),
                    'supercategory': cat.get('supercategory', '')
                })
            
            # 统计类别分布
            for ann in coco_data.get('annotations', []):
                cat_id = ann.get('category_id')
                if cat_id not in analysis['category_distribution']:
                    analysis['category_distribution'][cat_id] = 0
                analysis['category_distribution'][cat_id] += 1
            
            return analysis
            
        except Exception as e:
            return {'error': f'Failed to parse COCO file: {e}'}
    
    def analyze_unknown_categories(self) -> Dict[str, Any]:
        """分析未知物品类别映射"""
        txt_file = self.addition_path / "unkown/未知物txt(1).txt"
        
        if not txt_file.exists():
            return {'error': 'Unknown categories file not found'}
        
        try:
            with open(txt_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            categories = {}
            category_keywords = {}
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # 解析每行的类别信息
                parts = line.split()
                for part in parts:
                    if part.startswith('W00'):
                        # 提取类别编号
                        category_id = part[:4]  # W001, W002, etc.
                        if category_id not in categories:
                            categories[category_id] = set()
                            category_keywords[category_id] = []
                    elif len(part) > 1 and not part.isdigit():
                        # 提取关键词
                        if category_id in categories:
                            categories[category_id].add(part)
                            if part not in category_keywords[category_id]:
                                category_keywords[category_id].append(part)
            
            # 转换为可序列化的格式
            result = {
                'categories_found': list(categories.keys()),
                'category_keywords': {}
            }
            
            for cat_id, keywords in category_keywords.items():
                result['category_keywords'][cat_id] = keywords
            
            return result
            
        except Exception as e:
            return {'error': f'Failed to parse unknown categories: {e}'}

    def analyze_all_datasets(self):
        """分析所有数据集"""
        print("📊 开始分析数据集格式和内容...")

        # 分析每个解压的数据集
        for extract_name, info in self.analysis_results['extraction_info'].items():
            if 'error' in info:
                continue

            extract_dir = Path(info['extract_dir'])
            print(f"  分析: {extract_name}")

            # 基础格式分析
            format_analysis = self.analyze_dataset_format(extract_dir)

            # COCO格式特殊分析
            coco_analysis = None
            if format_analysis['has_coco_annotation']:
                for ann_file in format_analysis['annotation_files']:
                    if 'coco' in ann_file.lower() or 'annotation' in ann_file.lower():
                        coco_file = extract_dir / ann_file
                        if coco_file.exists():
                            coco_analysis = self.analyze_coco_annotation(coco_file)
                            break

            # 保存分析结果
            self.analysis_results[extract_name] = {
                'format_analysis': format_analysis,
                'coco_analysis': coco_analysis,
                'extraction_info': info
            }

        # 分析未知物品类别
        unknown_analysis = self.analyze_unknown_categories()
        self.analysis_results['unknown_categories'] = unknown_analysis

        print("✅ 数据集分析完成")

    def generate_compatibility_report(self) -> Dict[str, Any]:
        """生成兼容性报告"""
        report = {
            'overall_compatibility': 'good',
            'issues': [],
            'recommendations': [],
            'integration_strategy': {}
        }

        # 检查现有数据集格式
        existing_format = 'COCO'  # 基于现有competition_2025_dataset

        for dataset_name, analysis in self.analysis_results.items():
            if dataset_name in ['extraction_info', 'unknown_categories']:
                continue

            format_analysis = analysis.get('format_analysis', {})

            # 检查格式兼容性
            if format_analysis.get('format_type') != existing_format:
                if format_analysis.get('format_type') == 'YOLO':
                    report['issues'].append(f"{dataset_name}: YOLO格式需要转换为COCO格式")
                    report['recommendations'].append(f"使用现有的数据集整合器转换{dataset_name}")
                elif format_analysis.get('format_type') == 'unknown':
                    report['issues'].append(f"{dataset_name}: 未知格式，需要手动检查")

            # 检查图像格式
            image_formats = format_analysis.get('image_formats', [])
            if '.jpg' not in image_formats and '.jpeg' not in image_formats:
                report['issues'].append(f"{dataset_name}: 缺少标准JPG格式图像")

            # 生成整合策略
            report['integration_strategy'][dataset_name] = {
                'format_conversion_needed': format_analysis.get('format_type') != existing_format,
                'estimated_samples': format_analysis.get('sample_count', 0),
                'priority': 'high' if 'task2' in dataset_name.lower() else 'medium'
            }

        return report

    def generate_analysis_report(self):
        """生成完整的分析报告"""
        print("📝 生成分析报告...")

        # 生成兼容性报告
        compatibility_report = self.generate_compatibility_report()

        # 完整报告
        full_report = {
            'analysis_timestamp': datetime.datetime.now().isoformat(),
            'summary': {
                'total_datasets': len([k for k in self.analysis_results.keys()
                                     if k not in ['extraction_info', 'unknown_categories']]),
                'total_extracted_files': sum([info.get('files_count', 0)
                                            for info in self.analysis_results.get('extraction_info', {}).values()
                                            if 'files_count' in info]),
                'total_size_mb': sum([info.get('size_mb', 0)
                                    for info in self.analysis_results.get('extraction_info', {}).values()
                                    if 'size_mb' in info])
            },
            'detailed_analysis': self.analysis_results,
            'compatibility_report': compatibility_report,
            'next_steps': [
                "使用扩展的数据集整合器处理格式转换",
                "建立OCR关键词映射字典",
                "整合到现有competition_2025_dataset",
                "更新类别映射配置文件"
            ]
        }

        # 保存报告
        report_file = Path("addition_dataset_analysis_report.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(full_report, f, ensure_ascii=False, indent=2)

        print(f"✅ 分析报告已保存: {report_file}")

        # 打印摘要
        self.print_summary(full_report)

        return full_report

    def print_summary(self, report: Dict[str, Any]):
        """打印分析摘要"""
        print("\n" + "="*60)
        print("📊 Addition数据集分析摘要")
        print("="*60)

        summary = report['summary']
        print(f"总数据集数量: {summary['total_datasets']}")
        print(f"总文件数量: {summary['total_extracted_files']}")
        print(f"总大小: {summary['total_size_mb']:.2f} MB")

        print("\n🔍 数据集详情:")
        for name, analysis in report['detailed_analysis'].items():
            if name in ['extraction_info', 'unknown_categories']:
                continue

            format_info = analysis.get('format_analysis', {})
            print(f"  {name}:")
            print(f"    格式: {format_info.get('format_type', 'unknown')}")
            print(f"    样本数: {format_info.get('sample_count', 0)}")
            print(f"    图像格式: {', '.join(format_info.get('image_formats', []))}")

        print("\n📋 未知物品类别:")
        unknown_cats = report['detailed_analysis'].get('unknown_categories', {})
        if 'categories_found' in unknown_cats:
            print(f"  发现类别: {', '.join(unknown_cats['categories_found'])}")

        print("\n⚠️  兼容性问题:")
        issues = report['compatibility_report'].get('issues', [])
        if issues:
            for issue in issues:
                print(f"  - {issue}")
        else:
            print("  无兼容性问题")

        print("\n💡 建议:")
        recommendations = report['compatibility_report'].get('recommendations', [])
        for rec in recommendations:
            print(f"  - {rec}")

        print("\n🚀 下一步:")
        for step in report['next_steps']:
            print(f"  - {step}")

        print("="*60)

    def cleanup(self):
        """清理临时文件"""
        if self.temp_extract_dir.exists():
            shutil.rmtree(self.temp_extract_dir)
            print(f"🧹 已清理临时目录: {self.temp_extract_dir}")

def main():
    """主函数"""
    print("🚀 Addition数据集分析器启动")

    analyzer = AdditionDatasetAnalyzer()

    try:
        # 1. 解压所有zip文件
        analyzer.extract_zip_files()

        # 2. 分析所有数据集
        analyzer.analyze_all_datasets()

        # 3. 生成分析报告
        analyzer.generate_analysis_report()

        print("\n✅ 分析完成！")

    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        raise

    finally:
        # 清理临时文件
        analyzer.cleanup()

if __name__ == "__main__":
    main()
