#!/usr/bin/env python3
"""
增强数据集整合器
基于现有RGBDDatasetIntegrator，扩展支持Addition数据集的整合功能
"""

import os
import json
import shutil
import zipfile
from pathlib import Path
import cv2
import numpy as np
from typing import Dict, List, Tuple, Any
import datetime
from dataset_integration import RGBDDatasetIntegrator

class EnhancedDatasetIntegrator(RGBDDatasetIntegrator):
    """增强数据集整合器，支持Addition数据集"""
    
    def __init__(self, output_dir: str = "enhanced_competition_2025_dataset"):
        # 调用父类初始化
        super().__init__(output_dir)
        
        # 扩展类别映射支持W001-W008未知物品子类别
        self.enhanced_category_mapping = {
            # 原有8个基本类别
            'CA001': 0,  # 衣架
            'CA002': 1,  # 牙刷
            'CB001': 2,  # 果冻
            'CB002': 3,  # 长方形状饼干
            'CC001': 4,  # 罐装饮料
            'CC002': 5,  # 瓶装饮料
            'CD001': 6,  # 香蕉
            'CD002': 7,  # 橙子
            # 扩展的未知物品子类别
            'W001': 8,   # 数学类书籍
            'W002': 9,   # 物理类书籍
            'W003': 10,  # 药盒类
            'W004': 11,  # 电子类
            'W005': 12,  # 英语类书籍
            'W006': 13,  # 文学类书籍
            'W007': 14,  # 速食类
            'W008': 15,  # 文具类
            'Wxxx': 16   # 通用未知物品
        }
        
        # 更新类别映射
        self.category_mapping = self.enhanced_category_mapping
        
        # Addition数据集路径
        self.addition_path = Path("Addition")
        self.temp_extract_dir = Path("temp_addition_extract")
        
        # 统计信息
        self.integration_stats = {
            'processed_datasets': [],
            'total_images_added': 0,
            'category_distribution': {},
            'errors': []
        }
        
    def setup_temp_directory(self):
        """设置临时解压目录"""
        self.temp_extract_dir.mkdir(exist_ok=True)
        
    def cleanup_temp_directory(self):
        """清理临时目录"""
        if self.temp_extract_dir.exists():
            shutil.rmtree(self.temp_extract_dir)
            
    def extract_addition_datasets(self):
        """解压Addition数据集"""
        print("🔄 解压Addition数据集...")
        
        zip_files = [
            # Task2 旋转物体数据集
            ("Task2/19组.zip", "task2_19组"),
            ("Task2/分组标注.zip", "task2_分组标注"),
            # small 小样本数据集  
            ("small/8.6标注.zip", "small_8.6标注"),
            ("small/dataset_20250807_coco(小目标）.zip", "small_coco"),
            # unknown 未知物体数据集
            ("unkown/50未知物标注(1).zip", "unknown_50标注")
        ]
        
        extracted_dirs = {}
        
        for zip_path, extract_name in zip_files:
            full_zip_path = self.addition_path / zip_path
            extract_dir = self.temp_extract_dir / extract_name
            
            if full_zip_path.exists():
                try:
                    print(f"  解压: {zip_path}")
                    extract_dir.mkdir(exist_ok=True)
                    
                    with zipfile.ZipFile(full_zip_path, 'r') as zip_ref:
                        zip_ref.extractall(extract_dir)
                    
                    extracted_dirs[extract_name] = extract_dir
                    print(f"    ✅ 成功解压到: {extract_dir}")
                    
                except Exception as e:
                    print(f"    ❌ 解压失败: {e}")
                    self.integration_stats['errors'].append(f"解压失败 {zip_path}: {e}")
            else:
                print(f"    ⚠️  文件不存在: {zip_path}")
                
        return extracted_dirs
    
    def find_coco_annotation_file(self, dataset_dir: Path) -> Path:
        """查找COCO标注文件"""
        # 常见的COCO标注文件名模式
        patterns = [
            "_annotations.coco.json",
            "annotations.json", 
            "*coco*.json",
            "*annotation*.json"
        ]
        
        for pattern in patterns:
            files = list(dataset_dir.rglob(pattern))
            if files:
                return files[0]
                
        return None
    
    def process_addition_coco_dataset(self, dataset_dir: Path, dataset_name: str, 
                                    split_ratio: Tuple[float, float, float] = (0.7, 0.2, 0.1)):
        """处理Addition中的COCO格式数据集"""
        print(f"  处理COCO数据集: {dataset_name}")
        
        # 查找COCO标注文件
        coco_file = self.find_coco_annotation_file(dataset_dir)
        if not coco_file:
            error_msg = f"未找到COCO标注文件: {dataset_name}"
            print(f"    ❌ {error_msg}")
            self.integration_stats['errors'].append(error_msg)
            return 0
            
        try:
            # 加载COCO数据
            coco_data = self.load_coco_annotations(coco_file)
            
            # 创建映射
            image_id_to_filename = {img['id']: img['file_name'] for img in coco_data['images']}
            image_id_to_size = {img['id']: (img['width'], img['height']) for img in coco_data['images']}
            
            # 组织标注
            annotations_by_image = {}
            for ann in coco_data['annotations']:
                image_id = ann['image_id']
                if image_id not in annotations_by_image:
                    annotations_by_image[image_id] = []
                annotations_by_image[image_id].append(ann)
            
            # 查找图像文件
            image_files = []
            for img_info in coco_data['images']:
                filename = img_info['file_name']
                # 在数据集目录中递归查找图像
                image_paths = list(dataset_dir.rglob(filename))
                if not image_paths:
                    # 尝试不同的扩展名
                    base_name = Path(filename).stem
                    for ext in ['.jpg', '.jpeg', '.png']:
                        alt_paths = list(dataset_dir.rglob(f"{base_name}{ext}"))
                        if alt_paths:
                            image_paths = alt_paths
                            break
                
                if image_paths:
                    image_files.append((img_info['id'], image_paths[0]))
            
            if not image_files:
                error_msg = f"未找到图像文件: {dataset_name}"
                print(f"    ❌ {error_msg}")
                self.integration_stats['errors'].append(error_msg)
                return 0
            
            # 数据集分割
            total_images = len(image_files)
            train_count = int(total_images * split_ratio[0])
            val_count = int(total_images * split_ratio[1])
            
            splits = {
                'train': image_files[:train_count],
                'val': image_files[train_count:train_count + val_count],
                'test': image_files[train_count + val_count:]
            }
            
            # 处理每个分割
            processed_count = 0
            for split_name, split_files in splits.items():
                for image_id, image_path in split_files:
                    try:
                        filename = image_id_to_filename[image_id]
                        base_name = Path(filename).stem
                        
                        # 复制图像文件
                        new_image_name = f"{dataset_name}_{base_name}.jpg"
                        image_dst = self.output_dir / "images" / split_name / new_image_name
                        
                        # 读取并保存图像（确保格式统一）
                        image = cv2.imread(str(image_path))
                        if image is not None:
                            cv2.imwrite(str(image_dst), image)
                            
                            # 处理标注
                            if image_id in annotations_by_image:
                                img_width, img_height = image_id_to_size[image_id]
                                yolo_annotations = []
                                
                                for ann in annotations_by_image[image_id]:
                                    # 获取类别ID（这里需要根据实际情况映射）
                                    category_id = ann['category_id']
                                    
                                    # 转换bbox格式
                                    bbox = self.coco_to_yolo_bbox(ann['bbox'], img_width, img_height)
                                    yolo_annotations.append(f"{category_id} {' '.join(map(str, bbox))}")
                                
                                # 保存YOLO标注
                                if yolo_annotations:
                                    label_file = self.output_dir / "labels" / split_name / f"{dataset_name}_{base_name}.txt"
                                    with open(label_file, 'w') as f:
                                        f.write('\n'.join(yolo_annotations))
                            
                            processed_count += 1
                            
                    except Exception as e:
                        error_msg = f"处理图像失败 {filename}: {e}"
                        print(f"      ⚠️  {error_msg}")
                        self.integration_stats['errors'].append(error_msg)
            
            print(f"    ✅ 成功处理 {processed_count} 张图像")
            return processed_count
            
        except Exception as e:
            error_msg = f"处理数据集失败 {dataset_name}: {e}"
            print(f"    ❌ {error_msg}")
            self.integration_stats['errors'].append(error_msg)
            return 0

    def process_addition_datasets(self):
        """处理所有Addition数据集"""
        print("🚀 开始处理Addition数据集...")

        # 设置临时目录
        self.setup_temp_directory()

        try:
            # 解压数据集
            extracted_dirs = self.extract_addition_datasets()

            if not extracted_dirs:
                print("❌ 没有成功解压任何数据集")
                return

            # 处理每个数据集
            total_processed = 0

            for dataset_name, dataset_dir in extracted_dirs.items():
                print(f"\n📂 处理数据集: {dataset_name}")

                # 检查是否为COCO格式
                coco_file = self.find_coco_annotation_file(dataset_dir)
                if coco_file:
                    processed_count = self.process_addition_coco_dataset(dataset_dir, dataset_name)
                    total_processed += processed_count

                    self.integration_stats['processed_datasets'].append({
                        'name': dataset_name,
                        'format': 'COCO',
                        'images_processed': processed_count,
                        'status': 'success' if processed_count > 0 else 'failed'
                    })
                else:
                    print(f"    ⚠️  跳过非COCO格式数据集: {dataset_name}")
                    self.integration_stats['processed_datasets'].append({
                        'name': dataset_name,
                        'format': 'unknown',
                        'images_processed': 0,
                        'status': 'skipped'
                    })

            self.integration_stats['total_images_added'] = total_processed
            print(f"\n✅ 总共处理了 {total_processed} 张图像")

        finally:
            # 清理临时目录
            self.cleanup_temp_directory()

    def update_category_mapping_for_unknown_objects(self):
        """更新类别映射以支持未知物品子类别"""
        # 基于分析报告中的OCR关键词映射
        unknown_category_keywords = {
            'W001': ['数学', '高等数学', '工程数学', '概率论', '数理统计'],
            'W002': ['物理', '电路', '电磁场', '电磁波'],
            'W003': ['胶囊', '颗粒', '口服液', '药盒'],
            'W004': ['xiaomi', '南孚', '华为', '电子'],
            'W005': ['英语', '雅思', 'IELTS'],
            'W006': ['思想', '毛泽东', '文学'],
            'W007': ['方便面', '牛肉面', '速食'],
            'W008': ['订书钉', '墨水', 'ink', '修正带', '笔芯', '文具']
        }

        return unknown_category_keywords

    def create_enhanced_dataset_yaml(self):
        """创建增强的数据集配置文件"""
        # 获取类别名称映射
        id_to_name = {v: k for k, v in self.enhanced_category_mapping.items()}
        category_names = [id_to_name[i] for i in range(len(self.enhanced_category_mapping))]

        yaml_content = f"""# Enhanced Competition 2025 RGBD Dataset Configuration
# Generated on: {datetime.datetime.now().isoformat()}
path: {self.output_dir.absolute()}
train: images/train
val: images/val
test: images/test

# Enhanced Classes (8 basic classes + 8 unknown subcategories + 1 generic unknown)
nc: {len(self.enhanced_category_mapping)}
names: {category_names}

# Depth image paths
depth_train: depth/train
depth_val: depth/val
depth_test: depth/test

# Competition specific configuration
competition_format: true
output_format: "ID;Num"

# Unknown object categories with OCR keywords
unknown_categories:
  W001: "数学类书籍"
  W002: "物理类书籍"
  W003: "药盒类"
  W004: "电子类"
  W005: "英语类书籍"
  W006: "文学类书籍"
  W007: "速食类"
  W008: "文具类"

# OCR keyword mapping for unknown objects
ocr_keywords:
  W001: ["数学", "高等数学", "工程数学", "概率论", "数理统计"]
  W002: ["物理", "电路", "电磁场", "电磁波"]
  W003: ["胶囊", "颗粒", "口服液", "药盒"]
  W004: ["xiaomi", "南孚", "华为", "电子"]
  W005: ["英语", "雅思", "IELTS"]
  W006: ["思想", "毛泽东", "文学"]
  W007: ["方便面", "牛肉面", "速食"]
  W008: ["订书钉", "墨水", "ink", "修正带", "笔芯", "文具"]
"""

        # 保存配置文件
        yaml_file = self.output_dir / "enhanced_dataset.yaml"
        with open(yaml_file, 'w', encoding='utf-8') as f:
            f.write(yaml_content)

        print(f"✅ 增强数据集配置已保存: {yaml_file}")
        return yaml_file

    def generate_integration_statistics(self):
        """生成整合统计信息"""
        # 统计各分割的图像数量
        splits_stats = {}
        for split in ['train', 'val', 'test']:
            images_dir = self.output_dir / "images" / split
            labels_dir = self.output_dir / "labels" / split

            if images_dir.exists():
                image_count = len(list(images_dir.glob('*.jpg'))) + len(list(images_dir.glob('*.png')))
                label_count = len(list(labels_dir.glob('*.txt'))) if labels_dir.exists() else 0

                splits_stats[split] = {
                    'images': image_count,
                    'labels': label_count
                }

        # 完整统计信息
        stats = {
            'integration_timestamp': datetime.datetime.now().isoformat(),
            'output_directory': str(self.output_dir),
            'total_categories': len(self.enhanced_category_mapping),
            'category_mapping': self.enhanced_category_mapping,
            'splits_statistics': splits_stats,
            'addition_datasets': self.integration_stats['processed_datasets'],
            'total_images_added': self.integration_stats['total_images_added'],
            'errors': self.integration_stats['errors']
        }

        # 保存统计文件
        stats_file = self.output_dir / "enhanced_dataset_stats.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)

        print(f"✅ 整合统计信息已保存: {stats_file}")

        # 打印摘要
        self.print_integration_summary(stats)

        return stats

    def print_integration_summary(self, stats: Dict[str, Any]):
        """打印整合摘要"""
        print("\n" + "="*60)
        print("📊 数据集整合摘要")
        print("="*60)

        print(f"输出目录: {stats['output_directory']}")
        print(f"总类别数: {stats['total_categories']}")
        print(f"新增图像: {stats['total_images_added']}")

        print("\n📂 数据分割统计:")
        for split, split_stats in stats['splits_statistics'].items():
            print(f"  {split}: {split_stats['images']} 图像, {split_stats['labels']} 标注")

        print("\n📋 处理的Addition数据集:")
        for dataset in stats['addition_datasets']:
            status_icon = "✅" if dataset['status'] == 'success' else "⚠️" if dataset['status'] == 'skipped' else "❌"
            print(f"  {status_icon} {dataset['name']}: {dataset['images_processed']} 图像 ({dataset['status']})")

        if stats['errors']:
            print(f"\n⚠️  错误 ({len(stats['errors'])}):")
            for error in stats['errors'][:5]:  # 只显示前5个错误
                print(f"  - {error}")
            if len(stats['errors']) > 5:
                print(f"  ... 还有 {len(stats['errors']) - 5} 个错误")

        print("="*60)

def main():
    """主函数"""
    print("🚀 增强数据集整合器启动")

    # 创建整合器实例
    integrator = EnhancedDatasetIntegrator()

    try:
        # 1. 处理Addition数据集
        integrator.process_addition_datasets()

        # 2. 创建增强的数据集配置文件
        integrator.create_enhanced_dataset_yaml()

        # 3. 生成整合统计信息
        integrator.generate_integration_statistics()

        print("\n✅ 数据集整合完成！")

    except Exception as e:
        print(f"❌ 整合过程中出现错误: {e}")
        raise

if __name__ == "__main__":
    main()
