#!/usr/bin/env python3
"""
创建包含文字的测试图像，用于验证OCR系统
"""

import cv2
import numpy as np
from pathlib import Path
import json

def create_text_image(text, filename, size=(200, 100), font_scale=1.0):
    """创建包含指定文字的图像"""
    # 创建白色背景
    img = np.ones((size[1], size[0], 3), dtype=np.uint8) * 255
    
    # 设置文字参数
    font = cv2.FONT_HERSHEY_SIMPLEX
    thickness = 2
    color = (0, 0, 0)  # 黑色文字
    
    # 计算文字位置（居中）
    text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
    text_x = (size[0] - text_size[0]) // 2
    text_y = (size[1] + text_size[1]) // 2
    
    # 绘制文字
    cv2.putText(img, text, (text_x, text_y), font, font_scale, color, thickness)
    
    return img

def create_book_like_image(title, author, filename, size=(300, 200)):
    """创建类似书籍封面的图像"""
    # 创建彩色背景
    img = np.ones((size[1], size[0], 3), dtype=np.uint8)
    img[:, :] = [240, 240, 240]  # 浅灰色背景
    
    # 添加边框
    cv2.rectangle(img, (10, 10), (size[0]-10, size[1]-10), (100, 100, 100), 2)
    
    # 设置字体
    font = cv2.FONT_HERSHEY_SIMPLEX
    
    # 绘制标题
    title_scale = 0.8
    title_thickness = 2
    title_color = (0, 0, 0)
    
    title_size = cv2.getTextSize(title, font, title_scale, title_thickness)[0]
    title_x = (size[0] - title_size[0]) // 2
    title_y = size[1] // 3
    
    cv2.putText(img, title, (title_x, title_y), font, title_scale, title_color, title_thickness)
    
    # 绘制作者
    if author:
        author_scale = 0.5
        author_thickness = 1
        author_color = (50, 50, 50)
        
        author_size = cv2.getTextSize(author, font, author_scale, author_thickness)[0]
        author_x = (size[0] - author_size[0]) // 2
        author_y = title_y + 50
        
        cv2.putText(img, author, (author_x, author_y), font, author_scale, author_color, author_thickness)
    
    return img

def main():
    print("=== 创建OCR测试图像 ===")
    
    # 创建测试图像目录
    test_dir = Path("ocr_test_images")
    test_dir.mkdir(exist_ok=True)
    
    # 创建各种测试图像
    test_cases = [
        # 数学类
        {"text": "高等数学", "filename": "math_book_1.jpg", "category": "数学"},
        {"text": "线性代数", "filename": "math_book_2.jpg", "category": "数学"},
        {"text": "Mathematics", "filename": "math_book_3.jpg", "category": "数学"},
        
        # 物理类
        {"text": "大学物理", "filename": "physics_book_1.jpg", "category": "物理"},
        {"text": "Physics", "filename": "physics_book_2.jpg", "category": "物理"},
        
        # 计算机类
        {"text": "Python编程", "filename": "cs_book_1.jpg", "category": "计算机"},
        {"text": "Computer Science", "filename": "cs_book_2.jpg", "category": "计算机"},
        
        # 英语类
        {"text": "英语词汇", "filename": "english_book_1.jpg", "category": "英语"},
        {"text": "English Grammar", "filename": "english_book_2.jpg", "category": "英语"},
        
        # 文学类
        {"text": "中国文学", "filename": "literature_book_1.jpg", "category": "文学"},
        {"text": "Literature", "filename": "literature_book_2.jpg", "category": "文学"},
    ]
    
    created_images = []
    
    print(f"创建 {len(test_cases)} 个测试图像...")
    
    for i, case in enumerate(test_cases, 1):
        print(f"[{i:2d}/{len(test_cases)}] 创建: {case['text']} -> {case['filename']}")
        
        # 创建书籍样式的图像
        img = create_book_like_image(case['text'], "", case['filename'])
        
        # 保存图像
        img_path = test_dir / case['filename']
        cv2.imwrite(str(img_path), img)
        
        created_images.append({
            'filename': case['filename'],
            'text': case['text'],
            'expected_category': case['category'],
            'path': str(img_path)
        })
    
    # 保存测试用例信息
    test_info = {
        'description': 'OCR测试图像集合',
        'total_images': len(created_images),
        'categories': list(set(case['category'] for case in test_cases)),
        'images': created_images
    }
    
    info_file = test_dir / "test_info.json"
    with open(info_file, 'w', encoding='utf-8') as f:
        json.dump(test_info, f, indent=2, ensure_ascii=False)
    
    print(f"\n✅ 创建完成!")
    print(f"📁 图像保存目录: {test_dir}")
    print(f"📊 创建图像数量: {len(created_images)}")
    print(f"📋 测试信息文件: {info_file}")
    
    # 显示类别统计
    category_counts = {}
    for case in test_cases:
        category_counts[case['category']] = category_counts.get(case['category'], 0) + 1
    
    print(f"\n📈 类别分布:")
    for category, count in category_counts.items():
        print(f"  {category}: {count} 张")
    
    return test_dir

if __name__ == "__main__":
    main()
