#!/usr/bin/env python3
"""
更新标注文件中的文件名以匹配重命名后的图像文件
"""

import json
import os
from pathlib import Path

def create_filename_mapping():
    """创建原始文件名到新文件名的映射"""
    base_111 = Path("/home/<USER>/claude/SpatialVLA/3D/111")
    
    # 映射字典：原始文件名 -> 新文件名
    filename_mapping = {}
    
    # 处理d1目录
    d1_dir = base_111 / 'd1' / 'images（rgb+深度）'
    if d1_dir.exists():
        for file_path in d1_dir.iterdir():
            if file_path.suffix.lower() == '.jpg':
                original_name = file_path.name
                new_name = f"d1_{original_name}"
                filename_mapping[original_name] = new_name
    
    # 处理d2目录
    d2_dir = base_111 / 'd2' / 'dataset' / 'images（rgb+深度）'
    if d2_dir.exists():
        for file_path in d2_dir.iterdir():
            if file_path.suffix.lower() == '.jpg':
                original_name = file_path.name
                new_name = f"d2_{original_name}"
                filename_mapping[original_name] = new_name
    
    # 处理d3目录
    d3_dir = base_111 / 'd3' / 'images'
    if d3_dir.exists():
        for file_path in d3_dir.iterdir():
            if file_path.suffix.lower() == '.jpg':
                original_name = file_path.name
                new_name = f"d3_{original_name}"
                filename_mapping[original_name] = new_name
    
    # 处理d4目录
    d4_dir = base_111 / 'd4' / 'dataset' / 'images（rgb+深度）'
    if d4_dir.exists():
        for file_path in d4_dir.iterdir():
            if file_path.suffix.lower() == '.jpg':
                original_name = file_path.name
                new_name = f"d4_{original_name}"
                filename_mapping[original_name] = new_name
    
    # 处理dataset目录
    dataset_dir = base_111 / 'dataset' / 'images'
    if dataset_dir.exists():
        for file_path in dataset_dir.iterdir():
            if file_path.suffix.lower() == '.jpg':
                original_name = file_path.name
                new_name = f"dataset_{original_name}"
                filename_mapping[original_name] = new_name
    
    return filename_mapping

def update_annotation_file(annotation_path, filename_mapping):
    """更新标注文件中的文件名"""
    print(f"更新标注文件: {annotation_path}")
    
    with open(annotation_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    updated_count = 0
    not_found_count = 0
    
    # 更新images部分的file_name
    for img in data.get('images', []):
        original_filename = img.get('file_name', '')
        if original_filename in filename_mapping:
            new_filename = filename_mapping[original_filename]
            img['file_name'] = new_filename
            updated_count += 1
            print(f"  更新: {original_filename} -> {new_filename}")
        else:
            not_found_count += 1
            print(f"  未找到映射: {original_filename}")
    
    # 保存更新后的文件
    with open(annotation_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    
    print(f"更新完成: {updated_count} 个文件名已更新, {not_found_count} 个文件名未找到映射")
    return updated_count, not_found_count

def main():
    """主函数"""
    print("开始更新标注文件中的文件名...")
    
    # 创建文件名映射
    print("创建文件名映射...")
    filename_mapping = create_filename_mapping()
    print(f"创建了 {len(filename_mapping)} 个文件名映射")
    
    # 显示一些映射示例
    print("\n映射示例:")
    for i, (original, new) in enumerate(list(filename_mapping.items())[:10]):
        print(f"  {original} -> {new}")
    if len(filename_mapping) > 10:
        print(f"  ... 还有 {len(filename_mapping) - 10} 个映射")
    
    # 更新标注文件
    annotation_path = Path("/home/<USER>/claude/SpatialVLA/3D/reorganized_dataset/labels/train/annotations.json")
    if annotation_path.exists():
        total_updated, total_not_found = update_annotation_file(annotation_path, filename_mapping)
        print(f"\n总计: {total_updated} 个文件名已更新, {total_not_found} 个文件名未找到映射")
    else:
        print(f"标注文件不存在: {annotation_path}")
    
    print("标注文件更新完成!")

if __name__ == "__main__":
    main()
