#!/usr/bin/env python3
"""
2025年比赛RGB-D YOLO训练脚本
针对新比赛规则优化的轻量化模型训练
"""

import os
import time
import torch
import yaml
from pathlib import Path
from ultralytics import YOLO
import json

class Competition2025Trainer:
    def __init__(self, dataset_path="competition_2025_dataset", output_dir="competition_2025_models"):
        self.dataset_path = Path(dataset_path)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 比赛特定配置
        self.competition_config = {
            "target_platform": "香橙派 AI pro 8T",
            "memory_limit": "16GB",
            "camera": "奥比中光 Astra Pro Plus RGBD",
            "max_loading_time": "30s",  # 模型加载时间限制
            "inference_time_target": "< 5s",  # 推理时间目标
            "categories": 9,  # 8个基本类别 + 未知物品
        }
        
    def create_optimized_config(self, model_name="yolo11n"):
        """创建优化的训练配置"""
        config = {
            # 基础配置
            "model": f"{model_name}.pt",
            "data": str(self.dataset_path / "competition_dataset.yaml"),
            
            # 训练参数 - 针对比赛优化
            "epochs": 150,  # 增加训练轮数以提高精度
            "batch": 16,    # 适中的批次大小
            "imgsz": 640,   # 标准输入尺寸
            "device": 0 if torch.cuda.is_available() else "cpu",
            
            # 优化参数
            "optimizer": "AdamW",
            "lr0": 0.001,
            "lrf": 0.01,
            "momentum": 0.937,
            "weight_decay": 0.0005,
            "warmup_epochs": 3,
            "warmup_momentum": 0.8,
            "warmup_bias_lr": 0.1,
            
            # 数据增强 - 适应比赛环境
            "hsv_h": 0.015,    # 色调变化（适应不同光照）
            "hsv_s": 0.7,      # 饱和度变化
            "hsv_v": 0.4,      # 亮度变化（适应特定光源）
            "degrees": 15.0,   # 旋转（适应转动台）
            "translate": 0.1,  # 平移
            "scale": 0.5,      # 缩放
            "shear": 0.0,      # 剪切
            "perspective": 0.0, # 透视变换
            "flipud": 0.0,     # 上下翻转
            "fliplr": 0.5,     # 左右翻转
            "mosaic": 1.0,     # 马赛克增强
            "mixup": 0.0,      # 混合增强
            "copy_paste": 0.0, # 复制粘贴增强
            
            # 验证和保存
            "val": True,
            "save": True,
            "save_period": 10,
            "plots": True,
            "patience": 30,
            
            # 比赛特定
            "project": str(self.output_dir),
            "name": f"competition_2025_{model_name}",
            "exist_ok": True,
        }
        
        return config
        
    def train_model(self, model_name="yolo11n", description=""):
        """训练单个模型"""
        print(f"\n{'='*60}")
        print(f"开始训练: {model_name}")
        print(f"描述: {description}")
        print(f"{'='*60}")
        
        try:
            # 创建训练配置
            config = self.create_optimized_config(model_name)
            
            # 加载模型
            model = YOLO(config["model"])
            
            # 记录开始时间
            start_time = time.time()
            
            # 开始训练
            results = model.train(**{k: v for k, v in config.items() 
                                   if k not in ["model"]})
            
            # 记录结束时间
            end_time = time.time()
            training_time = end_time - start_time
            
            # 测试模型加载时间
            model_path = self.output_dir / f"competition_2025_{model_name}" / "weights" / "best.pt"
            loading_start = time.time()
            test_model = YOLO(str(model_path))
            loading_time = time.time() - loading_start
            
            # 在验证集上评估
            val_results = test_model.val(data=config["data"], split='val')
            
            # 测试推理速度
            test_image = self.dataset_path / "images" / "val"
            test_images = list(test_image.glob("*.jpg"))
            if test_images:
                inference_start = time.time()
                _ = test_model(str(test_images[0]))
                inference_time = time.time() - inference_start
            else:
                inference_time = 0
            
            # 保存训练结果
            training_results = {
                "model_name": model_name,
                "description": description,
                "training_time_hours": training_time / 3600,
                "model_loading_time_seconds": loading_time,
                "inference_time_seconds": inference_time,
                "metrics": {
                    "mAP50": float(val_results.box.map50),
                    "mAP50_95": float(val_results.box.map),
                    "precision": float(val_results.box.mp),
                    "recall": float(val_results.box.mr),
                },
                "competition_compliance": {
                    "loading_time_ok": loading_time < 30,  # 30秒限制
                    "inference_time_ok": inference_time < 5,  # 5秒目标
                    "memory_efficient": True,  # 需要实际测试
                },
                "model_path": str(model_path),
                "config": config
            }
            
            # 保存结果
            result_file = self.output_dir / f"training_results_{model_name}.json"
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(training_results, f, indent=2, ensure_ascii=False)
            
            print(f"\n训练完成: {model_name}")
            print(f"mAP50: {training_results['metrics']['mAP50']:.4f}")
            print(f"mAP50-95: {training_results['metrics']['mAP50_95']:.4f}")
            print(f"模型加载时间: {loading_time:.2f}s")
            print(f"推理时间: {inference_time:.3f}s")
            print(f"训练时间: {training_time/3600:.2f}小时")
            
            return training_results
            
        except Exception as e:
            print(f"训练失败: {e}")
            return None
    
    def train_multiple_models(self):
        """训练多个模型进行对比"""
        models_to_train = [
            ("yolo11n", "最新YOLO11纳米版 - 最轻量"),
            ("yolo11s", "最新YOLO11小型版 - 平衡性能"),
            ("yolov8n", "YOLOv8纳米版 - 基线对比"),
            ("yolov8s", "YOLOv8小型版 - 性能对比"),
        ]
        
        all_results = {}
        
        for model_name, description in models_to_train:
            result = self.train_model(model_name, description)
            if result:
                all_results[model_name] = result
        
        # 生成对比报告
        self.generate_comparison_report(all_results)
        
        return all_results
    
    def generate_comparison_report(self, results):
        """生成模型对比报告"""
        print(f"\n{'='*80}")
        print("2025年比赛模型对比报告")
        print(f"{'='*80}")
        
        if not results:
            print("没有成功的训练结果")
            return
        
        # 按mAP50排序
        sorted_results = sorted(results.items(), 
                              key=lambda x: x[1]['metrics']['mAP50'], reverse=True)
        
        # 生成表格
        print(f"{'模型':<12} {'mAP50':<8} {'mAP50-95':<10} {'加载时间(s)':<12} {'推理时间(s)':<12} {'合规性':<8}")
        print("-" * 80)
        
        for model_name, result in sorted_results:
            metrics = result['metrics']
            loading_time = result['model_loading_time_seconds']
            inference_time = result['inference_time_seconds']
            compliance = "✓" if (result['competition_compliance']['loading_time_ok'] and 
                               result['competition_compliance']['inference_time_ok']) else "✗"
            
            print(f"{model_name:<12} {metrics['mAP50']:<8.4f} {metrics['mAP50_95']:<10.4f} "
                  f"{loading_time:<12.2f} {inference_time:<12.3f} {compliance:<8}")
        
        # 推荐最佳模型
        best_model = sorted_results[0]
        print(f"\n推荐模型: {best_model[0]}")
        print(f"理由: mAP50={best_model[1]['metrics']['mAP50']:.4f}, "
              f"加载时间={best_model[1]['model_loading_time_seconds']:.2f}s")
        
        # 保存详细报告
        report = {
            "competition_info": self.competition_config,
            "training_summary": {
                "total_models": len(results),
                "best_model": best_model[0],
                "best_mAP50": best_model[1]['metrics']['mAP50']
            },
            "detailed_results": results
        }
        
        report_file = self.output_dir / "competition_2025_training_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n详细报告保存在: {report_file}")
        
        # 生成Markdown报告
        self.generate_markdown_report(sorted_results, report)
    
    def generate_markdown_report(self, sorted_results, report):
        """生成Markdown格式的报告"""
        md_content = f"""# 2025年比赛RGB-D YOLO模型训练报告

## 比赛信息
- **目标平台**: {self.competition_config['target_platform']}
- **内存限制**: {self.competition_config['memory_limit']}
- **相机**: {self.competition_config['camera']}
- **模型加载时间限制**: {self.competition_config['max_loading_time']}
- **推理时间目标**: {self.competition_config['inference_time_target']}

## 训练结果排名

| 排名 | 模型 | mAP50 | mAP50-95 | 精确率 | 召回率 | 加载时间(s) | 推理时间(s) | 合规性 |
|------|------|-------|----------|--------|--------|-------------|-------------|--------|
"""
        
        for i, (model_name, result) in enumerate(sorted_results, 1):
            metrics = result['metrics']
            loading_time = result['model_loading_time_seconds']
            inference_time = result['inference_time_seconds']
            compliance = "✓" if (result['competition_compliance']['loading_time_ok'] and 
                               result['competition_compliance']['inference_time_ok']) else "✗"
            
            md_content += f"| {i} | {model_name} | {metrics['mAP50']:.4f} | {metrics['mAP50_95']:.4f} | "
            md_content += f"{metrics['precision']:.4f} | {metrics['recall']:.4f} | "
            md_content += f"{loading_time:.2f} | {inference_time:.3f} | {compliance} |\n"
        
        md_content += f"""
## 推荐方案

**最佳模型**: {sorted_results[0][0]}
- mAP50: {sorted_results[0][1]['metrics']['mAP50']:.4f}
- mAP50-95: {sorted_results[0][1]['metrics']['mAP50_95']:.4f}
- 模型加载时间: {sorted_results[0][1]['model_loading_time_seconds']:.2f}s
- 推理时间: {sorted_results[0][1]['inference_time_seconds']:.3f}s

## 比赛适配建议

1. **模型部署**: 使用推荐的最佳模型
2. **加载优化**: 预加载模型以减少比赛时的加载时间
3. **推理优化**: 使用模型量化和TensorRT加速
4. **环境适配**: 针对转动台和特定光源进行测试

## 下一步工作

1. 实现文字识别功能（用于未知物品分类）
2. 创建比赛输出格式（ID;Num）
3. 实现网络通讯协议
4. 在目标硬件上进行性能测试
"""
        
        md_file = self.output_dir / "competition_2025_training_report.md"
        with open(md_file, 'w', encoding='utf-8') as f:
            f.write(md_content)
        
        print(f"Markdown报告保存在: {md_file}")

def main():
    # 检查数据集是否存在
    dataset_path = "competition_2025_dataset"
    if not Path(dataset_path).exists():
        print(f"错误: 比赛数据集不存在: {dataset_path}")
        print("请先运行 competition_2025_dataset_preparation.py")
        return

    print("=== 2025年比赛RGB-D YOLO模型训练 ===")

    # 创建训练器
    trainer = Competition2025Trainer(dataset_path)

    # 先训练一个YOLOv11n模型进行快速验证
    print("开始训练YOLOv11n模型...")
    result = trainer.train_model("yolo11n", "2025年比赛优化版YOLOv11n")

    if result:
        print(f"\n快速训练完成!")
        print(f"mAP50: {result['metrics']['mAP50']:.4f}")
        print(f"模型加载时间: {result['model_loading_time_seconds']:.2f}s")
        print(f"推理时间: {result['inference_time_seconds']:.3f}s")

        # 如果需要训练更多模型，可以取消注释下面的代码
        # print("\n继续训练其他模型进行对比...")
        # results = trainer.train_multiple_models()

    print("\n=== 训练完成 ===")
    print("模型已训练完成，可以开始实现推理系统")

if __name__ == "__main__":
    main()
