#!/usr/bin/env python3
"""
全面的模型评估系统
对所有训练的模型进行完整的准确率评估和对比分析
"""

import os
import time
import json
import numpy as np
from pathlib import Path
from ultralytics import YOLO
import pandas as pd
import matplotlib.pyplot as plt

class ComprehensiveModelEvaluator:
    def __init__(self, dataset_path="competition_2025_dataset"):
        self.dataset_path = Path(dataset_path)
        self.models = {}
        self.evaluation_results = {}
        
        # 比赛类别映射
        self.category_names = [
            'CA001_衣架', 'CA002_牙刷', 'CB001_果冻', 'CB002_长方形状饼干',
            'CC001_罐装饮料', 'CC002_瓶装饮料', 'CD001_香蕉', 'CD002_橙子', 'Wxxx_未知物品'
        ]
        
        # 困难类别
        self.difficult_categories = ['CA002_牙刷', 'CB001_果冻']
        
    def load_all_models(self):
        """加载所有可用的训练模型"""
        model_candidates = {
            "基线_YOLOv11n": "competition_2025_models/competition_2025_yolo11n/weights/best.pt",
            "优化_YOLOv11n": "optimized_models/class_weighted_yolo11n_thin_small/weights/best.pt",
            "精细化_stage1": "refined_models/refined_yolo11n_stage1_balanced/weights/best.pt",
            "精细化_stage2": "refined_models/refined_yolo11n_stage2_focused/weights/best.pt",
            "优化_YOLOv11s": "optimized_models_s/class_weighted_yolo11s_thin_small/weights/best.pt"
        }
        
        print("📦 加载所有可用模型...")
        for model_name, model_path in model_candidates.items():
            if os.path.exists(model_path):
                try:
                    print(f"  加载: {model_name}")
                    start_time = time.time()
                    model = YOLO(model_path)
                    loading_time = time.time() - start_time
                    
                    self.models[model_name] = {
                        'model': model,
                        'path': model_path,
                        'loading_time': loading_time
                    }
                    print(f"    ✅ 成功 ({loading_time:.3f}s)")
                    
                except Exception as e:
                    print(f"    ❌ 失败: {e}")
            else:
                print(f"  ⚠️  {model_name}: 模型文件不存在")
                
        print(f"\n成功加载 {len(self.models)} 个模型")
        
    def evaluate_single_model(self, model_name, model_info):
        """评估单个模型"""
        print(f"\n🔍 评估模型: {model_name}")
        
        model = model_info['model']
        dataset_config = self.dataset_path / "competition_dataset.yaml"
        
        if not dataset_config.exists():
            print(f"  ❌ 数据集配置文件不存在: {dataset_config}")
            return None
            
        try:
            # 在验证集上评估
            print("  📊 在验证集上评估...")
            start_time = time.time()
            val_results = model.val(data=str(dataset_config), split='val', verbose=False)
            eval_time = time.time() - start_time
            
            # 提取基本指标
            basic_metrics = {
                'mAP50': float(val_results.box.map50),
                'mAP50_95': float(val_results.box.map),
                'precision': float(val_results.box.mp),
                'recall': float(val_results.box.mr),
                'evaluation_time': eval_time
            }
            
            # 提取各类别指标
            class_metrics = {}
            if hasattr(val_results.box, 'maps') and val_results.box.maps is not None:
                for i, class_map in enumerate(val_results.box.maps):
                    if i < len(self.category_names):
                        class_metrics[self.category_names[i]] = float(class_map)
            
            # 提取各类别精确率和召回率
            class_precision = {}
            class_recall = {}
            if hasattr(val_results.box, 'p') and val_results.box.p is not None:
                for i, p in enumerate(val_results.box.p):
                    if i < len(self.category_names):
                        class_precision[self.category_names[i]] = float(p)
                        
            if hasattr(val_results.box, 'r') and val_results.box.r is not None:
                for i, r in enumerate(val_results.box.r):
                    if i < len(self.category_names):
                        class_recall[self.category_names[i]] = float(r)
            
            # 在测试集上评估
            print("  📊 在测试集上评估...")
            test_results = model.val(data=str(dataset_config), split='test', verbose=False)
            
            test_metrics = {
                'mAP50': float(test_results.box.map50),
                'mAP50_95': float(test_results.box.map),
                'precision': float(test_results.box.mp),
                'recall': float(test_results.box.mr)
            }
            
            # 汇总结果
            evaluation_result = {
                'model_name': model_name,
                'model_path': model_info['path'],
                'loading_time': model_info['loading_time'],
                'evaluation_time': eval_time,
                'validation_metrics': basic_metrics,
                'test_metrics': test_metrics,
                'class_metrics': class_metrics,
                'class_precision': class_precision,
                'class_recall': class_recall
            }
            
            print(f"    ✅ 验证集 mAP50: {basic_metrics['mAP50']:.4f}")
            print(f"    ✅ 测试集 mAP50: {test_metrics['mAP50']:.4f}")
            
            return evaluation_result
            
        except Exception as e:
            print(f"    ❌ 评估失败: {e}")
            return None
            
    def evaluate_all_models(self):
        """评估所有模型"""
        print(f"\n{'='*60}")
        print("开始全面模型评估")
        print(f"{'='*60}")
        
        for model_name, model_info in self.models.items():
            result = self.evaluate_single_model(model_name, model_info)
            if result:
                self.evaluation_results[model_name] = result
                
        print(f"\n✅ 评估完成，成功评估 {len(self.evaluation_results)} 个模型")
        
    def generate_comparison_report(self):
        """生成详细的对比报告"""
        if not self.evaluation_results:
            print("没有评估结果可供分析")
            return
            
        print(f"\n{'='*80}")
        print("📊 详细模型对比报告")
        print(f"{'='*80}")
        
        # 创建对比表格
        comparison_data = []
        
        for model_name, result in self.evaluation_results.items():
            val_metrics = result['validation_metrics']
            test_metrics = result['test_metrics']
            
            row = {
                '模型': model_name,
                '验证集_mAP50': val_metrics['mAP50'],
                '验证集_mAP50-95': val_metrics['mAP50_95'],
                '验证集_精确率': val_metrics['precision'],
                '验证集_召回率': val_metrics['recall'],
                '测试集_mAP50': test_metrics['mAP50'],
                '测试集_mAP50-95': test_metrics['mAP50_95'],
                '测试集_精确率': test_metrics['precision'],
                '测试集_召回率': test_metrics['recall'],
                '加载时间(s)': result['loading_time'],
                '评估时间(s)': result['evaluation_time']
            }
            comparison_data.append(row)
        
        # 按验证集mAP50排序
        comparison_data.sort(key=lambda x: x['验证集_mAP50'], reverse=True)
        
        # 显示主要指标对比
        print("🏆 主要指标对比 (按验证集mAP50排序):")
        print(f"{'排名':<4} {'模型':<20} {'验证mAP50':<12} {'测试mAP50':<12} {'验证mAP50-95':<14} {'测试mAP50-95':<14}")
        print("-" * 90)
        
        for i, row in enumerate(comparison_data, 1):
            print(f"{i:<4} {row['模型']:<20} {row['验证集_mAP50']:<12.4f} {row['测试集_mAP50']:<12.4f} "
                  f"{row['验证集_mAP50-95']:<14.4f} {row['测试集_mAP50-95']:<14.4f}")
        
        # 困难类别详细分析
        print(f"\n🎯 困难类别详细分析:")
        print(f"{'模型':<20} {'CA002_牙刷_mAP':<15} {'CB001_果冻_mAP':<15} {'困难类别平均':<15}")
        print("-" * 70)
        
        for model_name, result in self.evaluation_results.items():
            class_metrics = result.get('class_metrics', {})
            ca002_map = class_metrics.get('CA002_牙刷', 0)
            cb001_map = class_metrics.get('CB001_果冻', 0)
            difficult_avg = (ca002_map + cb001_map) / 2
            
            print(f"{model_name:<20} {ca002_map:<15.4f} {cb001_map:<15.4f} {difficult_avg:<15.4f}")
        
        # 找出最佳模型
        best_model = comparison_data[0]
        print(f"\n🥇 最佳模型: {best_model['模型']}")
        print(f"   验证集性能: mAP50={best_model['验证集_mAP50']:.4f}, mAP50-95={best_model['验证集_mAP50-95']:.4f}")
        print(f"   测试集性能: mAP50={best_model['测试集_mAP50']:.4f}, mAP50-95={best_model['测试集_mAP50-95']:.4f}")
        
        # 与基线对比
        baseline_result = None
        best_result = None
        
        for model_name, result in self.evaluation_results.items():
            if "基线" in model_name:
                baseline_result = result
            if model_name == best_model['模型']:
                best_result = result
        
        if baseline_result and best_result and baseline_result != best_result:
            print(f"\n📈 相比基线模型的改进:")
            val_improvement = best_result['validation_metrics']['mAP50'] - baseline_result['validation_metrics']['mAP50']
            test_improvement = best_result['test_metrics']['mAP50'] - baseline_result['test_metrics']['mAP50']
            
            print(f"   验证集mAP50改进: {val_improvement:+.4f} ({val_improvement/baseline_result['validation_metrics']['mAP50']*100:+.2f}%)")
            print(f"   测试集mAP50改进: {test_improvement:+.4f} ({test_improvement/baseline_result['test_metrics']['mAP50']*100:+.2f}%)")
            
            # 困难类别改进
            baseline_class = baseline_result.get('class_metrics', {})
            best_class = best_result.get('class_metrics', {})
            
            for difficult_cat in self.difficult_categories:
                baseline_score = baseline_class.get(difficult_cat, 0)
                best_score = best_class.get(difficult_cat, 0)
                improvement = best_score - baseline_score
                
                if baseline_score > 0:
                    improvement_pct = improvement / baseline_score * 100
                    print(f"   {difficult_cat}改进: {improvement:+.4f} ({improvement_pct:+.2f}%)")
        
        # 保存详细报告
        self.save_detailed_report(comparison_data)
        
        return comparison_data
        
    def save_detailed_report(self, comparison_data):
        """保存详细报告"""
        # 保存JSON格式的详细结果
        detailed_report = {
            "evaluation_summary": {
                "total_models_evaluated": len(self.evaluation_results),
                "evaluation_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "dataset_path": str(self.dataset_path)
            },
            "model_rankings": comparison_data,
            "detailed_results": self.evaluation_results
        }
        
        report_file = "comprehensive_model_evaluation_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(detailed_report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存: {report_file}")
        
        # 保存CSV格式的对比表格
        df = pd.DataFrame(comparison_data)
        csv_file = "model_comparison_table.csv"
        df.to_csv(csv_file, index=False, encoding='utf-8')
        print(f"📊 对比表格已保存: {csv_file}")
        
    def generate_visualization(self):
        """生成可视化图表"""
        if not self.evaluation_results:
            return
            
        # 创建性能对比图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        models = list(self.evaluation_results.keys())
        val_map50 = [self.evaluation_results[m]['validation_metrics']['mAP50'] for m in models]
        test_map50 = [self.evaluation_results[m]['test_metrics']['mAP50'] for m in models]
        val_map50_95 = [self.evaluation_results[m]['validation_metrics']['mAP50_95'] for m in models]
        test_map50_95 = [self.evaluation_results[m]['test_metrics']['mAP50_95'] for m in models]
        
        # mAP50对比
        x = np.arange(len(models))
        width = 0.35
        
        ax1.bar(x - width/2, val_map50, width, label='验证集', alpha=0.8)
        ax1.bar(x + width/2, test_map50, width, label='测试集', alpha=0.8)
        ax1.set_xlabel('模型')
        ax1.set_ylabel('mAP50')
        ax1.set_title('mAP50 对比')
        ax1.set_xticks(x)
        ax1.set_xticklabels(models, rotation=45, ha='right')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # mAP50-95对比
        ax2.bar(x - width/2, val_map50_95, width, label='验证集', alpha=0.8)
        ax2.bar(x + width/2, test_map50_95, width, label='测试集', alpha=0.8)
        ax2.set_xlabel('模型')
        ax2.set_ylabel('mAP50-95')
        ax2.set_title('mAP50-95 对比')
        ax2.set_xticks(x)
        ax2.set_xticklabels(models, rotation=45, ha='right')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 困难类别性能对比
        ca002_scores = []
        cb001_scores = []
        
        for model in models:
            class_metrics = self.evaluation_results[model].get('class_metrics', {})
            ca002_scores.append(class_metrics.get('CA002_牙刷', 0))
            cb001_scores.append(class_metrics.get('CB001_果冻', 0))
        
        ax3.bar(x - width/2, ca002_scores, width, label='CA002_牙刷', alpha=0.8)
        ax3.bar(x + width/2, cb001_scores, width, label='CB001_果冻', alpha=0.8)
        ax3.set_xlabel('模型')
        ax3.set_ylabel('mAP50-95')
        ax3.set_title('困难类别性能对比')
        ax3.set_xticks(x)
        ax3.set_xticklabels(models, rotation=45, ha='right')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 加载时间对比
        loading_times = [self.evaluation_results[m]['loading_time'] for m in models]
        
        ax4.bar(models, loading_times, alpha=0.8, color='orange')
        ax4.set_xlabel('模型')
        ax4.set_ylabel('加载时间 (秒)')
        ax4.set_title('模型加载时间对比')
        ax4.set_xticklabels(models, rotation=45, ha='right')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('model_performance_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("📈 可视化图表已保存: model_performance_comparison.png")

def main():
    """主函数"""
    print("🔬 全面模型评估系统启动")
    
    # 创建评估器
    evaluator = ComprehensiveModelEvaluator()
    
    # 加载所有模型
    evaluator.load_all_models()
    
    if not evaluator.models:
        print("❌ 没有可用的模型进行评估")
        return
    
    # 评估所有模型
    evaluator.evaluate_all_models()
    
    if not evaluator.evaluation_results:
        print("❌ 没有成功的评估结果")
        return
    
    # 生成对比报告
    comparison_data = evaluator.generate_comparison_report()
    
    # 生成可视化
    try:
        evaluator.generate_visualization()
    except Exception as e:
        print(f"⚠️  可视化生成失败: {e}")
    
    print("\n🎉 全面模型评估完成!")
    print("所有模型的详细性能指标已生成，可用于最终模型选择")

if __name__ == "__main__":
    main()
