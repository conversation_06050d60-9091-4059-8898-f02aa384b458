#!/usr/bin/env python3
"""
数据集重组脚本
将 /home/<USER>/claude/SpatialVLA/3D/111 中的数据按照 competition_2025_dataset 的标准格式重新组织
"""

import os
import shutil
import json
from pathlib import Path
from collections import defaultdict

def create_standard_structure(base_path):
    """创建标准的数据集目录结构"""
    directories = [
        'images/train', 'images/val', 'images/test',
        'depth/train', 'depth/val', 'depth/test', 
        'labels/train', 'labels/val', 'labels/test'
    ]
    
    for dir_path in directories:
        full_path = base_path / dir_path
        full_path.mkdir(parents=True, exist_ok=True)
        print(f"创建目录: {full_path}")

def get_source_directories():
    """获取源数据目录列表"""
    base_111 = Path("/home/<USER>/claude/SpatialVLA/3D/111")
    
    source_dirs = {
        'd1': base_111 / 'd1' / 'images（rgb+深度）',
        'd2': base_111 / 'd2' / 'dataset' / 'images（rgb+深度）', 
        'd3': base_111 / 'd3' / 'images',
        'd4': base_111 / 'd4' / 'dataset' / 'images（rgb+深度）',
        'dataset_rgb': base_111 / 'dataset' / 'images',
        'dataset_depth': base_111 / 'dataset' / 'deep'
    }
    
    return source_dirs

def process_mixed_directory(source_dir, prefix, target_images_dir, target_depth_dir):
    """处理RGB和深度图像混合的目录"""
    if not source_dir.exists():
        print(f"警告: 源目录不存在 {source_dir}")
        return 0, 0
    
    rgb_count = 0
    depth_count = 0
    
    for file_path in source_dir.iterdir():
        if file_path.is_file():
            filename = file_path.name
            
            if filename.endswith('_depth.png'):
                # 深度图像
                base_name = filename.replace('_depth.png', '')
                new_name = f"{prefix}_{base_name}_depth.png"
                target_path = target_depth_dir / new_name
                shutil.copy2(file_path, target_path)
                depth_count += 1
                print(f"复制深度图: {filename} -> {new_name}")
                
            elif filename.endswith('.jpg'):
                # RGB图像
                base_name = filename.replace('.jpg', '')
                new_name = f"{prefix}_{base_name}.jpg"
                target_path = target_images_dir / new_name
                shutil.copy2(file_path, target_path)
                rgb_count += 1
                print(f"复制RGB图: {filename} -> {new_name}")
    
    return rgb_count, depth_count

def process_separated_directories(rgb_dir, depth_dir, prefix, target_images_dir, target_depth_dir):
    """处理已分离的RGB和深度目录"""
    rgb_count = 0
    depth_count = 0
    
    # 处理RGB图像
    if rgb_dir.exists():
        for file_path in rgb_dir.iterdir():
            if file_path.is_file() and file_path.suffix.lower() == '.jpg':
                filename = file_path.name
                base_name = filename.replace('.jpg', '')
                new_name = f"{prefix}_{base_name}.jpg"
                target_path = target_images_dir / new_name
                shutil.copy2(file_path, target_path)
                rgb_count += 1
                print(f"复制RGB图: {filename} -> {new_name}")
    
    # 处理深度图像
    if depth_dir.exists():
        for file_path in depth_dir.iterdir():
            if file_path.is_file() and file_path.suffix.lower() == '.png':
                filename = file_path.name
                base_name = filename.replace('_depth.png', '')
                new_name = f"{prefix}_{base_name}_depth.png"
                target_path = target_depth_dir / new_name
                shutil.copy2(file_path, target_path)
                depth_count += 1
                print(f"复制深度图: {filename} -> {new_name}")
    
    return rgb_count, depth_count

def collect_annotation_files():
    """收集所有标注文件"""
    base_111 = Path("/home/<USER>/claude/SpatialVLA/3D/111")
    annotation_files = []
    
    # 查找所有 _annotations.coco.json 文件
    for root, dirs, files in os.walk(base_111):
        for file in files:
            if file == '_annotations.coco.json':
                annotation_files.append(Path(root) / file)
    
    return annotation_files

def merge_coco_annotations(annotation_files, output_path):
    """合并COCO格式的标注文件"""
    merged_data = {
        "images": [],
        "annotations": [],
        "categories": []
    }
    
    image_id_offset = 0
    annotation_id_offset = 0
    categories_set = set()
    
    for ann_file in annotation_files:
        print(f"处理标注文件: {ann_file}")
        
        try:
            with open(ann_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 处理categories（去重）
            for cat in data.get('categories', []):
                cat_key = (cat['id'], cat['name'])
                if cat_key not in categories_set:
                    categories_set.add(cat_key)
                    merged_data['categories'].append(cat)
            
            # 处理images
            for img in data.get('images', []):
                img['id'] += image_id_offset
                merged_data['images'].append(img)
            
            # 处理annotations
            for ann in data.get('annotations', []):
                ann['id'] += annotation_id_offset
                ann['image_id'] += image_id_offset
                merged_data['annotations'].append(ann)
            
            # 更新偏移量
            if data.get('images'):
                image_id_offset = max(img['id'] for img in merged_data['images']) + 1
            if data.get('annotations'):
                annotation_id_offset = max(ann['id'] for ann in merged_data['annotations']) + 1
                
        except Exception as e:
            print(f"处理标注文件时出错 {ann_file}: {e}")
    
    # 保存合并后的标注文件
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(merged_data, f, indent=2, ensure_ascii=False)
    
    print(f"合并标注文件保存到: {output_path}")
    print(f"总计: {len(merged_data['images'])} 图像, {len(merged_data['annotations'])} 标注, {len(merged_data['categories'])} 类别")

def main():
    """主函数"""
    print("开始数据集重组...")
    
    # 创建目标目录
    target_base = Path("/home/<USER>/claude/SpatialVLA/3D/reorganized_dataset")
    if target_base.exists():
        response = input(f"目标目录 {target_base} 已存在，是否继续？(y/n): ")
        if response.lower() != 'y':
            print("操作取消")
            return
    
    create_standard_structure(target_base)
    
    # 获取源目录
    source_dirs = get_source_directories()
    
    # 目标目录（暂时全部放到train中）
    target_images_train = target_base / 'images' / 'train'
    target_depth_train = target_base / 'depth' / 'train'
    
    total_rgb = 0
    total_depth = 0
    
    # 处理混合目录
    for prefix in ['d1', 'd2', 'd3', 'd4']:
        if prefix in source_dirs:
            print(f"\n处理 {prefix} 目录...")
            rgb_count, depth_count = process_mixed_directory(
                source_dirs[prefix], prefix, target_images_train, target_depth_train
            )
            total_rgb += rgb_count
            total_depth += depth_count
    
    # 处理已分离的dataset目录
    print(f"\n处理 dataset 目录...")
    rgb_count, depth_count = process_separated_directories(
        source_dirs['dataset_rgb'], source_dirs['dataset_depth'], 
        'dataset', target_images_train, target_depth_train
    )
    total_rgb += rgb_count
    total_depth += depth_count
    
    # 处理标注文件
    print(f"\n处理标注文件...")
    annotation_files = collect_annotation_files()
    if annotation_files:
        output_annotation = target_base / 'labels' / 'train' / 'annotations.json'
        merge_coco_annotations(annotation_files, output_annotation)
    
    print(f"\n数据集重组完成!")
    print(f"总计处理: {total_rgb} RGB图像, {total_depth} 深度图像")
    print(f"目标目录: {target_base}")

if __name__ == "__main__":
    main()
