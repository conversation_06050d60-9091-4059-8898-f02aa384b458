#!/usr/bin/env python3
"""
修复数据集分割问题
将训练数据重新分割为train/val，并确保标注正确对应
"""

import os
import shutil
import random
from pathlib import Path

def fix_dataset_split():
    """修复数据集分割"""
    print("=== 修复数据集分割 ===")
    
    dataset_dir = Path("/home/<USER>/claude/SpatialVLA/3D/yolo11x/dataset")
    
    # 收集所有训练数据
    train_images_dir = dataset_dir / "images" / "train"
    train_labels_dir = dataset_dir / "labels" / "train"
    
    # 获取所有有标注的图像
    valid_images = []
    for label_file in train_labels_dir.glob("*.txt"):
        # 检查标注文件是否为空
        if label_file.stat().st_size > 0:
            img_name = label_file.stem + ".jpg"
            img_path = train_images_dir / img_name
            if img_path.exists():
                valid_images.append(img_name)
    
    print(f"找到 {len(valid_images)} 个有效的图像-标注对")
    
    # 随机分割数据集 (80% train, 20% val)
    random.seed(42)
    random.shuffle(valid_images)
    
    split_idx = int(len(valid_images) * 0.8)
    train_files = valid_images[:split_idx]
    val_files = valid_images[split_idx:]
    
    print(f"训练集: {len(train_files)} 个文件")
    print(f"验证集: {len(val_files)} 个文件")
    
    # 移动验证集文件
    val_images_dir = dataset_dir / "images" / "val"
    val_labels_dir = dataset_dir / "labels" / "val"
    
    # 清空验证集目录
    for f in val_images_dir.glob("*"):
        f.unlink()
    for f in val_labels_dir.glob("*"):
        f.unlink()
    
    # 移动文件到验证集
    for filename in val_files:
        # 移动图像
        src_img = train_images_dir / filename
        dst_img = val_images_dir / filename
        shutil.move(str(src_img), str(dst_img))
        
        # 移动标注
        label_name = filename.replace('.jpg', '.txt')
        src_label = train_labels_dir / label_name
        dst_label = val_labels_dir / label_name
        if src_label.exists():
            shutil.move(str(src_label), str(dst_label))
    
    # 清理训练集中的空标注文件
    for label_file in train_labels_dir.glob("*.txt"):
        if label_file.stat().st_size == 0:
            # 删除空标注文件和对应的图像
            img_name = label_file.stem + ".jpg"
            img_path = train_images_dir / img_name
            if img_path.exists():
                img_path.unlink()
            label_file.unlink()
    
    # 统计最终结果
    final_train_images = len(list(train_images_dir.glob("*.jpg")))
    final_train_labels = len(list(train_labels_dir.glob("*.txt")))
    final_val_images = len(list(val_images_dir.glob("*.jpg")))
    final_val_labels = len(list(val_labels_dir.glob("*.txt")))
    
    print(f"\n最终统计:")
    print(f"训练集: {final_train_images} 图像, {final_train_labels} 标注")
    print(f"验证集: {final_val_images} 图像, {final_val_labels} 标注")
    
    # 清空测试集（暂时不使用）
    test_images_dir = dataset_dir / "images" / "test"
    test_labels_dir = dataset_dir / "labels" / "test"
    for f in test_images_dir.glob("*"):
        f.unlink()
    for f in test_labels_dir.glob("*"):
        f.unlink()
    
    print("✓ 数据集分割修复完成")

if __name__ == "__main__":
    fix_dataset_split()
