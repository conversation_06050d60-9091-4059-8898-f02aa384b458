task: detect
mode: train
model: /home/<USER>/homes/ch/claude/SpatialVLA/3D/enhanced_models/enhanced_stage1_384/weights/best.pt
data: /home/<USER>/homes/ch/claude/SpatialVLA/3D/competition_2025_dataset/competition_dataset.yaml
epochs: 75
time: null
patience: 20
batch: 16
imgsz: 512
save: true
save_period: 10
cache: false
device: '0'
workers: 8
project: /home/<USER>/homes/ch/claude/SpatialVLA/3D/enhanced_models
name: enhanced_stage2_512
exist_ok: true
pretrained: true
optimizer: AdamW
verbose: true
seed: 0
deterministic: true
single_cls: false
rect: false
cos_lr: true
close_mosaic: 12
resume: false
amp: true
fraction: 1.0
profile: false
freeze: null
multi_scale: false
overlap_mask: true
mask_ratio: 4
dropout: 0.0
val: true
split: val
save_json: false
conf: null
iou: 0.7
max_det: 300
half: false
dnn: false
plots: true
source: null
vid_stride: 1
stream_buffer: false
visualize: false
augment: false
agnostic_nms: false
classes: null
retina_masks: false
embed: null
show: false
save_frames: false
save_txt: false
save_conf: false
save_crop: false
show_labels: true
show_conf: true
show_boxes: true
line_width: null
format: torchscript
keras: false
optimize: false
int8: false
dynamic: false
simplify: true
opset: null
workspace: null
nms: false
lr0: 0.0008
lrf: 0.01
momentum: 0.937
weight_decay: 0.0008
warmup_epochs: 8
warmup_momentum: 0.8
warmup_bias_lr: 0.1
box: 7.5
cls: 0.5
dfl: 1.5
pose: 12.0
kobj: 1.0
nbs: 64
hsv_h: 0.012
hsv_s: 0.6
hsv_v: 0.35
degrees: 6.0
translate: 0.08
scale: 0.35
shear: 1.2
perspective: 0.0
flipud: 0.0
fliplr: 0.5
bgr: 0.0
mosaic: 0.8
mixup: 0.1
cutmix: 0.0
copy_paste: 0.2
copy_paste_mode: flip
auto_augment: randaugment
erasing: 0.4
cfg: null
tracker: botsort.yaml
save_dir: /home/<USER>/homes/ch/claude/SpatialVLA/3D/enhanced_models/enhanced_stage2_512
