#!/usr/bin/env python3
"""
验证合并后的RGB-D数据集
确保所有RGB图像都有对应的深度图像，适合RGB-D融合训练
"""

import json
import os
from pathlib import Path
from collections import defaultdict

def validate_rgbd_pairing(dataset_path):
    """验证RGB-D配对完整性"""
    print("=== 验证RGB-D配对 ===")
    
    # 收集所有RGB和深度文件
    rgb_files = {}
    depth_files = {}
    
    # 遍历所有分割
    for split in ['train', 'val', 'test']:
        rgb_dir = dataset_path / 'images' / split
        depth_dir = dataset_path / 'depth' / split
        
        if rgb_dir.exists():
            for rgb_file in rgb_dir.glob('*.jpg'):
                rgb_files[rgb_file.stem] = (split, rgb_file)
        
        if depth_dir.exists():
            for depth_file in depth_dir.glob('*.png'):
                # 去掉_depth后缀
                base_name = depth_file.stem
                if base_name.endswith('_depth'):
                    base_name = base_name[:-6]
                depth_files[base_name] = (split, depth_file)
    
    print(f"总RGB文件: {len(rgb_files)}")
    print(f"总深度文件: {len(depth_files)}")
    
    # 检查配对
    paired = 0
    rgb_only = []
    depth_only = []
    split_mismatch = []
    
    all_names = set(rgb_files.keys()) | set(depth_files.keys())
    
    for name in all_names:
        has_rgb = name in rgb_files
        has_depth = name in depth_files
        
        if has_rgb and has_depth:
            rgb_split, _ = rgb_files[name]
            depth_split, _ = depth_files[name]
            if rgb_split == depth_split:
                paired += 1
            else:
                split_mismatch.append((name, rgb_split, depth_split))
        elif has_rgb:
            rgb_only.append(name)
        else:
            depth_only.append(name)
    
    print(f"完美配对: {paired}")
    print(f"仅有RGB: {len(rgb_only)}")
    print(f"仅有深度: {len(depth_only)}")
    print(f"分割不匹配: {len(split_mismatch)}")
    
    if rgb_only:
        print(f"仅有RGB示例: {rgb_only[:5]}")
    if depth_only:
        print(f"仅有深度示例: {depth_only[:5]}")
    if split_mismatch:
        print(f"分割不匹配示例: {split_mismatch[:3]}")
    
    # 计算配对率
    total_files = max(len(rgb_files), len(depth_files))
    pairing_rate = paired / total_files * 100 if total_files > 0 else 0
    
    return {
        'paired': paired,
        'rgb_only': len(rgb_only),
        'depth_only': len(depth_only),
        'split_mismatch': len(split_mismatch),
        'pairing_rate': pairing_rate,
        'perfect_pairing': len(rgb_only) == 0 and len(depth_only) == 0 and len(split_mismatch) == 0
    }

def validate_annotations(dataset_path):
    """验证标注文件"""
    print("\n=== 验证标注文件 ===")
    
    annotation_file = dataset_path / 'labels' / 'train' / 'annotations.json'
    
    if not annotation_file.exists():
        print("✗ 标注文件不存在")
        return False, {}
    
    try:
        with open(annotation_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        images_count = len(data.get('images', []))
        annotations_count = len(data.get('annotations', []))
        categories_count = len(data.get('categories', []))
        
        print(f"✓ 标注文件加载成功")
        print(f"  图像条目: {images_count}")
        print(f"  标注条目: {annotations_count}")
        print(f"  类别数量: {categories_count}")
        
        # 检查图像文件是否存在
        missing_images = []
        existing_images = 0
        
        for img in data['images'][:100]:  # 检查前100个
            img_path = dataset_path / 'images' / 'train' / img['file_name']
            if img_path.exists():
                existing_images += 1
            else:
                missing_images.append(img['file_name'])
        
        print(f"  图像文件检查 (前100个): {existing_images}/100 存在")
        if missing_images:
            print(f"  缺失图像示例: {missing_images[:3]}")
        
        # 统计类别分布
        category_counts = defaultdict(int)
        for ann in data['annotations']:
            category_counts[ann['category_id']] += 1
        
        print(f"  类别分布: {dict(sorted(category_counts.items())[:5])}...")
        
        return True, {
            'images': images_count,
            'annotations': annotations_count,
            'categories': categories_count,
            'missing_images': len(missing_images)
        }
        
    except Exception as e:
        print(f"✗ 标注文件验证失败: {e}")
        return False, {}

def analyze_data_distribution(dataset_path):
    """分析数据分布"""
    print("\n=== 数据分布分析 ===")
    
    distribution = {}
    
    for split in ['train', 'val', 'test']:
        rgb_dir = dataset_path / 'images' / split
        depth_dir = dataset_path / 'depth' / split
        
        rgb_count = len(list(rgb_dir.glob('*.jpg'))) if rgb_dir.exists() else 0
        depth_count = len(list(depth_dir.glob('*.png'))) if depth_dir.exists() else 0
        
        distribution[split] = {'rgb': rgb_count, 'depth': depth_count}
        print(f"{split}: RGB={rgb_count}, Depth={depth_count}")
    
    return distribution

def analyze_file_sources(dataset_path):
    """分析文件来源"""
    print("\n=== 文件来源分析 ===")
    
    sources = defaultdict(int)
    
    for split in ['train', 'val', 'test']:
        rgb_dir = dataset_path / 'images' / split
        if rgb_dir.exists():
            for rgb_file in rgb_dir.glob('*.jpg'):
                if rgb_file.name.startswith('comp_'):
                    sources['competition'] += 1
                elif rgb_file.name.startswith('reorg_'):
                    sources['reorganized'] += 1
                else:
                    sources['unknown'] += 1
    
    print("文件来源分布:")
    for source, count in sources.items():
        print(f"  {source}: {count} 文件")
    
    return sources

def check_training_readiness(pairing_result, annotation_result):
    """检查训练就绪状态"""
    print("\n=== 训练就绪检查 ===")
    
    checks = []
    
    # 检查RGB-D配对
    if pairing_result['perfect_pairing']:
        checks.append("✓ RGB-D完美配对")
    else:
        checks.append(f"⚠ RGB-D配对不完美 (配对率: {pairing_result['pairing_rate']:.1f}%)")
    
    # 检查标注文件
    if annotation_result[0]:
        checks.append("✓ 标注文件有效")
        if annotation_result[1]['missing_images'] == 0:
            checks.append("✓ 所有标注图像文件存在")
        else:
            checks.append(f"⚠ {annotation_result[1]['missing_images']} 个标注图像文件缺失")
    else:
        checks.append("✗ 标注文件无效")
    
    # 检查数据量
    if pairing_result['paired'] > 1000:
        checks.append("✓ 数据量充足 (>1000对)")
    elif pairing_result['paired'] > 500:
        checks.append("⚠ 数据量中等 (>500对)")
    else:
        checks.append("⚠ 数据量较少 (<500对)")
    
    for check in checks:
        print(f"  {check}")
    
    # 总体评估
    critical_issues = sum(1 for check in checks if check.startswith("✗"))
    warnings = sum(1 for check in checks if check.startswith("⚠"))
    
    if critical_issues == 0:
        if warnings == 0:
            print("\n🎉 数据集完全就绪，可以开始RGB-D融合训练！")
            return "ready"
        else:
            print(f"\n⚠️ 数据集基本就绪，但有 {warnings} 个警告需要注意")
            return "ready_with_warnings"
    else:
        print(f"\n❌ 数据集存在 {critical_issues} 个关键问题，需要修复后才能训练")
        return "not_ready"

def main():
    """主函数"""
    dataset_path = Path("/home/<USER>/claude/SpatialVLA/3D/merged_rgbd_dataset")
    
    print(f"验证合并后的RGB-D数据集: {dataset_path}")
    print("=" * 60)
    
    # 1. 验证RGB-D配对
    pairing_result = validate_rgbd_pairing(dataset_path)
    
    # 2. 验证标注文件
    annotation_result = validate_annotations(dataset_path)
    
    # 3. 分析数据分布
    distribution = analyze_data_distribution(dataset_path)
    
    # 4. 分析文件来源
    sources = analyze_file_sources(dataset_path)
    
    # 5. 检查训练就绪状态
    readiness = check_training_readiness(pairing_result, annotation_result)
    
    # 6. 生成总结报告
    print("\n" + "=" * 60)
    print("=== 验证总结 ===")
    print(f"总RGB-D配对: {pairing_result['paired']}")
    print(f"配对率: {pairing_result['pairing_rate']:.1f}%")
    print(f"标注条目: {annotation_result[1].get('annotations', 0) if annotation_result[0] else 0}")
    print(f"类别数量: {annotation_result[1].get('categories', 0) if annotation_result[0] else 0}")
    print(f"训练就绪状态: {readiness}")
    
    # 保存验证报告
    report = {
        'dataset_path': str(dataset_path),
        'validation_date': '2025-01-12',
        'pairing_result': pairing_result,
        'annotation_result': annotation_result[1] if annotation_result[0] else {},
        'distribution': distribution,
        'sources': dict(sources),
        'readiness': readiness
    }
    
    report_file = dataset_path / 'validation_report.json'
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"验证报告保存到: {report_file}")

if __name__ == "__main__":
    main()
