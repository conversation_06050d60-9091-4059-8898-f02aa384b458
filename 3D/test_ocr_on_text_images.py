#!/usr/bin/env python3
"""
测试OCR在包含文字的图像上的效果
验证OCR系统和分类逻辑是否正常工作
"""

import cv2
import json
import easyocr
from pathlib import Path

class TextImageOCRTester:
    def __init__(self):
        self.ocr_reader = None
        
        # 未知物品分类关键词
        self.unknown_keywords = {
            '数学': ['数学', '高等数学', '微积分', '线性代数', '概率论', 'math', 'mathematics'],
            '物理': ['物理', '力学', '电磁学', '光学', 'physics'],
            '化学': ['化学', '有机化学', '无机化学', 'chemistry'],
            '计算机': ['计算机', '编程', 'python', 'java', 'computer', 'programming', 'science'],
            '英语': ['英语', 'english', '词汇', '语法', 'vocabulary', 'grammar'],
            '文学': ['文学', '小说', '诗歌', '散文', 'literature'],
            '历史': ['历史', 'history', '古代史', '近代史'],
            '地理': ['地理', 'geography', '地图', '地球'],
            '生物': ['生物', 'biology', '细胞', '遗传'],
            '经济': ['经济', 'economics', '金融', '管理']
        }
    
    def load_ocr(self):
        """加载OCR引擎"""
        print("正在加载EasyOCR...")
        self.ocr_reader = easyocr.Reader(['ch_sim', 'en'], gpu=True)
        print("✅ EasyOCR加载完成")
    
    def classify_unknown_text(self, text):
        """根据文字对未知物品分类"""
        if not text:
            return "Wxxx_未识别"
        
        text_lower = text.lower()
        
        # 检查关键词
        for category, keywords in self.unknown_keywords.items():
            for keyword in keywords:
                if keyword.lower() in text_lower:
                    return f"W{category[:3]}_{category}"
        
        # 如果没有匹配到分类，返回识别到的文字
        return f"Wxxx_{text[:10]}"
    
    def test_single_image(self, image_path, expected_text=None, expected_category=None):
        """测试单张图像的OCR效果"""
        print(f"\n🔍 测试图像: {Path(image_path).name}")
        
        # 读取图像
        image = cv2.imread(str(image_path))
        if image is None:
            print(f"❌ 无法读取图像: {image_path}")
            return None
        
        # OCR识别
        results = self.ocr_reader.readtext(image)
        
        # 提取文字
        texts = []
        confidences = []
        for (bbox, text, confidence) in results:
            if confidence > 0.3:  # 置信度阈值
                texts.append(text)
                confidences.append(confidence)
                print(f"  OCR识别: '{text}' (置信度: {confidence:.3f})")
        
        combined_text = ' '.join(texts).strip()
        
        if combined_text:
            # 分类
            predicted_category = self.classify_unknown_text(combined_text)
            print(f"  分类结果: {predicted_category}")
            
            # 验证结果
            success = True
            if expected_text:
                text_match = any(expected_text.lower() in text.lower() for text in texts)
                print(f"  文字匹配: {'✅' if text_match else '❌'} (期望: '{expected_text}')")
                success = success and text_match
            
            if expected_category:
                category_match = expected_category in predicted_category
                print(f"  类别匹配: {'✅' if category_match else '❌'} (期望: {expected_category})")
                success = success and category_match
            
            return {
                'image_path': str(image_path),
                'ocr_texts': texts,
                'ocr_confidences': confidences,
                'combined_text': combined_text,
                'predicted_category': predicted_category,
                'expected_text': expected_text,
                'expected_category': expected_category,
                'success': success
            }
        else:
            print(f"  ❌ 未识别到文字")
            return {
                'image_path': str(image_path),
                'ocr_texts': [],
                'ocr_confidences': [],
                'combined_text': '',
                'predicted_category': 'Wxxx_未识别',
                'expected_text': expected_text,
                'expected_category': expected_category,
                'success': False
            }
    
    def run_text_image_test(self, test_dir="ocr_test_images"):
        """运行文字图像OCR测试"""
        test_path = Path(test_dir)
        if not test_path.exists():
            print(f"❌ 测试目录不存在: {test_dir}")
            return
        
        # 读取测试信息
        info_file = test_path / "test_info.json"
        if info_file.exists():
            with open(info_file, 'r', encoding='utf-8') as f:
                test_info = json.load(f)
            test_images = test_info['images']
        else:
            # 如果没有测试信息文件，扫描目录
            image_files = list(test_path.glob("*.jpg"))
            test_images = [{'filename': img.name, 'path': str(img)} for img in image_files]
        
        print(f"🚀 开始文字图像OCR测试")
        print(f"测试图像数量: {len(test_images)}")
        print("=" * 60)
        
        all_results = []
        success_count = 0
        ocr_success_count = 0
        
        for img_info in test_images:
            img_path = img_info['path']
            expected_text = img_info.get('text')
            expected_category = img_info.get('expected_category')
            
            result = self.test_single_image(img_path, expected_text, expected_category)
            if result:
                all_results.append(result)
                if result['combined_text']:
                    ocr_success_count += 1
                if result['success']:
                    success_count += 1
        
        # 生成汇总报告
        print("\n" + "=" * 60)
        print("📊 文字图像OCR测试汇总报告")
        print("=" * 60)
        
        total_images = len(all_results)
        ocr_success_rate = (ocr_success_count / total_images * 100) if total_images > 0 else 0
        classification_success_rate = (success_count / total_images * 100) if total_images > 0 else 0
        
        print(f"🖼️  测试图像数量: {total_images}")
        print(f"📝 OCR成功数量: {ocr_success_count}")
        print(f"📈 OCR成功率: {ocr_success_rate:.1f}%")
        print(f"🎯 分类成功数量: {success_count}")
        print(f"📊 分类成功率: {classification_success_rate:.1f}%")
        
        # 按类别统计
        category_stats = {}
        for result in all_results:
            expected_cat = result.get('expected_category', 'Unknown')
            if expected_cat not in category_stats:
                category_stats[expected_cat] = {'total': 0, 'success': 0}
            category_stats[expected_cat]['total'] += 1
            if result['success']:
                category_stats[expected_cat]['success'] += 1
        
        print(f"\n📋 分类别成功率:")
        for category, stats in category_stats.items():
            success_rate = (stats['success'] / stats['total'] * 100) if stats['total'] > 0 else 0
            print(f"  {category}: {stats['success']}/{stats['total']} ({success_rate:.1f}%)")
        
        # 保存结果
        report = {
            'summary': {
                'total_images': total_images,
                'ocr_success_count': ocr_success_count,
                'ocr_success_rate': ocr_success_rate,
                'classification_success_count': success_count,
                'classification_success_rate': classification_success_rate
            },
            'category_statistics': category_stats,
            'detailed_results': all_results
        }
        
        report_file = "text_image_ocr_results.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 详细结果已保存: {report_file}")
        
        return report

def main():
    print("=== 文字图像OCR测试 ===")
    
    # 检查测试图像目录
    test_dir = "ocr_test_images"
    if not Path(test_dir).exists():
        print(f"❌ 测试图像目录不存在: {test_dir}")
        print("请先运行 create_text_test_images.py 创建测试图像")
        return
    
    try:
        # 创建OCR测试器
        tester = TextImageOCRTester()
        
        # 加载OCR引擎
        tester.load_ocr()
        
        # 运行测试
        results = tester.run_text_image_test(test_dir)
        
        if results:
            print(f"\n🎉 文字图像OCR测试完成!")
            print(f"📊 测试了 {results['summary']['total_images']} 张图像")
            print(f"📝 OCR成功 {results['summary']['ocr_success_count']} 次")
            print(f"🎯 分类成功 {results['summary']['classification_success_count']} 次")
            print(f"📈 总体成功率: {results['summary']['classification_success_rate']:.1f}%")
        
    except Exception as e:
        print(f"❌ 文字图像OCR测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
