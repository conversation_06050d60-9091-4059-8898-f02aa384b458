#!/usr/bin/env python3
"""
验证重组后的数据集完整性
"""

import json
import os
from pathlib import Path
from collections import defaultdict

def validate_dataset_structure(dataset_path):
    """验证数据集目录结构"""
    print("=== 验证数据集结构 ===")
    
    required_dirs = [
        'images/train', 'images/val', 'images/test',
        'depth/train', 'depth/val', 'depth/test',
        'labels/train', 'labels/val', 'labels/test'
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        full_path = dataset_path / dir_path
        if not full_path.exists():
            missing_dirs.append(dir_path)
        else:
            print(f"✓ {dir_path}")
    
    if missing_dirs:
        print(f"✗ 缺失目录: {missing_dirs}")
    
    return len(missing_dirs) == 0

def count_files(dataset_path):
    """统计文件数量"""
    print("\n=== 文件数量统计 ===")
    
    counts = {}
    
    # RGB图像
    rgb_train = list((dataset_path / 'images' / 'train').glob('*.jpg'))
    rgb_val = list((dataset_path / 'images' / 'val').glob('*.jpg'))
    rgb_test = list((dataset_path / 'images' / 'test').glob('*.jpg'))
    
    counts['rgb_train'] = len(rgb_train)
    counts['rgb_val'] = len(rgb_val)
    counts['rgb_test'] = len(rgb_test)
    counts['rgb_total'] = counts['rgb_train'] + counts['rgb_val'] + counts['rgb_test']
    
    # 深度图像
    depth_train = list((dataset_path / 'depth' / 'train').glob('*.png'))
    depth_val = list((dataset_path / 'depth' / 'val').glob('*.png'))
    depth_test = list((dataset_path / 'depth' / 'test').glob('*.png'))
    
    counts['depth_train'] = len(depth_train)
    counts['depth_val'] = len(depth_val)
    counts['depth_test'] = len(depth_test)
    counts['depth_total'] = counts['depth_train'] + counts['depth_val'] + counts['depth_test']
    
    print(f"RGB图像: train={counts['rgb_train']}, val={counts['rgb_val']}, test={counts['rgb_test']}, total={counts['rgb_total']}")
    print(f"深度图像: train={counts['depth_train']}, val={counts['depth_val']}, test={counts['depth_test']}, total={counts['depth_total']}")
    
    return counts, rgb_train, depth_train

def analyze_file_naming(rgb_files, depth_files):
    """分析文件命名规则"""
    print("\n=== 文件命名分析 ===")
    
    # 分析RGB文件前缀
    rgb_prefixes = defaultdict(int)
    for file_path in rgb_files:
        filename = file_path.name
        if '_' in filename:
            prefix = filename.split('_')[0]
            rgb_prefixes[prefix] += 1
    
    print("RGB文件前缀分布:")
    for prefix, count in sorted(rgb_prefixes.items()):
        print(f"  {prefix}: {count} 文件")
    
    # 分析深度文件前缀
    depth_prefixes = defaultdict(int)
    for file_path in depth_files:
        filename = file_path.name
        if '_' in filename:
            prefix = filename.split('_')[0]
            depth_prefixes[prefix] += 1
    
    print("深度文件前缀分布:")
    for prefix, count in sorted(depth_prefixes.items()):
        print(f"  {prefix}: {count} 文件")
    
    return rgb_prefixes, depth_prefixes

def check_rgb_depth_pairs(rgb_files, depth_files):
    """检查RGB和深度图像的配对情况"""
    print("\n=== RGB-深度图像配对检查 ===")
    
    # 创建RGB文件名到路径的映射（去掉扩展名）
    rgb_names = {}
    for file_path in rgb_files:
        name_without_ext = file_path.stem  # 去掉.jpg
        rgb_names[name_without_ext] = file_path
    
    # 创建深度文件名到路径的映射（去掉_depth.png）
    depth_names = {}
    for file_path in depth_files:
        name = file_path.stem  # 去掉.png
        if name.endswith('_depth'):
            base_name = name[:-6]  # 去掉_depth
            depth_names[base_name] = file_path
    
    # 找到配对的文件
    paired_files = []
    rgb_only = []
    depth_only = []
    
    all_names = set(rgb_names.keys()) | set(depth_names.keys())
    
    for name in all_names:
        has_rgb = name in rgb_names
        has_depth = name in depth_names
        
        if has_rgb and has_depth:
            paired_files.append(name)
        elif has_rgb:
            rgb_only.append(name)
        else:
            depth_only.append(name)
    
    print(f"配对文件: {len(paired_files)} 对")
    print(f"仅有RGB: {len(rgb_only)} 文件")
    print(f"仅有深度: {len(depth_only)} 文件")
    
    if len(rgb_only) > 0:
        print(f"仅有RGB的文件示例: {rgb_only[:5]}")
    if len(depth_only) > 0:
        print(f"仅有深度的文件示例: {depth_only[:5]}")
    
    return len(paired_files), len(rgb_only), len(depth_only)

def validate_annotations(dataset_path):
    """验证标注文件"""
    print("\n=== 标注文件验证 ===")
    
    annotation_file = dataset_path / 'labels' / 'train' / 'annotations.json'
    
    if not annotation_file.exists():
        print("✗ 标注文件不存在")
        return False
    
    try:
        with open(annotation_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        images_count = len(data.get('images', []))
        annotations_count = len(data.get('annotations', []))
        categories_count = len(data.get('categories', []))
        
        print(f"✓ 标注文件加载成功")
        print(f"  图像条目: {images_count}")
        print(f"  标注条目: {annotations_count}")
        print(f"  类别数量: {categories_count}")
        
        # 检查文件名格式
        if images_count > 0:
            sample_filenames = [img['file_name'] for img in data['images'][:5]]
            print(f"  文件名示例: {sample_filenames}")
        
        return True
        
    except Exception as e:
        print(f"✗ 标注文件验证失败: {e}")
        return False

def main():
    """主函数"""
    dataset_path = Path("/home/<USER>/claude/SpatialVLA/3D/reorganized_dataset")
    
    print(f"验证数据集: {dataset_path}")
    print("=" * 50)
    
    # 1. 验证目录结构
    structure_ok = validate_dataset_structure(dataset_path)
    
    # 2. 统计文件数量
    counts, rgb_files, depth_files = count_files(dataset_path)
    
    # 3. 分析文件命名
    rgb_prefixes, depth_prefixes = analyze_file_naming(rgb_files, depth_files)
    
    # 4. 检查RGB-深度配对
    paired_count, rgb_only_count, depth_only_count = check_rgb_depth_pairs(rgb_files, depth_files)
    
    # 5. 验证标注文件
    annotations_ok = validate_annotations(dataset_path)
    
    # 总结
    print("\n" + "=" * 50)
    print("=== 验证总结 ===")
    print(f"目录结构: {'✓' if structure_ok else '✗'}")
    print(f"标注文件: {'✓' if annotations_ok else '✗'}")
    print(f"总RGB图像: {counts['rgb_total']}")
    print(f"总深度图像: {counts['depth_total']}")
    print(f"RGB-深度配对: {paired_count} 对")
    print(f"配对率: {paired_count/max(counts['rgb_total'], 1)*100:.1f}%")
    
    if structure_ok and annotations_ok:
        print("\n✓ 数据集重组成功！可以开始使用。")
    else:
        print("\n✗ 数据集存在问题，需要进一步修复。")

if __name__ == "__main__":
    main()
