#!/usr/bin/env python3
"""
合并两个数据集的标注文件
1. 将competition数据集的YOLO格式标注转换为COCO格式
2. 与reorganized数据集的COCO标注合并
3. 更新文件名以匹配合并后的数据集
"""

import json
import os
from pathlib import Path
from PIL import Image
import yaml

def load_class_names():
    """从competition数据集配置文件中加载类别名称"""
    config_files = [
        "/home/<USER>/claude/SpatialVLA/3D/competition_2025_dataset/competition_dataset.yaml",
        "/home/<USER>/claude/SpatialVLA/3D/competition_2025_dataset/optimized_dataset.yaml",
        "/home/<USER>/claude/SpatialVLA/3D/competition_2025_dataset/refined_dataset.yaml"
    ]

    for config_file in config_files:
        if Path(config_file).exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    if 'names' in config:
                        names = config['names']
                        # 如果是列表格式，转换为字典格式
                        if isinstance(names, list):
                            return {i: names[i] for i in range(len(names))}
                        elif isinstance(names, dict):
                            return names
            except Exception as e:
                print(f"读取配置文件 {config_file} 时出错: {e}")
                continue

    # 默认类别名称
    return {i: f'class_{i}' for i in range(12)}

def yolo_to_coco_bbox(yolo_bbox, img_width, img_height):
    """将YOLO格式的边界框转换为COCO格式"""
    x_center, y_center, width, height = yolo_bbox
    
    # YOLO格式是相对坐标 (0-1)
    x_center *= img_width
    y_center *= img_height
    width *= img_width
    height *= img_height
    
    # 转换为COCO格式 (x, y, width, height)，其中(x,y)是左上角坐标
    x = x_center - width / 2
    y = y_center - height / 2
    
    return [x, y, width, height]

def convert_yolo_to_coco(images_dir, labels_dir, split_name, filename_prefix="comp_"):
    """将YOLO格式标注转换为COCO格式"""
    print(f"转换 {split_name} 数据的YOLO标注...")
    
    images = []
    annotations = []
    image_id = 0
    annotation_id = 0
    
    # 遍历图像文件
    for img_file in sorted(images_dir.glob("*.jpg")):
        # 获取对应的标注文件
        label_file = labels_dir / f"{img_file.stem}.txt"
        
        if not label_file.exists():
            print(f"警告: 找不到标注文件 {label_file}")
            continue
        
        # 获取图像尺寸
        try:
            with Image.open(img_file) as img:
                img_width, img_height = img.size
        except Exception as e:
            print(f"无法读取图像 {img_file}: {e}")
            continue
        
        # 添加图像信息
        image_info = {
            "id": image_id,
            "file_name": f"{filename_prefix}{img_file.name}",
            "width": img_width,
            "height": img_height
        }
        images.append(image_info)
        
        # 读取YOLO标注
        try:
            with open(label_file, 'r') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                parts = line.split()
                if len(parts) < 5:
                    continue
                
                class_id = int(parts[0])
                yolo_bbox = [float(x) for x in parts[1:5]]
                
                # 转换边界框格式
                coco_bbox = yolo_to_coco_bbox(yolo_bbox, img_width, img_height)
                
                # 计算面积
                area = coco_bbox[2] * coco_bbox[3]
                
                # 添加标注信息
                annotation_info = {
                    "id": annotation_id,
                    "image_id": image_id,
                    "category_id": class_id,
                    "bbox": coco_bbox,
                    "area": area,
                    "iscrowd": 0
                }
                annotations.append(annotation_info)
                annotation_id += 1
        
        except Exception as e:
            print(f"处理标注文件 {label_file} 时出错: {e}")
        
        image_id += 1
    
    print(f"  转换了 {len(images)} 张图像, {len(annotations)} 个标注")
    return images, annotations

def merge_annotations():
    """合并所有标注文件"""
    print("=== 合并标注文件 ===")
    
    # 加载类别名称
    class_names = load_class_names()
    print(f"加载了 {len(class_names)} 个类别")
    
    # 创建COCO格式的基础结构
    merged_coco = {
        "images": [],
        "annotations": [],
        "categories": []
    }
    
    # 添加类别信息
    for class_id, class_name in class_names.items():
        category = {
            "id": int(class_id),
            "name": class_name,
            "supercategory": "object"
        }
        merged_coco["categories"].append(category)
    
    # 路径定义
    comp_path = Path("/home/<USER>/claude/SpatialVLA/3D/competition_2025_dataset")
    merged_path = Path("/home/<USER>/claude/SpatialVLA/3D/merged_rgbd_dataset")
    
    # 转换competition数据集的标注
    image_id_offset = 0
    annotation_id_offset = 0
    
    # 处理train数据
    train_images, train_annotations = convert_yolo_to_coco(
        comp_path / "images/train",
        comp_path / "labels/train", 
        "train"
    )
    
    # 更新ID偏移
    for img in train_images:
        img["id"] += image_id_offset
    for ann in train_annotations:
        ann["id"] += annotation_id_offset
        ann["image_id"] += image_id_offset
    
    merged_coco["images"].extend(train_images)
    merged_coco["annotations"].extend(train_annotations)
    
    image_id_offset += len(train_images)
    annotation_id_offset += len(train_annotations)
    
    # 处理val数据
    val_images, val_annotations = convert_yolo_to_coco(
        comp_path / "images/val",
        comp_path / "labels/val",
        "val"
    )
    
    for img in val_images:
        img["id"] += image_id_offset
    for ann in val_annotations:
        ann["id"] += annotation_id_offset
        ann["image_id"] += image_id_offset
    
    merged_coco["images"].extend(val_images)
    merged_coco["annotations"].extend(val_annotations)
    
    image_id_offset += len(val_images)
    annotation_id_offset += len(val_annotations)
    
    # 处理test数据
    test_images, test_annotations = convert_yolo_to_coco(
        comp_path / "images/test",
        comp_path / "labels/test",
        "test"
    )
    
    for img in test_images:
        img["id"] += image_id_offset
    for ann in test_annotations:
        ann["id"] += annotation_id_offset
        ann["image_id"] += image_id_offset
    
    merged_coco["images"].extend(test_images)
    merged_coco["annotations"].extend(test_annotations)
    
    image_id_offset += len(test_images)
    annotation_id_offset += len(test_annotations)
    
    # 加载reorganized数据集的清理后标注
    reorg_annotation_file = Path("/home/<USER>/claude/SpatialVLA/3D/reorganized_dataset/labels/train/cleaned_annotations.json")
    if reorg_annotation_file.exists():
        print("加载reorganized数据集的标注...")
        with open(reorg_annotation_file, 'r', encoding='utf-8') as f:
            reorg_data = json.load(f)
        
        # 更新reorganized数据的文件名和ID
        for img in reorg_data["images"]:
            img["id"] += image_id_offset
            img["file_name"] = f"reorg_{img['file_name']}"
        
        for ann in reorg_data["annotations"]:
            ann["id"] += annotation_id_offset
            ann["image_id"] += image_id_offset
        
        merged_coco["images"].extend(reorg_data["images"])
        merged_coco["annotations"].extend(reorg_data["annotations"])
        
        print(f"  添加了 {len(reorg_data['images'])} 张图像, {len(reorg_data['annotations'])} 个标注")
    
    # 保存合并后的标注文件
    output_files = {
        "train": merged_path / "labels/train/annotations.json",
        "val": merged_path / "labels/val/annotations.json", 
        "test": merged_path / "labels/test/annotations.json"
    }
    
    # 为了简化，先将所有标注保存到train中
    # 后续可以根据需要分割
    with open(output_files["train"], 'w', encoding='utf-8') as f:
        json.dump(merged_coco, f, indent=2, ensure_ascii=False)
    
    print(f"合并标注文件保存到: {output_files['train']}")
    print(f"总计: {len(merged_coco['images'])} 张图像, {len(merged_coco['annotations'])} 个标注, {len(merged_coco['categories'])} 个类别")
    
    return merged_coco

def main():
    """主函数"""
    print("开始合并标注文件...")
    merged_data = merge_annotations()
    print("标注文件合并完成!")

if __name__ == "__main__":
    main()
