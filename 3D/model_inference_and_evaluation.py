#!/usr/bin/env python3
"""
模型推理和评估脚本
对训练好的RGB-D YOLO模型进行推理测试，生成标注框的测试结果
"""

import os
import cv2
import numpy as np
from pathlib import Path
import json
from ultralytics import YOLO
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib import font_manager
import argparse

class RGBDModelEvaluator:
    def __init__(self, model_path, dataset_path, output_dir="inference_results"):
        self.model_path = model_path
        self.dataset_path = Path(dataset_path)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 加载模型
        self.model = YOLO(model_path)
        
        # 类别名称映射
        self.class_names = [
            'CA001_勺子', 'CA002_筷子', 'CA003_碗', 'CA004_衣架', 
            'CB001_沙琪玛', 'CB002_罐装蜜饯', 'CB003_火腿肠', 'CB004_薯片',
            'CC001_罐装饮料', 'CC002_瓶装饮料', 'CC003_盒装牛奶', 'CC004_瓶装水',
            'CD001_苹果', 'CD002_橙子', 'CD003_香蕉', 'CD004_芒果', 'Wxxx_未知物品'
        ]
        
        # 设置中文字体
        self.setup_chinese_font()
        
    def setup_chinese_font(self):
        """设置中文字体"""
        try:
            # 尝试使用系统中文字体
            font_paths = [
                '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',
                '/System/Library/Fonts/Arial.ttf',
                'C:/Windows/Fonts/simhei.ttf',
                'C:/Windows/Fonts/simsun.ttc'
            ]
            
            for font_path in font_paths:
                if os.path.exists(font_path):
                    self.font_prop = font_manager.FontProperties(fname=font_path)
                    break
            else:
                self.font_prop = font_manager.FontProperties()
                
        except Exception as e:
            print(f"字体设置失败: {e}")
            self.font_prop = font_manager.FontProperties()
    
    def load_test_images(self, split='test'):
        """加载测试图像"""
        test_dir = self.dataset_path / "images" / split
        depth_dir = self.dataset_path / "depth" / split
        
        image_files = []
        for img_file in test_dir.glob("*.jpg"):
            # 查找对应的深度图像
            base_name = img_file.stem
            depth_patterns = [f"{base_name}_depth.png", f"{base_name}.png"]
            
            depth_file = None
            for pattern in depth_patterns:
                potential_depth = depth_dir / pattern
                if potential_depth.exists():
                    depth_file = potential_depth
                    break
                    
            if depth_file:
                image_files.append({
                    'rgb': img_file,
                    'depth': depth_file,
                    'name': img_file.stem
                })
                
        return image_files
    
    def run_inference(self, image_path, conf_threshold=0.5):
        """运行模型推理"""
        results = self.model(image_path, conf=conf_threshold)
        return results[0]
    
    def visualize_results(self, rgb_image, result, save_path=None):
        """可视化检测结果"""
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        
        # 显示RGB图像
        if isinstance(rgb_image, str):
            rgb_image = cv2.imread(rgb_image)
            rgb_image = cv2.cvtColor(rgb_image, cv2.COLOR_BGR2RGB)
            
        ax.imshow(rgb_image)
        
        # 绘制检测框
        if result.boxes is not None:
            boxes = result.boxes.xyxy.cpu().numpy()
            confidences = result.boxes.conf.cpu().numpy()
            classes = result.boxes.cls.cpu().numpy().astype(int)
            
            colors = plt.cm.Set3(np.linspace(0, 1, len(self.class_names)))
            
            for box, conf, cls in zip(boxes, confidences, classes):
                x1, y1, x2, y2 = box
                width = x2 - x1
                height = y2 - y1
                
                # 绘制边界框
                rect = patches.Rectangle(
                    (x1, y1), width, height,
                    linewidth=2, edgecolor=colors[cls], facecolor='none'
                )
                ax.add_patch(rect)
                
                # 添加标签
                label = f"{self.class_names[cls]}: {conf:.2f}"
                ax.text(x1, y1-5, label, fontproperties=self.font_prop,
                       bbox=dict(boxstyle="round,pad=0.3", facecolor=colors[cls], alpha=0.7),
                       fontsize=10, color='black')
        
        ax.set_title("RGB-D YOLO检测结果", fontproperties=self.font_prop, fontsize=14)
        ax.axis('off')
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
        else:
            plt.show()
    
    def evaluate_on_test_set(self, conf_threshold=0.5, max_images=50):
        """在测试集上进行评估"""
        print("开始在测试集上进行推理...")
        
        test_images = self.load_test_images('test')
        
        if len(test_images) > max_images:
            test_images = test_images[:max_images]
            
        results_summary = {
            'total_images': len(test_images),
            'detections': [],
            'statistics': {}
        }
        
        detection_counts = {name: 0 for name in self.class_names}
        total_detections = 0
        
        for i, img_data in enumerate(test_images):
            print(f"处理图像 {i+1}/{len(test_images)}: {img_data['name']}")
            
            # 运行推理
            result = self.run_inference(str(img_data['rgb']), conf_threshold)
            
            # 保存可视化结果
            output_path = self.output_dir / f"{img_data['name']}_result.jpg"
            self.visualize_results(str(img_data['rgb']), result, output_path)
            
            # 统计检测结果
            image_detections = []
            if result.boxes is not None:
                boxes = result.boxes.xyxy.cpu().numpy()
                confidences = result.boxes.conf.cpu().numpy()
                classes = result.boxes.cls.cpu().numpy().astype(int)
                
                for box, conf, cls in zip(boxes, confidences, classes):
                    detection = {
                        'class': self.class_names[cls],
                        'confidence': float(conf),
                        'bbox': box.tolist()
                    }
                    image_detections.append(detection)
                    detection_counts[self.class_names[cls]] += 1
                    total_detections += 1
            
            results_summary['detections'].append({
                'image': img_data['name'],
                'detections': image_detections,
                'count': len(image_detections)
            })
        
        # 计算统计信息
        results_summary['statistics'] = {
            'total_detections': total_detections,
            'avg_detections_per_image': total_detections / len(test_images),
            'class_distribution': detection_counts
        }
        
        # 保存结果
        results_file = self.output_dir / "inference_results.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results_summary, f, indent=2, ensure_ascii=False)
            
        print(f"\n推理完成!")
        print(f"总图像数: {len(test_images)}")
        print(f"总检测数: {total_detections}")
        print(f"平均每张图像检测数: {total_detections / len(test_images):.2f}")
        print(f"结果保存在: {self.output_dir}")
        
        return results_summary
    
    def generate_performance_report(self):
        """生成性能报告"""
        # 在验证集上运行官方评估
        print("运行官方模型评估...")
        
        dataset_config = self.dataset_path / "dataset.yaml"
        if dataset_config.exists():
            metrics = self.model.val(data=str(dataset_config), split='val')
            
            report = f"""
# RGB-D YOLO模型性能报告

## 模型信息
- 模型路径: {self.model_path}
- 数据集路径: {self.dataset_path}

## 性能指标
- mAP50: {metrics.box.map50:.4f}
- mAP50-95: {metrics.box.map:.4f}
- 精确率: {metrics.box.mp:.4f}
- 召回率: {metrics.box.mr:.4f}

## 各类别性能
"""
            
            if hasattr(metrics.box, 'maps'):
                for i, class_map in enumerate(metrics.box.maps):
                    if i < len(self.class_names):
                        report += f"- {self.class_names[i]}: {class_map:.4f}\n"
            
            report_file = self.output_dir / "performance_report.md"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
                
            print(f"性能报告保存在: {report_file}")
            return metrics
        else:
            print(f"未找到数据集配置文件: {dataset_config}")
            return None

def main():
    parser = argparse.ArgumentParser(description='RGB-D YOLO模型推理和评估')
    parser.add_argument('--model', type=str, 
                       default='rgbd_yolo_runs/rgbd_experiment/weights/best.pt',
                       help='模型权重文件路径')
    parser.add_argument('--dataset', type=str, 
                       default='unified_rgbd_dataset',
                       help='数据集路径')
    parser.add_argument('--output', type=str, 
                       default='inference_results',
                       help='输出目录')
    parser.add_argument('--conf', type=float, default=0.5,
                       help='置信度阈值')
    parser.add_argument('--max_images', type=int, default=50,
                       help='最大测试图像数')
    
    args = parser.parse_args()
    
    # 检查模型文件是否存在
    if not os.path.exists(args.model):
        print(f"错误: 模型文件不存在: {args.model}")
        print("请确保训练已完成并且模型文件路径正确")
        return
    
    # 创建评估器
    evaluator = RGBDModelEvaluator(args.model, args.dataset, args.output)
    
    # 运行推理
    results = evaluator.evaluate_on_test_set(args.conf, args.max_images)
    
    # 生成性能报告
    metrics = evaluator.generate_performance_report()
    
    print("\n=== 推理和评估完成 ===")
    print(f"检测结果图像保存在: {args.output}")

if __name__ == "__main__":
    main()
