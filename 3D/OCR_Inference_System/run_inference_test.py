#!/usr/bin/env python3
"""
简化的推理测试脚本
测试训练好的YOLOv11n模型在测试集上的表现
"""

import os
import time
import json
from pathlib import Path
from ultralytics import YOLO
import cv2
import numpy as np

class InferenceTest:
    def __init__(self, model_path="competition_2025_models/competition_2025_yolo11n/weights/best.pt"):
        self.model_path = model_path
        self.model = None
        
        # 比赛类别映射
        self.category_mapping = {
            0: 'CA001_衣架',
            1: 'CA002_牙刷', 
            2: 'CB001_果冻',
            3: 'CB002_长方形状饼干',
            4: 'CC001_罐装饮料',
            5: 'CC002_瓶装饮料',
            6: 'CD001_香蕉',
            7: 'CD002_橙子',
            8: 'Wxxx_未知物品'
        }
        
        # 性能统计
        self.stats = {
            'total_images': 0,
            'total_detections': 0,
            'inference_times': [],
            'category_counts': {cat: 0 for cat in self.category_mapping.values()}
        }
    
    def load_model(self):
        """加载模型"""
        print(f"正在加载模型: {self.model_path}")
        
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型文件不存在: {self.model_path}")
        
        start_time = time.time()
        self.model = YOLO(self.model_path)
        loading_time = time.time() - start_time
        
        print(f"✅ 模型加载完成，耗时: {loading_time:.3f}s")
        
        # 预热模型
        dummy_image = np.zeros((640, 640, 3), dtype=np.uint8)
        _ = self.model(dummy_image, verbose=False)
        print("✅ 模型预热完成")
        
        return loading_time
    
    def run_inference_on_image(self, image_path, conf_threshold=0.25):
        """对单张图像进行推理"""
        start_time = time.time()
        
        # 运行推理
        results = self.model(image_path, conf=conf_threshold, verbose=False)
        
        inference_time = time.time() - start_time
        self.stats['inference_times'].append(inference_time)
        
        # 解析结果
        detections = []
        if results[0].boxes is not None:
            boxes = results[0].boxes.xyxy.cpu().numpy()
            confidences = results[0].boxes.conf.cpu().numpy()
            classes = results[0].boxes.cls.cpu().numpy().astype(int)
            
            for box, conf, cls in zip(boxes, confidences, classes):
                category_name = self.category_mapping.get(cls, 'Unknown')
                detection = {
                    'category_id': int(cls),
                    'category_name': category_name,
                    'confidence': float(conf),
                    'bbox': box.tolist()
                }
                detections.append(detection)
                
                # 统计类别
                self.stats['category_counts'][category_name] += 1
        
        self.stats['total_detections'] += len(detections)
        
        return {
            'image_path': str(image_path),
            'inference_time': inference_time,
            'detections': detections,
            'detection_count': len(detections)
        }
    
    def run_batch_inference(self, test_dir="competition_2025_dataset/images/test", max_images=10):
        """批量推理测试"""
        test_path = Path(test_dir)
        if not test_path.exists():
            raise FileNotFoundError(f"测试目录不存在: {test_dir}")
        
        # 获取测试图像
        image_files = list(test_path.glob("*.jpg"))[:max_images]
        
        if not image_files:
            raise FileNotFoundError(f"在 {test_dir} 中未找到图像文件")
        
        print(f"\n🚀 开始批量推理测试")
        print(f"测试图像数量: {len(image_files)}")
        print("=" * 60)
        
        all_results = []
        
        for i, img_path in enumerate(image_files, 1):
            try:
                result = self.run_inference_on_image(img_path)
                all_results.append(result)
                
                print(f"[{i:2d}/{len(image_files)}] {img_path.name}")
                print(f"  检测数量: {result['detection_count']}")
                print(f"  推理时间: {result['inference_time']:.3f}s")
                
                if result['detections']:
                    print("  检测结果:")
                    for det in result['detections']:
                        print(f"    - {det['category_name']}: {det['confidence']:.3f}")
                else:
                    print("  未检测到物体")
                print("-" * 40)
                
            except Exception as e:
                print(f"❌ 处理图像失败 {img_path.name}: {e}")
        
        self.stats['total_images'] = len(all_results)
        
        return all_results
    
    def generate_summary_report(self, results):
        """生成汇总报告"""
        if not results:
            print("❌ 没有推理结果")
            return
        
        # 计算统计信息
        avg_inference_time = np.mean(self.stats['inference_times'])
        total_detections = self.stats['total_detections']
        avg_detections_per_image = total_detections / len(results) if results else 0
        
        print("\n" + "=" * 60)
        print("📊 推理测试汇总报告")
        print("=" * 60)
        
        print(f"🖼️  测试图像数量: {len(results)}")
        print(f"🎯 总检测数量: {total_detections}")
        print(f"📈 平均每张图像检测数: {avg_detections_per_image:.1f}")
        print(f"⏱️  平均推理时间: {avg_inference_time:.3f}s")
        print(f"🚀 推理速度: {1/avg_inference_time:.1f} FPS")
        
        print(f"\n📋 类别检测统计:")
        for category, count in self.stats['category_counts'].items():
            if count > 0:
                percentage = (count / total_detections) * 100 if total_detections > 0 else 0
                print(f"  {category}: {count} ({percentage:.1f}%)")
        
        # 性能评估
        print(f"\n🏆 性能评估:")
        loading_time_ok = True  # 已经加载过了
        inference_time_ok = avg_inference_time < 5.0  # 比赛要求 < 5秒
        
        print(f"  模型加载时间: ✅ (< 30s)")
        print(f"  推理时间: {'✅' if inference_time_ok else '❌'} ({avg_inference_time:.3f}s {'< 5s' if inference_time_ok else '>= 5s'})")
        
        # 保存详细结果
        report = {
            'summary': {
                'total_images': len(results),
                'total_detections': total_detections,
                'avg_detections_per_image': float(avg_detections_per_image),
                'avg_inference_time': float(avg_inference_time),
                'fps': float(1/avg_inference_time),
                'performance_ok': bool(inference_time_ok)
            },
            'category_statistics': self.stats['category_counts'],
            'detailed_results': results
        }
        
        report_file = "inference_test_results.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 详细结果已保存: {report_file}")
        
        return report

def main():
    print("=== 2025年比赛模型推理测试 ===")
    
    # 检查模型是否存在
    model_path = "competition_2025_models/competition_2025_yolo11n/weights/best.pt"
    if not Path(model_path).exists():
        print(f"❌ 模型文件不存在: {model_path}")
        print("请先运行训练脚本生成模型")
        return
    
    try:
        # 创建推理测试器
        tester = InferenceTest(model_path)
        
        # 加载模型
        loading_time = tester.load_model()
        
        # 运行批量推理测试
        results = tester.run_batch_inference(max_images=15)  # 测试15张图像
        
        # 生成汇总报告
        report = tester.generate_summary_report(results)
        
        print(f"\n🎉 推理测试完成!")
        print(f"📊 测试了 {len(results)} 张图像")
        print(f"🎯 检测到 {tester.stats['total_detections']} 个物体")
        print(f"⚡ 平均推理时间: {np.mean(tester.stats['inference_times']):.3f}s")
        
    except Exception as e:
        print(f"❌ 推理测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
