#!/usr/bin/env python3
"""
专门测试OCR在未知物品上的效果
从检测结果中提取未知物品区域，进行OCR识别测试
"""

import os
import cv2
import json
import numpy as np
from pathlib import Path
from ultralytics import YOLO
import easyocr

class OCRTester:
    def __init__(self, model_path="competition_2025_models/competition_2025_yolo11n/weights/best.pt"):
        self.model_path = model_path
        self.model = None
        self.ocr_reader = None
        
        # 比赛类别映射
        self.category_mapping = {
            0: 'CA001_衣架',
            1: 'CA002_牙刷', 
            2: 'CB001_果冻',
            3: 'CB002_长方形状饼干',
            4: 'CC001_罐装饮料',
            5: 'CC002_瓶装饮料',
            6: 'CD001_香蕉',
            7: 'CD002_橙子',
            8: 'Wxxx_未知物品'
        }
        
        # 未知物品分类关键词
        self.unknown_keywords = {
            '数学': ['数学', '高等数学', '微积分', '线性代数', '概率论', 'math', 'mathematics'],
            '物理': ['物理', '力学', '电磁学', '光学', 'physics'],
            '化学': ['化学', '有机化学', '无机化学', 'chemistry'],
            '计算机': ['计算机', '编程', 'python', 'java', 'computer', 'programming'],
            '英语': ['英语', 'english', '词汇', '语法', 'vocabulary'],
            '文学': ['文学', '小说', '诗歌', '散文', 'literature'],
            '历史': ['历史', 'history', '古代史', '近代史'],
            '地理': ['地理', 'geography', '地图', '地球'],
            '生物': ['生物', 'biology', '细胞', '遗传'],
            '经济': ['经济', 'economics', '金融', '管理']
        }
    
    def load_models(self):
        """加载YOLO和OCR模型"""
        print("正在加载YOLO模型...")
        self.model = YOLO(self.model_path)
        print("✅ YOLO模型加载完成")
        
        print("正在加载EasyOCR...")
        self.ocr_reader = easyocr.Reader(['ch_sim', 'en'], gpu=True)
        print("✅ EasyOCR加载完成")
    
    def preprocess_roi_for_ocr(self, roi):
        """优化ROI区域用于OCR识别"""
        if roi.size == 0:
            return roi
        
        # 调整大小 - 如果太小则放大
        h, w = roi.shape[:2]
        if h < 50 or w < 50:
            scale = max(50/h, 50/w, 2.0)  # 至少放大2倍
            new_h, new_w = int(h * scale), int(w * scale)
            roi = cv2.resize(roi, (new_w, new_h), interpolation=cv2.INTER_CUBIC)
        
        # 转换为灰度图
        if len(roi.shape) == 3:
            gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
        else:
            gray = roi
        
        # 增强对比度
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)
        
        # 去噪
        denoised = cv2.medianBlur(enhanced, 3)
        
        # 锐化
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(denoised, -1, kernel)
        
        # 自适应二值化
        binary = cv2.adaptiveThreshold(sharpened, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                     cv2.THRESH_BINARY, 11, 2)
        
        return binary
    
    def extract_text_from_roi(self, image, bbox, save_roi=False, roi_name=""):
        """从ROI区域提取文字"""
        try:
            x1, y1, x2, y2 = map(int, bbox)
            
            # 添加边距
            margin = 10
            h, w = image.shape[:2]
            x1 = max(0, x1 - margin)
            y1 = max(0, y1 - margin)
            x2 = min(w, x2 + margin)
            y2 = min(h, y2 + margin)
            
            # 提取ROI
            roi = image[y1:y2, x1:x2]
            
            if roi.size == 0:
                return ""
            
            # 预处理ROI
            processed_roi = self.preprocess_roi_for_ocr(roi)
            
            # 保存ROI用于调试
            if save_roi and roi_name:
                roi_dir = Path("ocr_test_rois")
                roi_dir.mkdir(exist_ok=True)
                cv2.imwrite(str(roi_dir / f"{roi_name}_original.jpg"), roi)
                cv2.imwrite(str(roi_dir / f"{roi_name}_processed.jpg"), processed_roi)
            
            # OCR识别
            results = self.ocr_reader.readtext(processed_roi)
            
            # 提取文字
            texts = []
            for (bbox_ocr, text, confidence) in results:
                if confidence > 0.3:  # 降低置信度阈值
                    texts.append(text)
            
            return ' '.join(texts).strip()
            
        except Exception as e:
            print(f"OCR识别失败: {e}")
            return ""
    
    def classify_unknown_text(self, text):
        """根据文字对未知物品分类"""
        if not text:
            return "Wxxx_未识别"
        
        text_lower = text.lower()
        
        # 检查关键词
        for category, keywords in self.unknown_keywords.items():
            for keyword in keywords:
                if keyword.lower() in text_lower:
                    return f"W{category[:3]}_{category}"
        
        # 如果没有匹配到分类，返回识别到的文字
        return f"Wxxx_{text[:10]}"
    
    def test_ocr_on_image(self, image_path):
        """测试单张图像的OCR效果"""
        print(f"\n🔍 测试图像: {Path(image_path).name}")
        
        # 读取图像
        image = cv2.imread(str(image_path))
        if image is None:
            print(f"❌ 无法读取图像: {image_path}")
            return None
        
        # YOLO检测
        results = self.model(image, conf=0.25, verbose=False)
        
        if results[0].boxes is None:
            print("  未检测到任何物体")
            return None
        
        boxes = results[0].boxes.xyxy.cpu().numpy()
        confidences = results[0].boxes.conf.cpu().numpy()
        classes = results[0].boxes.cls.cpu().numpy().astype(int)
        
        unknown_items = []
        total_detections = len(boxes)
        
        print(f"  检测到 {total_detections} 个物体")
        
        for i, (box, conf, cls) in enumerate(zip(boxes, confidences, classes)):
            category_name = self.category_mapping.get(cls, 'Unknown')
            
            if cls == 8:  # 未知物品
                print(f"    未知物品 #{i+1}: 置信度 {conf:.3f}")
                
                # OCR识别
                roi_name = f"{Path(image_path).stem}_unknown_{i+1}"
                ocr_text = self.extract_text_from_roi(image, box, save_roi=True, roi_name=roi_name)
                
                if ocr_text:
                    final_category = self.classify_unknown_text(ocr_text)
                    print(f"      OCR识别: '{ocr_text}' -> {final_category}")
                    
                    unknown_items.append({
                        'bbox': box.tolist(),
                        'confidence': float(conf),
                        'ocr_text': ocr_text,
                        'final_category': final_category
                    })
                else:
                    print(f"      OCR识别: 无文字")
                    unknown_items.append({
                        'bbox': box.tolist(),
                        'confidence': float(conf),
                        'ocr_text': '',
                        'final_category': 'Wxxx_未识别'
                    })
            else:
                print(f"    {category_name}: 置信度 {conf:.3f}")
        
        return {
            'image_path': str(image_path),
            'total_detections': total_detections,
            'unknown_items': unknown_items,
            'ocr_success_count': len([item for item in unknown_items if item['ocr_text']])
        }
    
    def run_ocr_test(self, test_dir="competition_2025_dataset/images/test", max_images=5):
        """运行OCR测试"""
        test_path = Path(test_dir)
        if not test_path.exists():
            print(f"❌ 测试目录不存在: {test_dir}")
            return
        
        # 获取测试图像
        image_files = list(test_path.glob("*.jpg"))[:max_images]
        
        if not image_files:
            print(f"❌ 在 {test_dir} 中未找到图像文件")
            return
        
        print(f"🚀 开始OCR测试")
        print(f"测试图像数量: {len(image_files)}")
        print("=" * 60)
        
        all_results = []
        total_unknown = 0
        total_ocr_success = 0
        
        for img_path in image_files:
            result = self.test_ocr_on_image(img_path)
            if result:
                all_results.append(result)
                total_unknown += len(result['unknown_items'])
                total_ocr_success += result['ocr_success_count']
        
        # 生成汇总报告
        print("\n" + "=" * 60)
        print("📊 OCR测试汇总报告")
        print("=" * 60)
        
        print(f"🖼️  测试图像数量: {len(all_results)}")
        print(f"🔍 未知物品总数: {total_unknown}")
        print(f"📝 OCR成功数量: {total_ocr_success}")
        
        if total_unknown > 0:
            success_rate = (total_ocr_success / total_unknown) * 100
            print(f"📈 OCR成功率: {success_rate:.1f}%")
        else:
            print(f"📈 OCR成功率: N/A (无未知物品)")
        
        # 保存结果
        report = {
            'summary': {
                'total_images': len(all_results),
                'total_unknown_items': total_unknown,
                'ocr_success_count': total_ocr_success,
                'ocr_success_rate': (total_ocr_success / total_unknown * 100) if total_unknown > 0 else 0
            },
            'detailed_results': all_results
        }
        
        report_file = "ocr_test_results.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 详细结果已保存: {report_file}")
        print(f"🖼️  ROI图像已保存到: ocr_test_rois/")
        
        return report

def main():
    print("=== OCR未知物品识别测试 ===")
    
    # 检查模型是否存在
    model_path = "competition_2025_models/competition_2025_yolo11n/weights/best.pt"
    if not Path(model_path).exists():
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    try:
        # 创建OCR测试器
        tester = OCRTester(model_path)
        
        # 加载模型
        tester.load_models()
        
        # 运行OCR测试
        results = tester.run_ocr_test(max_images=8)
        
        if results:
            print(f"\n🎉 OCR测试完成!")
            print(f"📊 测试了 {results['summary']['total_images']} 张图像")
            print(f"🔍 发现了 {results['summary']['total_unknown_items']} 个未知物品")
            print(f"📝 OCR成功 {results['summary']['ocr_success_count']} 次")
            print(f"📈 成功率: {results['summary']['ocr_success_rate']:.1f}%")
        
    except Exception as e:
        print(f"❌ OCR测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
