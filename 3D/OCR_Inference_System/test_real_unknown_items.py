#!/usr/bin/env python3
"""
测试真实未知物品图像的OCR识别效果
专门针对 /home/<USER>/claude/SpatialVLA/3D/Addition/unkown/dataset/images/ 中的真实图像
"""

import os
import cv2
import json
import numpy as np
from pathlib import Path
from ultralytics import YOLO
import easyocr

class RealUnknownItemsOCRTester:
    def __init__(self, model_path="../competition_2025_models/competition_2025_yolo11n/weights/best.pt"):
        self.model_path = model_path
        self.model = None
        self.ocr_reader = None
        
        # 比赛类别映射
        self.category_mapping = {
            0: 'CA001_衣架',
            1: 'CA002_牙刷', 
            2: 'CB001_果冻',
            3: 'CB002_长方形状饼干',
            4: 'CC001_罐装饮料',
            5: 'CC002_瓶装饮料',
            6: 'CD001_香蕉',
            7: 'CD002_橙子',
            8: 'Wxxx_未知物品'
        }
        
        # 扩展的未知物品分类关键词
        self.unknown_keywords = {
            '数学': ['数学', '高等数学', '微积分', '线性代数', '概率论', '统计', 'math', 'mathematics', 'calculus', 'algebra'],
            '物理': ['物理', '力学', '电磁学', '光学', '热学', 'physics', 'mechanics', 'optics'],
            '化学': ['化学', '有机化学', '无机化学', '分析化学', 'chemistry', 'organic', 'inorganic'],
            '计算机': ['计算机', '编程', 'python', 'java', 'c++', 'computer', 'programming', 'software', 'code'],
            '英语': ['英语', 'english', '词汇', '语法', 'vocabulary', 'grammar', 'toefl', 'ielts'],
            '文学': ['文学', '小说', '诗歌', '散文', 'literature', 'novel', 'poetry'],
            '历史': ['历史', 'history', '古代史', '近代史', '世界史', '中国史'],
            '地理': ['地理', 'geography', '地图', '地球', '世界地理'],
            '生物': ['生物', 'biology', '细胞', '遗传', '生态', '植物', '动物'],
            '经济': ['经济', 'economics', '金融', '管理', '会计', '市场'],
            '医学': ['医学', '药学', '护理', 'medicine', 'medical', 'health'],
            '艺术': ['艺术', '美术', '音乐', '绘画', 'art', 'music', 'painting'],
            '哲学': ['哲学', '思想', 'philosophy', '伦理'],
            '法律': ['法律', '法学', 'law', 'legal', '司法'],
            '教育': ['教育', '教学', 'education', 'teaching', '师范']
        }
        
        # 真实图像路径
        self.real_images_path = "/home/<USER>/claude/SpatialVLA/3D/Addition/unkown/dataset/images"
    
    def load_models(self):
        """加载YOLO和OCR模型"""
        print("正在加载YOLO模型...")
        if not os.path.exists(self.model_path):
            print(f"❌ 模型文件不存在: {self.model_path}")
            print("请确保模型路径正确")
            return False
            
        self.model = YOLO(self.model_path)
        print("✅ YOLO模型加载完成")
        
        print("正在加载EasyOCR...")
        self.ocr_reader = easyocr.Reader(['ch_sim', 'en'], gpu=True)
        print("✅ EasyOCR加载完成")
        
        return True
    
    def enhanced_preprocess_for_ocr(self, roi):
        """增强的OCR预处理"""
        if roi.size == 0:
            return roi
        
        # 1. 尺寸调整 - 确保足够大
        h, w = roi.shape[:2]
        if h < 100 or w < 100:
            scale = max(100/h, 100/w, 3.0)  # 至少放大3倍
            new_h, new_w = int(h * scale), int(w * scale)
            roi = cv2.resize(roi, (new_w, new_h), interpolation=cv2.INTER_CUBIC)
        
        # 2. 转换为灰度图
        if len(roi.shape) == 3:
            gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
        else:
            gray = roi
        
        # 3. 高斯模糊去噪
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)
        
        # 4. 增强对比度
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        enhanced = clahe.apply(blurred)
        
        # 5. 锐化
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(enhanced, -1, kernel)
        
        # 6. 多种二值化方法
        # 方法1: OTSU
        _, binary1 = cv2.threshold(sharpened, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 方法2: 自适应阈值
        binary2 = cv2.adaptiveThreshold(sharpened, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                       cv2.THRESH_BINARY, 11, 2)
        
        # 方法3: 反向二值化
        _, binary3 = cv2.threshold(sharpened, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        
        return [binary1, binary2, binary3, enhanced]  # 返回多个版本
    
    def extract_text_with_multiple_methods(self, image, bbox, save_debug=False, image_name=""):
        """使用多种方法提取文字"""
        try:
            x1, y1, x2, y2 = map(int, bbox)
            
            # 添加边距
            margin = 15
            h, w = image.shape[:2]
            x1 = max(0, x1 - margin)
            y1 = max(0, y1 - margin)
            x2 = min(w, x2 + margin)
            y2 = min(h, y2 + margin)
            
            # 提取ROI
            roi = image[y1:y2, x1:x2]
            
            if roi.size == 0:
                return ""
            
            # 多种预处理方法
            processed_rois = self.enhanced_preprocess_for_ocr(roi)
            
            all_texts = []
            best_confidence = 0
            best_text = ""
            
            # 对每种预处理结果进行OCR
            for i, processed_roi in enumerate(processed_rois):
                try:
                    # 保存调试图像
                    if save_debug:
                        debug_dir = Path("real_ocr_debug")
                        debug_dir.mkdir(exist_ok=True)
                        cv2.imwrite(str(debug_dir / f"{image_name}_method_{i}.jpg"), processed_roi)
                    
                    # OCR识别
                    results = self.ocr_reader.readtext(processed_roi)
                    
                    for (bbox_ocr, text, confidence) in results:
                        if confidence > 0.2:  # 降低置信度阈值
                            all_texts.append((text, confidence, i))
                            if confidence > best_confidence:
                                best_confidence = confidence
                                best_text = text
                                
                except Exception as e:
                    continue
            
            # 合并所有识别结果
            if all_texts:
                # 按置信度排序
                all_texts.sort(key=lambda x: x[1], reverse=True)
                
                # 取置信度最高的几个结果
                top_texts = [text for text, conf, method in all_texts[:3] if conf > 0.3]
                
                if top_texts:
                    combined_text = ' '.join(top_texts)
                    return combined_text.strip()
            
            return best_text.strip()
            
        except Exception as e:
            print(f"OCR识别失败: {e}")
            return ""
    
    def classify_unknown_text(self, text):
        """根据文字对未知物品分类"""
        if not text:
            return "Wxxx_未识别"
        
        text_lower = text.lower()
        
        # 检查关键词
        for category, keywords in self.unknown_keywords.items():
            for keyword in keywords:
                if keyword.lower() in text_lower:
                    return f"W{category[:3]}_{category}"
        
        # 如果没有匹配到分类，返回识别到的文字
        clean_text = ''.join(c for c in text if c.isalnum() or c.isspace())[:10]
        return f"Wxxx_{clean_text}" if clean_text else "Wxxx_未识别"
    
    def test_real_unknown_images(self, max_images=20):
        """测试真实未知物品图像"""
        images_path = Path(self.real_images_path)
        
        if not images_path.exists():
            print(f"❌ 真实图像目录不存在: {self.real_images_path}")
            return None
        
        # 获取图像文件
        image_files = list(images_path.glob("*.jpg"))[:max_images]
        
        if not image_files:
            print(f"❌ 在 {self.real_images_path} 中未找到图像文件")
            return None
        
        print(f"\n🚀 开始测试真实未知物品图像")
        print(f"图像目录: {self.real_images_path}")
        print(f"测试图像数量: {len(image_files)}")
        print("=" * 80)
        
        all_results = []
        total_unknown_detected = 0
        total_ocr_success = 0
        total_classified = 0
        
        for i, img_path in enumerate(image_files, 1):
            print(f"\n🔍 [{i:2d}/{len(image_files)}] 测试图像: {img_path.name}")
            
            # 读取图像
            image = cv2.imread(str(img_path))
            if image is None:
                print(f"  ❌ 无法读取图像")
                continue
            
            # YOLO检测
            results = self.model(image, conf=0.2, verbose=False)  # 降低置信度
            
            if results[0].boxes is None:
                print(f"  未检测到任何物体")
                continue
            
            boxes = results[0].boxes.xyxy.cpu().numpy()
            confidences = results[0].boxes.conf.cpu().numpy()
            classes = results[0].boxes.cls.cpu().numpy().astype(int)
            
            print(f"  检测到 {len(boxes)} 个物体")
            
            image_result = {
                'image_path': str(img_path),
                'image_name': img_path.name,
                'total_detections': len(boxes),
                'unknown_items': [],
                'other_items': []
            }
            
            unknown_count = 0
            ocr_success_count = 0
            
            for j, (box, conf, cls) in enumerate(zip(boxes, confidences, classes)):
                category_name = self.category_mapping.get(cls, 'Unknown')
                
                if cls == 8:  # 未知物品
                    unknown_count += 1
                    total_unknown_detected += 1
                    
                    print(f"    🔍 未知物品 #{j+1}: 置信度 {conf:.3f}")
                    
                    # OCR识别
                    ocr_text = self.extract_text_with_multiple_methods(
                        image, box, save_debug=True, 
                        image_name=f"{img_path.stem}_unknown_{j+1}"
                    )
                    
                    if ocr_text:
                        ocr_success_count += 1
                        total_ocr_success += 1
                        final_category = self.classify_unknown_text(ocr_text)
                        total_classified += 1
                        
                        print(f"      ✅ OCR识别: '{ocr_text}'")
                        print(f"      🎯 分类结果: {final_category}")
                        
                        image_result['unknown_items'].append({
                            'bbox': box.tolist(),
                            'confidence': float(conf),
                            'ocr_text': ocr_text,
                            'final_category': final_category,
                            'ocr_success': True
                        })
                    else:
                        print(f"      ❌ OCR识别: 无文字")
                        image_result['unknown_items'].append({
                            'bbox': box.tolist(),
                            'confidence': float(conf),
                            'ocr_text': '',
                            'final_category': 'Wxxx_未识别',
                            'ocr_success': False
                        })
                else:
                    print(f"    ✓ {category_name}: 置信度 {conf:.3f}")
                    image_result['other_items'].append({
                        'category': category_name,
                        'confidence': float(conf)
                    })
            
            image_result['unknown_count'] = unknown_count
            image_result['ocr_success_count'] = ocr_success_count
            all_results.append(image_result)
            
            print(f"  📊 本图统计: {unknown_count}个未知物品, {ocr_success_count}个OCR成功")
        
        # 生成汇总报告
        self.generate_real_test_report(all_results, total_unknown_detected, total_ocr_success, total_classified)
        
        return all_results
    
    def generate_real_test_report(self, results, total_unknown, total_ocr_success, total_classified):
        """生成真实图像测试报告"""
        print("\n" + "=" * 80)
        print("📊 真实未知物品OCR测试汇总报告")
        print("=" * 80)
        
        total_images = len(results)
        ocr_success_rate = (total_ocr_success / total_unknown * 100) if total_unknown > 0 else 0
        
        print(f"🖼️  测试图像数量: {total_images}")
        print(f"🔍 检测到未知物品总数: {total_unknown}")
        print(f"📝 OCR识别成功数量: {total_ocr_success}")
        print(f"🎯 成功分类数量: {total_classified}")
        print(f"📈 OCR成功率: {ocr_success_rate:.1f}%")
        
        # 分类统计
        category_stats = {}
        for result in results:
            for item in result['unknown_items']:
                if item['ocr_success']:
                    category = item['final_category']
                    category_stats[category] = category_stats.get(category, 0) + 1
        
        if category_stats:
            print(f"\n📋 识别到的类别分布:")
            for category, count in sorted(category_stats.items()):
                print(f"  {category}: {count} 个")
        
        # 保存详细结果
        report = {
            'test_info': {
                'description': '真实未知物品OCR识别测试',
                'images_path': self.real_images_path,
                'test_time': str(Path().cwd())
            },
            'summary': {
                'total_images': total_images,
                'total_unknown_items': total_unknown,
                'ocr_success_count': total_ocr_success,
                'classification_count': total_classified,
                'ocr_success_rate': float(ocr_success_rate)
            },
            'category_statistics': category_stats,
            'detailed_results': results
        }
        
        report_file = "real_unknown_items_ocr_results.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 详细结果已保存: {report_file}")
        print(f"🖼️  调试图像已保存到: real_ocr_debug/")
        
        return report

def main():
    print("=== 真实未知物品OCR识别测试 ===")
    
    try:
        # 创建测试器
        tester = RealUnknownItemsOCRTester()
        
        # 加载模型
        if not tester.load_models():
            return
        
        # 运行测试
        results = tester.test_real_unknown_images(max_images=15)  # 测试15张图像
        
        if results:
            print(f"\n🎉 真实未知物品OCR测试完成!")
            print(f"📊 请查看详细结果文件和调试图像")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
