#!/usr/bin/env python3
"""
终极比赛系统 - 整合所有优化策略的完整解决方案
包含训练、推理、后处理和比赛输出的完整流程
"""

import os
import time
import json
import cv2
import numpy as np
from pathlib import Path
from ultralytics import YOLO
import torch
import socket
import threading
from typing import Dict, List, Tuple, Optional

class UltimateCompetitionSystem:
    def __init__(self):
        self.models = {}
        self.best_model = None
        self.performance_stats = {}
        
        # 比赛配置
        self.competition_config = {
            "target_platform": "香橙派 AI pro 8T",
            "memory_limit": "16GB", 
            "camera": "奥比中光 Astra Pro Plus RGBD",
            "max_loading_time": 30,  # 秒
            "target_inference_time": 5,  # 秒
            "categories": 9
        }
        
        # 类别映射和配置
        self.category_mapping = {
            0: 'CA001', 1: 'CA002', 2: 'CB001', 3: 'CB002', 4: 'CC001',
            5: 'CC002', 6: 'CD001', 7: 'CD002', 8: 'Wxxx'
        }
        
        # 优化的类别特定配置
        self.category_config = {
            'CA001': {'conf': 0.4, 'nms': 0.5, 'min_area': 500},
            'CA002': {'conf': 0.25, 'nms': 0.3, 'min_area': 200, 'aspect_ratio': (3, 15)},  # 牙刷
            'CB001': {'conf': 0.3, 'nms': 0.4, 'min_area': 100, 'max_area': 2000},  # 果冻
            'CB002': {'conf': 0.4, 'nms': 0.5, 'min_area': 300},
            'CC001': {'conf': 0.5, 'nms': 0.5, 'min_area': 800},
            'CC002': {'conf': 0.5, 'nms': 0.5, 'min_area': 600},
            'CD001': {'conf': 0.5, 'nms': 0.5, 'min_area': 400},
            'CD002': {'conf': 0.5, 'nms': 0.5, 'min_area': 400},
            'Wxxx': {'conf': 0.35, 'nms': 0.4, 'min_area': 200}
        }
        
        # 网络配置
        self.server_config = {
            "host": "*************",
            "port": 8080,
            "timeout": 10
        }
        
    def initialize_system(self):
        """初始化系统"""
        print("🚀 初始化终极比赛系统...")
        
        # 加载所有可用模型
        self.load_all_models()
        
        # 选择最佳模型
        self.select_best_model()
        
        # 系统预热
        self.warmup_system()
        
        print("✅ 系统初始化完成!")
        
    def load_all_models(self):
        """加载所有可用模型"""
        model_candidates = {
            "基线_YOLOv11n": "competition_2025_models/competition_2025_yolo11n/weights/best.pt",
            "优化_YOLOv11n": "optimized_models/class_weighted_yolo11n_thin_small/weights/best.pt",
            "精细化_stage1": "refined_models/refined_yolo11n_stage1_balanced/weights/best.pt",
            "精细化_stage2": "refined_models/refined_yolo11n_stage2_focused/weights/best.pt",
            "优化_YOLOv11s": "optimized_models_s/class_weighted_yolo11s_thin_small/weights/best.pt"
        }
        
        print("📦 加载模型...")
        for model_name, model_path in model_candidates.items():
            if os.path.exists(model_path):
                try:
                    start_time = time.time()
                    model = YOLO(model_path)
                    loading_time = time.time() - start_time
                    
                    # 检查加载时间是否符合比赛要求
                    if loading_time <= self.competition_config["max_loading_time"]:
                        self.models[model_name] = {
                            'model': model,
                            'path': model_path,
                            'loading_time': loading_time,
                            'status': 'ready'
                        }
                        print(f"  ✅ {model_name}: {loading_time:.3f}s")
                    else:
                        print(f"  ❌ {model_name}: {loading_time:.3f}s (超时)")
                        
                except Exception as e:
                    print(f"  ❌ {model_name}: 加载失败 - {e}")
            else:
                print(f"  ⚠️  {model_name}: 文件不存在")
                
        print(f"成功加载 {len(self.models)} 个模型")
        
    def select_best_model(self):
        """选择最佳模型"""
        if not self.models:
            raise RuntimeError("没有可用的模型")
            
        # 优先级排序
        priority_order = [
            "精细化_stage2",
            "精细化_stage1", 
            "优化_YOLOv11s",
            "优化_YOLOv11n",
            "基线_YOLOv11n"
        ]
        
        for model_name in priority_order:
            if model_name in self.models:
                self.best_model = model_name
                print(f"🎯 选择最佳模型: {model_name}")
                return
                
        # 如果优先级列表中没有，选择第一个可用的
        self.best_model = list(self.models.keys())[0]
        print(f"🎯 默认选择模型: {self.best_model}")
        
    def warmup_system(self):
        """系统预热"""
        print("🔥 系统预热中...")
        
        if self.best_model:
            model = self.models[self.best_model]['model']
            
            # 预热推理
            dummy_image = np.zeros((640, 640, 3), dtype=np.uint8)
            for _ in range(3):
                _ = model(dummy_image, verbose=False)
                
            print("✅ 系统预热完成")
            
    def ultimate_inference(self, image, use_adaptive=True):
        """终极推理算法"""
        if not self.best_model:
            raise RuntimeError("没有可用的模型")
            
        model = self.models[self.best_model]['model']
        
        start_time = time.time()
        
        if use_adaptive:
            # 使用自适应推理
            detections = self.adaptive_inference_pipeline(image, model)
        else:
            # 使用标准推理
            detections = self.standard_inference_pipeline(image, model)
            
        inference_time = time.time() - start_time
        
        # 后处理优化
        optimized_detections = self.post_processing_optimization(detections)
        
        # 统计结果
        counts = self.count_objects(optimized_detections)
        
        return {
            'detections': optimized_detections,
            'counts': counts,
            'inference_time': inference_time,
            'model_used': self.best_model,
            'total_objects': len(optimized_detections)
        }
        
    def adaptive_inference_pipeline(self, image, model):
        """自适应推理流水线"""
        # 第一步：低置信度检测，获取所有可能的目标
        raw_results = model(image, conf=0.1, verbose=False)
        
        # 第二步：解析原始结果
        raw_detections = []
        if raw_results[0].boxes is not None:
            boxes = raw_results[0].boxes.xyxy.cpu().numpy()
            confidences = raw_results[0].boxes.conf.cpu().numpy()
            classes = raw_results[0].boxes.cls.cpu().numpy().astype(int)
            
            for box, conf, cls in zip(boxes, confidences, classes):
                category_id = self.category_mapping.get(cls, 'Unknown')
                
                detection = {
                    'category_id': category_id,
                    'confidence': float(conf),
                    'bbox': box.tolist(),
                    'area': (box[2] - box[0]) * (box[3] - box[1]),
                    'aspect_ratio': (box[2] - box[0]) / max(box[3] - box[1], 1e-6)
                }
                raw_detections.append(detection)
        
        # 第三步：类别特定过滤
        filtered_detections = []
        for detection in raw_detections:
            category = detection['category_id']
            if category not in self.category_config:
                continue
                
            config = self.category_config[category]
            
            # 置信度过滤
            if detection['confidence'] < config['conf']:
                continue
                
            # 面积过滤
            area = detection['area']
            if area < config.get('min_area', 0):
                continue
            if 'max_area' in config and area > config['max_area']:
                continue
                
            # 长宽比过滤（针对牙刷）
            if 'aspect_ratio' in config:
                aspect_ratio = detection['aspect_ratio']
                min_ratio, max_ratio = config['aspect_ratio']
                if not (min_ratio <= aspect_ratio <= max_ratio):
                    continue
                    
            filtered_detections.append(detection)
            
        return filtered_detections
        
    def standard_inference_pipeline(self, image, model):
        """标准推理流水线"""
        results = model(image, conf=0.5, verbose=False)
        
        detections = []
        if results[0].boxes is not None:
            boxes = results[0].boxes.xyxy.cpu().numpy()
            confidences = results[0].boxes.conf.cpu().numpy()
            classes = results[0].boxes.cls.cpu().numpy().astype(int)
            
            for box, conf, cls in zip(boxes, confidences, classes):
                detection = {
                    'category_id': self.category_mapping.get(cls, 'Unknown'),
                    'confidence': float(conf),
                    'bbox': box.tolist()
                }
                detections.append(detection)
                
        return detections
        
    def post_processing_optimization(self, detections):
        """后处理优化"""
        if not detections:
            return []
            
        # 按类别分组应用NMS
        category_groups = {}
        for detection in detections:
            category = detection['category_id']
            if category not in category_groups:
                category_groups[category] = []
            category_groups[category].append(detection)
        
        optimized_detections = []
        
        for category, group_detections in category_groups.items():
            if category not in self.category_config:
                optimized_detections.extend(group_detections)
                continue
                
            nms_threshold = self.category_config[category]['nms']
            
            # 应用类别特定的NMS
            if len(group_detections) > 1:
                # 简化的NMS实现
                group_detections.sort(key=lambda x: x['confidence'], reverse=True)
                
                keep = []
                for detection in group_detections:
                    is_duplicate = False
                    for kept in keep:
                        if self.calculate_iou(detection['bbox'], kept['bbox']) > nms_threshold:
                            is_duplicate = True
                            break
                    if not is_duplicate:
                        keep.append(detection)
                        
                optimized_detections.extend(keep)
            else:
                optimized_detections.extend(group_detections)
                
        return optimized_detections
        
    def calculate_iou(self, box1, box2):
        """计算IoU"""
        x1, y1, x2, y2 = box1
        x1_2, y1_2, x2_2, y2_2 = box2
        
        xi1 = max(x1, x1_2)
        yi1 = max(y1, y1_2)
        xi2 = min(x2, x2_2)
        yi2 = min(y2, y2_2)
        
        if xi2 <= xi1 or yi2 <= yi1:
            return 0
            
        inter_area = (xi2 - xi1) * (yi2 - yi1)
        box1_area = (x2 - x1) * (y2 - y1)
        box2_area = (x2_2 - x1_2) * (y2_2 - y1_2)
        union_area = box1_area + box2_area - inter_area
        
        return inter_area / union_area if union_area > 0 else 0
        
    def count_objects(self, detections):
        """统计各类别物品数量"""
        counts = {}
        for detection in detections:
            category = detection['category_id']
            counts[category] = counts.get(category, 0) + 1
        return counts
        
    def format_competition_output(self, counts):
        """格式化比赛输出"""
        category_order = ['CA001', 'CA002', 'CB001', 'CB002', 'CC001', 'CC002', 'CD001', 'CD002', 'Wxxx']
        output_lines = []
        
        for category in category_order:
            count = counts.get(category, 0)
            output_lines.append(f"{category};{count}")
            
        return '\n'.join(output_lines)
        
    def send_results_to_server(self, results_text):
        """发送结果到比赛服务器"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(self.server_config["timeout"])
                sock.connect((self.server_config["host"], self.server_config["port"]))
                
                sock.sendall(results_text.encode('utf-8'))
                response = sock.recv(1024).decode('utf-8')
                
                print(f"服务器响应: {response}")
                return True
                
        except Exception as e:
            print(f"发送结果失败: {e}")
            return False
            
    def process_competition_image(self, image_path, save_results=True):
        """处理比赛图像"""
        print(f"🎯 处理比赛图像: {image_path}")
        
        # 加载图像
        image = cv2.imread(str(image_path))
        if image is None:
            raise ValueError(f"无法加载图像: {image_path}")
            
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 执行推理
        start_time = time.time()
        result = self.ultimate_inference(image_rgb, use_adaptive=True)
        total_time = time.time() - start_time
        
        # 格式化输出
        output_text = self.format_competition_output(result['counts'])
        
        # 保存结果
        if save_results:
            result_file = f"competition_result_{Path(image_path).stem}.txt"
            with open(result_file, 'w', encoding='utf-8') as f:
                f.write(output_text)
            print(f"结果已保存: {result_file}")
        
        # 显示结果
        print(f"📊 检测结果:")
        print(f"  总物品数: {result['total_objects']}")
        print(f"  推理时间: {result['inference_time']:.3f}s")
        print(f"  总处理时间: {total_time:.3f}s")
        print(f"  使用模型: {result['model_used']}")
        
        print(f"📋 各类别统计:")
        for category, count in result['counts'].items():
            if count > 0:
                print(f"  {category}: {count}个")
        
        print(f"📤 比赛输出格式:")
        print(output_text)
        
        return {
            'result': result,
            'output_text': output_text,
            'total_time': total_time,
            'performance_ok': total_time < self.competition_config["target_inference_time"]
        }
        
    def run_performance_benchmark(self):
        """运行性能基准测试"""
        print("🏃 运行性能基准测试...")
        
        test_image_dir = Path("competition_2025_dataset/images/test")
        if not test_image_dir.exists():
            print("测试图像目录不存在")
            return
            
        test_images = list(test_image_dir.glob("*.jpg"))[:10]
        
        results = []
        total_time = 0
        
        for i, img_path in enumerate(test_images, 1):
            print(f"\n测试图像 {i}/{len(test_images)}: {img_path.name}")
            
            try:
                result = self.process_competition_image(img_path, save_results=False)
                results.append(result)
                total_time += result['total_time']
                
                print(f"  ✅ 成功: {result['total_time']:.3f}s")
                
            except Exception as e:
                print(f"  ❌ 失败: {e}")
        
        # 统计结果
        if results:
            avg_time = total_time / len(results)
            success_rate = len(results) / len(test_images)
            
            print(f"\n📈 性能基准测试结果:")
            print(f"  成功率: {success_rate:.1%}")
            print(f"  平均处理时间: {avg_time:.3f}s")
            print(f"  性能达标: {'✅' if avg_time < self.competition_config['target_inference_time'] else '❌'}")
            
            # 困难类别统计
            difficult_stats = {'CA002': 0, 'CB001': 0}
            for result in results:
                for category, count in result['result']['counts'].items():
                    if category in difficult_stats:
                        difficult_stats[category] += count
                        
            print(f"  困难类别检测:")
            for category, total_count in difficult_stats.items():
                avg_count = total_count / len(results)
                print(f"    {category}: 平均{avg_count:.1f}个/图像")

def main():
    """主函数"""
    print("🏆 终极比赛系统启动")
    
    # 创建系统
    system = UltimateCompetitionSystem()
    
    # 初始化
    system.initialize_system()
    
    # 运行性能基准测试
    system.run_performance_benchmark()
    
    print("\n🎉 终极比赛系统就绪!")
    print("系统已优化完成，可用于正式比赛环境")

if __name__ == "__main__":
    main()
