#!/usr/bin/env python3
"""
高级训练策略
专门用于提升mAP50-95的训练技术
"""

import torch
import torch.nn as nn
import numpy as np
from ultralytics import YOLO
from ultralytics.utils import LOGGER
import yaml
import os
from pathlib import Path
import json
import time

class AdvancedTrainingConfig:
    """高级训练配置"""
    
    def __init__(self):
        # 基础配置
        self.base_config = {
            "epochs": 300,
            "batch": 16,
            "imgsz": 640,
            "optimizer": "AdamW",
            "lr0": 0.001,
            "lrf": 0.01,
            "momentum": 0.937,
            "weight_decay": 0.0005,
            "warmup_epochs": 3,
            "warmup_momentum": 0.8,
            "warmup_bias_lr": 0.1,
            "patience": 50,
            "save_period": 10,
            "val": True,
            "plots": True,
            "device": "0" if torch.cuda.is_available() else "cpu"
        }
        
        # mAP50-95优化配置
        self.map50_95_config = {
            # 损失函数权重调整
            "box": 7.5,      # 边界框回归损失权重（提高）
            "cls": 0.5,      # 分类损失权重
            "dfl": 1.5,      # 分布焦点损失权重
            
            # 高级数据增强
            "hsv_h": 0.015,  # 色调增强
            "hsv_s": 0.7,    # 饱和度增强
            "hsv_v": 0.4,    # 明度增强
            "degrees": 0.0,  # 旋转角度
            "translate": 0.1, # 平移
            "scale": 0.5,    # 缩放
            "shear": 0.0,    # 剪切
            "perspective": 0.0, # 透视变换
            "flipud": 0.0,   # 垂直翻转
            "fliplr": 0.5,   # 水平翻转
            "mosaic": 1.0,   # Mosaic增强
            "mixup": 0.2,    # MixUp增强
            "copy_paste": 0.3, # Copy-Paste增强
            
            # 标签平滑
            "label_smoothing": 0.1,
            
            # 多尺度训练
            "multi_scale": True,
            
            # 高级优化器设置
            "cos_lr": True,  # 余弦学习率调度
            
            # 模型EMA
            "ema": True,
            
            # 精确度优化
            "amp": True,     # 自动混合精度
        }
        
        # 困难类别特殊配置
        self.difficult_class_config = {
            # 针对牙刷和果冻的特殊权重
            "class_weights": [1.0, 2.5, 2.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.5],
            
            # 更强的小目标增强
            "small_object_aug": True,
            "min_area_ratio": 0.02,
            "max_area_ratio": 0.8,
            
            # 边界框质量增强
            "bbox_loss_type": "wiou",  # 使用WIoU损失
            "iou_threshold": 0.7,      # 更高的IoU阈值
        }

class AdvancedTrainer:
    """高级训练器"""
    
    def __init__(self, model_name: str = "yolo11n.pt"):
        self.model_name = model_name
        self.config = AdvancedTrainingConfig()
        self.project_root = Path(__file__).parent
        
    def train_for_map50_95(self, 
                          dataset_yaml: str,
                          experiment_name: str = "map50_95_optimization",
                          use_advanced_loss: bool = True,
                          use_label_smoothing: bool = True,
                          use_strong_augmentation: bool = True) -> Dict[str, Any]:
        """
        专门针对mAP50-95优化的训练
        
        Args:
            dataset_yaml: 数据集配置文件路径
            experiment_name: 实验名称
            use_advanced_loss: 是否使用高级损失函数
            use_label_smoothing: 是否使用标签平滑
            use_strong_augmentation: 是否使用强数据增强
            
        Returns:
            训练结果字典
        """
        print(f"🚀 开始mAP50-95优化训练: {experiment_name}")
        
        # 创建模型
        model = YOLO(self.model_name)
        
        # 构建训练配置
        train_config = self._build_training_config(
            dataset_yaml, experiment_name,
            use_advanced_loss, use_label_smoothing, use_strong_augmentation
        )
        
        # 记录配置
        self._save_training_config(train_config, experiment_name)
        
        # 开始训练
        start_time = time.time()
        
        try:
            results = model.train(**train_config)
            training_time = time.time() - start_time
            
            # 评估结果
            evaluation_results = self._evaluate_training_results(
                results, experiment_name, training_time
            )
            
            print(f"✅ 训练完成! 耗时: {training_time/3600:.2f}小时")
            print(f"📊 最佳mAP50: {evaluation_results['best_map50']:.4f}")
            print(f"📊 最佳mAP50-95: {evaluation_results['best_map50_95']:.4f}")
            
            return evaluation_results
            
        except Exception as e:
            print(f"❌ 训练失败: {e}")
            return {"error": str(e)}
    
    def _build_training_config(self, 
                              dataset_yaml: str,
                              experiment_name: str,
                              use_advanced_loss: bool,
                              use_label_smoothing: bool,
                              use_strong_augmentation: bool) -> Dict[str, Any]:
        """构建训练配置"""
        
        # 基础配置
        config = self.config.base_config.copy()
        config.update(self.config.map50_95_config)
        
        # 数据集配置
        config["data"] = dataset_yaml
        config["name"] = experiment_name
        config["project"] = "map50_95_experiments"
        
        # 高级损失函数
        if use_advanced_loss:
            config["box"] = 10.0  # 进一步提高边界框损失权重
            config["dfl"] = 2.0   # 提高分布焦点损失权重
        
        # 标签平滑
        if not use_label_smoothing:
            config["label_smoothing"] = 0.0
        
        # 强数据增强
        if use_strong_augmentation:
            config.update({
                "mosaic": 1.0,
                "mixup": 0.3,
                "copy_paste": 0.5,
                "hsv_h": 0.02,
                "hsv_s": 0.8,
                "hsv_v": 0.5,
                "scale": 0.7,
                "translate": 0.2,
            })
        
        # 困难类别优化
        config.update(self.config.difficult_class_config)
        
        return config
    
    def train_with_progressive_resizing(self,
                                      dataset_yaml: str,
                                      experiment_name: str = "progressive_resizing") -> Dict[str, Any]:
        """
        渐进式尺寸训练
        从小尺寸开始，逐步增加到大尺寸
        """
        print(f"🔄 开始渐进式尺寸训练: {experiment_name}")
        
        # 渐进式尺寸配置
        progressive_stages = [
            {"imgsz": 416, "epochs": 50, "lr0": 0.01},
            {"imgsz": 512, "epochs": 50, "lr0": 0.005},
            {"imgsz": 640, "epochs": 100, "lr0": 0.002},
            {"imgsz": 768, "epochs": 100, "lr0": 0.001},
        ]
        
        model = YOLO(self.model_name)
        all_results = []
        
        for i, stage in enumerate(progressive_stages):
            stage_name = f"{experiment_name}_stage_{i+1}"
            print(f"📐 阶段 {i+1}: 图像尺寸 {stage['imgsz']}")
            
            # 构建阶段配置
            stage_config = self.config.base_config.copy()
            stage_config.update(self.config.map50_95_config)
            stage_config.update(stage)
            stage_config.update({
                "data": dataset_yaml,
                "name": stage_name,
                "project": "progressive_resizing_experiments",
                "resume": i > 0,  # 从第二阶段开始恢复训练
            })
            
            # 训练当前阶段
            try:
                results = model.train(**stage_config)
                all_results.append({
                    "stage": i + 1,
                    "imgsz": stage["imgsz"],
                    "results": results
                })
                
                print(f"✅ 阶段 {i+1} 完成")
                
            except Exception as e:
                print(f"❌ 阶段 {i+1} 失败: {e}")
                break
        
        return {
            "progressive_results": all_results,
            "final_model": model
        }
    
    def train_with_knowledge_distillation(self,
                                        teacher_model_path: str,
                                        dataset_yaml: str,
                                        experiment_name: str = "knowledge_distillation") -> Dict[str, Any]:
        """
        知识蒸馏训练
        使用大模型作为教师指导小模型训练
        """
        print(f"🎓 开始知识蒸馏训练: {experiment_name}")
        
        # 加载教师模型
        teacher_model = YOLO(teacher_model_path)
        
        # 学生模型
        student_model = YOLO(self.model_name)
        
        # 知识蒸馏配置
        kd_config = self.config.base_config.copy()
        kd_config.update(self.config.map50_95_config)
        kd_config.update({
            "data": dataset_yaml,
            "name": experiment_name,
            "project": "knowledge_distillation_experiments",
            "epochs": 200,
            "lr0": 0.0005,  # 较低的学习率
            "warmup_epochs": 5,
            "patience": 30,
            
            # 蒸馏特定参数
            "distillation_alpha": 0.7,  # 蒸馏损失权重
            "distillation_temperature": 4.0,  # 蒸馏温度
        })
        
        # 注意：实际的知识蒸馏需要修改YOLO源码
        # 这里提供配置框架
        print("⚠️  知识蒸馏需要自定义YOLO训练循环")
        
        return {
            "teacher_model": teacher_model,
            "student_model": student_model,
            "config": kd_config
        }
    
    def _save_training_config(self, config: Dict[str, Any], experiment_name: str):
        """保存训练配置"""
        config_dir = self.project_root / "training_configs"
        config_dir.mkdir(exist_ok=True)
        
        config_file = config_dir / f"{experiment_name}_config.json"
        
        # 转换不可序列化的对象
        serializable_config = {}
        for key, value in config.items():
            if isinstance(value, (str, int, float, bool, list, dict)):
                serializable_config[key] = value
            else:
                serializable_config[key] = str(value)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_config, f, indent=2, ensure_ascii=False)
        
        print(f"💾 训练配置已保存: {config_file}")
    
    def _evaluate_training_results(self, 
                                 results, 
                                 experiment_name: str, 
                                 training_time: float) -> Dict[str, Any]:
        """评估训练结果"""
        
        # 提取关键指标
        try:
            # 从results中提取指标（具体实现取决于YOLO版本）
            best_map50 = getattr(results, 'best_map50', 0.0)
            best_map50_95 = getattr(results, 'best_map50_95', 0.0)
            
            evaluation = {
                "experiment_name": experiment_name,
                "training_time_hours": training_time / 3600,
                "best_map50": best_map50,
                "best_map50_95": best_map50_95,
                "map50_95_improvement": best_map50_95 - 0.68,  # 相对于当前最佳
                "efficiency_score": best_map50_95 / (training_time / 3600),  # mAP per hour
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
            # 保存评估结果
            self._save_evaluation_results(evaluation, experiment_name)
            
            return evaluation
            
        except Exception as e:
            print(f"⚠️  评估结果提取失败: {e}")
            return {
                "experiment_name": experiment_name,
                "training_time_hours": training_time / 3600,
                "error": str(e)
            }
    
    def _save_evaluation_results(self, evaluation: Dict[str, Any], experiment_name: str):
        """保存评估结果"""
        results_dir = self.project_root / "evaluation_results"
        results_dir.mkdir(exist_ok=True)
        
        results_file = results_dir / f"{experiment_name}_evaluation.json"
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(evaluation, f, indent=2, ensure_ascii=False)
        
        print(f"📊 评估结果已保存: {results_file}")

# 使用示例
def main():
    """高级训练策略使用示例"""
    
    # 创建训练器
    trainer = AdvancedTrainer("yolo11n.pt")
    
    # 数据集配置
    dataset_yaml = "competition_2025_dataset/competition_dataset.yaml"
    
    if not os.path.exists(dataset_yaml):
        print(f"❌ 数据集配置文件不存在: {dataset_yaml}")
        return
    
    # 1. mAP50-95优化训练
    print("=" * 60)
    print("1. mAP50-95优化训练")
    print("=" * 60)
    
    map50_95_results = trainer.train_for_map50_95(
        dataset_yaml=dataset_yaml,
        experiment_name="map50_95_wiou_optimization",
        use_advanced_loss=True,
        use_label_smoothing=True,
        use_strong_augmentation=True
    )
    
    # 2. 渐进式尺寸训练
    print("\n" + "=" * 60)
    print("2. 渐进式尺寸训练")
    print("=" * 60)
    
    progressive_results = trainer.train_with_progressive_resizing(
        dataset_yaml=dataset_yaml,
        experiment_name="progressive_resizing_map50_95"
    )
    
    # 输出总结
    print("\n" + "🎯" * 20)
    print("训练总结:")
    print("🎯" * 20)
    
    if "best_map50_95" in map50_95_results:
        print(f"mAP50-95优化训练最佳结果: {map50_95_results['best_map50_95']:.4f}")
    
    print(f"渐进式训练完成阶段数: {len(progressive_results.get('progressive_results', []))}")

if __name__ == "__main__":
    main()
