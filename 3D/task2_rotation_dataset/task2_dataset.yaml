# Task2旋转数据集配置
path: /home/<USER>/homes/ch/claude/SpatialVLA/3D/task2_rotation_dataset
train: images/train
val: images/val
test: images/test

# 类别配置
nc: 9
names: ['Wxxx', 'CD002', 'CD001', 'CC002', 'CC001', 'CB002', 'CB001', 'CA002', 'CA001']

# 深度图像路径
depth_train: depth/train
depth_val: depth/val
depth_test: depth/test

# Task2旋转场景特定配置
rotation_scenario: true
competition_format: true
output_format: "ID;Num"

# 数据集统计
total_groups: 19
total_images: 103
total_annotations: 1199
