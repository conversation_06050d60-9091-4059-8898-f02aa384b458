#!/usr/bin/env python3
"""
精细化训练系统 - 基于实际推理测试结果的改进
解决过度优化问题，实现训练指标与实际性能的统一
"""

import os
import time
import torch
import yaml
from pathlib import Path
from ultralytics import YOLO
import json
import numpy as np

class RefinedTrainingSystem:
    def __init__(self, dataset_path="competition_2025_dataset", output_dir="refined_models"):
        self.dataset_path = Path(dataset_path)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 基于测试结果的改进策略
        self.improvement_strategy = {
            "problem_analysis": {
                "training_vs_inference_gap": "训练指标提升但实际检测数量下降",
                "overfitting_risk": "过度的类别权重(3倍)导致过拟合",
                "confidence_threshold_issue": "默认0.5阈值对困难类别过高"
            },
            "solution_approach": {
                "moderate_class_weights": "使用温和的1.5倍权重",
                "balanced_augmentation": "减少过度增强，保持数据真实性",
                "adaptive_thresholds": "为不同类别设置不同置信度阈值",
                "multi_stage_training": "渐进式训练策略"
            }
        }
        
    def create_refined_config(self, model_name="yolo11n", stage="balanced"):
        """创建精细化的训练配置"""
        
        if stage == "balanced":
            # 第一阶段：平衡训练
            config = {
                "model": f"{model_name}.pt",
                "data": str(self.dataset_path / "competition_dataset.yaml"),
                
                # 训练参数 - 平衡策略
                "epochs": 200,
                "batch": 16,
                "imgsz": [640, 672, 704],  # 减少多尺度范围
                "device": 0 if torch.cuda.is_available() else "cpu",
                
                # 优化器配置
                "optimizer": "AdamW",
                "lr0": 0.001,
                "lrf": 0.01,
                "momentum": 0.937,
                "weight_decay": 0.0005,
                "warmup_epochs": 5,
                "warmup_momentum": 0.8,
                "warmup_bias_lr": 0.1,
                
                # 温和的数据增强
                "hsv_h": 0.01,     # 减少色调变化
                "hsv_s": 0.5,      # 减少饱和度变化
                "hsv_v": 0.3,      # 减少亮度变化
                "degrees": 3.0,    # 减少旋转角度
                "translate": 0.05, # 减少平移
                "scale": 0.7,      # 减少缩放范围
                "shear": 0.0,      # 避免剪切
                "perspective": 0.0, # 避免透视变换
                "flipud": 0.0,     # 避免上下翻转
                "fliplr": 0.5,     # 保持左右翻转
                "mosaic": 1.0,     # 保持马赛克
                "mixup": 0.05,     # 减少混合增强
                "copy_paste": 0.1, # 大幅减少copy-paste
                
                # 平衡的损失函数
                "cls": 1.0,
                "box": 7.5,
                "dfl": 1.5,
                
                # 验证和保存
                "val": True,
                "save": True,
                "save_period": 20,
                "plots": True,
                "patience": 40,
                
                "project": str(self.output_dir),
                "name": f"refined_{model_name}_stage1_balanced",
                "exist_ok": True,
            }
            
        elif stage == "focused":
            # 第二阶段：针对困难类别的微调
            config = {
                "model": f"{model_name}.pt",
                "data": str(self.dataset_path / "refined_dataset.yaml"),
                
                # 微调参数
                "epochs": 100,
                "batch": 16,
                "imgsz": 640,
                "device": 0 if torch.cuda.is_available() else "cpu",
                
                # 更低的学习率用于微调
                "optimizer": "AdamW",
                "lr0": 0.0005,  # 降低学习率
                "lrf": 0.01,
                "momentum": 0.937,
                "weight_decay": 0.0005,
                "warmup_epochs": 3,
                
                # 针对困难类别的增强
                "hsv_h": 0.015,
                "hsv_s": 0.6,
                "hsv_v": 0.4,
                "degrees": 5.0,
                "translate": 0.1,
                "scale": 0.8,
                "shear": 0.0,
                "perspective": 0.0,
                "flipud": 0.0,
                "fliplr": 0.5,
                "mosaic": 1.0,
                "mixup": 0.1,
                "copy_paste": 0.15,  # 适度增加
                
                # 针对困难类别的损失权重
                "cls": 1.0,
                "box": 10.0,  # 增加边界框权重
                "dfl": 2.0,
                
                "val": True,
                "save": True,
                "save_period": 10,
                "plots": True,
                "patience": 20,
                
                "project": str(self.output_dir),
                "name": f"refined_{model_name}_stage2_focused",
                "exist_ok": True,
            }
            
        return config
        
    def create_refined_dataset_config(self, stage="balanced"):
        """创建精细化的数据集配置"""
        
        if stage == "balanced":
            # 平衡训练阶段 - 不使用类别权重
            config_content = f"""# 精细化数据集配置 - 平衡阶段
path: {self.dataset_path.absolute()}
train: images/train
val: images/val
test: images/test

nc: 9
names: ['CA001_衣架', 'CA002_牙刷', 'CB001_果冻', 'CB002_长方形状饼干',
        'CC001_罐装饮料', 'CC002_瓶装饮料', 'CD001_香蕉', 'CD002_橙子', 'Wxxx_未知物品']

# 平衡训练 - 不使用类别权重
"""
            
        elif stage == "focused":
            # 微调阶段 - 使用温和的类别权重
            config_content = f"""# 精细化数据集配置 - 微调阶段
path: {self.dataset_path.absolute()}
train: images/train
val: images/val
test: images/test

nc: 9
names: ['CA001_衣架', 'CA002_牙刷', 'CB001_果冻', 'CB002_长方形状饼干',
        'CC001_罐装饮料', 'CC002_瓶装饮料', 'CD001_香蕉', 'CD002_橙子', 'Wxxx_未知物品']

# 温和的类别权重 - 避免过拟合
class_weights: [1.0, 1.5, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.2]
# 对应: [衣架, 牙刷, 果冻, 饼干, 罐装饮料, 瓶装饮料, 香蕉, 橙子, 未知物品]
"""
        
        config_file = self.dataset_path / f"refined_dataset.yaml"
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
            
        return config_file
        
    def train_refined_model(self, model_name="yolo11n", two_stage=True):
        """精细化训练流程"""
        print(f"\n{'='*60}")
        print(f"开始精细化训练: {model_name}")
        print(f"两阶段训练: {two_stage}")
        print(f"{'='*60}")
        
        results = {}
        
        try:
            # 第一阶段：平衡训练
            print(f"\n🎯 第一阶段：平衡训练")
            self.create_refined_dataset_config("balanced")
            config_stage1 = self.create_refined_config(model_name, "balanced")
            
            model = YOLO(config_stage1["model"])
            start_time = time.time()
            
            stage1_results = model.train(**{k: v for k, v in config_stage1.items() 
                                          if k not in ["model"]})
            
            stage1_time = time.time() - start_time
            
            # 获取第一阶段最佳模型
            stage1_model_path = self.output_dir / config_stage1["name"] / "weights" / "best.pt"
            
            # 评估第一阶段结果
            stage1_model = YOLO(str(stage1_model_path))
            stage1_val_results = stage1_model.val(data=config_stage1["data"], split='val')
            
            results["stage1"] = {
                "training_time_hours": stage1_time / 3600,
                "model_path": str(stage1_model_path),
                "metrics": {
                    "mAP50": float(stage1_val_results.box.map50),
                    "mAP50_95": float(stage1_val_results.box.map),
                    "precision": float(stage1_val_results.box.mp),
                    "recall": float(stage1_val_results.box.mr),
                }
            }
            
            print(f"第一阶段完成:")
            print(f"  mAP50: {results['stage1']['metrics']['mAP50']:.4f}")
            print(f"  mAP50-95: {results['stage1']['metrics']['mAP50_95']:.4f}")
            
            if two_stage:
                # 第二阶段：针对困难类别微调
                print(f"\n🎯 第二阶段：困难类别微调")
                self.create_refined_dataset_config("focused")
                config_stage2 = self.create_refined_config(model_name, "focused")
                
                # 从第一阶段的最佳模型开始微调
                config_stage2["model"] = str(stage1_model_path)
                
                start_time = time.time()
                stage2_results = stage1_model.train(**{k: v for k, v in config_stage2.items() 
                                                      if k not in ["model"]})
                
                stage2_time = time.time() - start_time
                
                # 获取第二阶段最佳模型
                stage2_model_path = self.output_dir / config_stage2["name"] / "weights" / "best.pt"
                
                # 评估第二阶段结果
                stage2_model = YOLO(str(stage2_model_path))
                stage2_val_results = stage2_model.val(data=config_stage2["data"], split='val')
                
                results["stage2"] = {
                    "training_time_hours": stage2_time / 3600,
                    "model_path": str(stage2_model_path),
                    "metrics": {
                        "mAP50": float(stage2_val_results.box.map50),
                        "mAP50_95": float(stage2_val_results.box.map),
                        "precision": float(stage2_val_results.box.mp),
                        "recall": float(stage2_val_results.box.mr),
                    }
                }
                
                print(f"第二阶段完成:")
                print(f"  mAP50: {results['stage2']['metrics']['mAP50']:.4f}")
                print(f"  mAP50-95: {results['stage2']['metrics']['mAP50_95']:.4f}")
                
                # 选择最佳模型
                if results["stage2"]["metrics"]["mAP50"] > results["stage1"]["metrics"]["mAP50"]:
                    best_model_path = stage2_model_path
                    best_stage = "stage2"
                else:
                    best_model_path = stage1_model_path
                    best_stage = "stage1"
            else:
                best_model_path = stage1_model_path
                best_stage = "stage1"
            
            # 测试推理性能
            inference_results = self.test_inference_performance(best_model_path)
            
            # 汇总结果
            final_results = {
                "model_name": model_name,
                "training_approach": "refined_two_stage" if two_stage else "refined_single_stage",
                "best_stage": best_stage,
                "best_model_path": str(best_model_path),
                "stages": results,
                "inference_performance": inference_results,
                "total_training_time": sum(stage["training_time_hours"] for stage in results.values())
            }
            
            # 保存结果
            result_file = self.output_dir / f"refined_training_results_{model_name}.json"
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(final_results, f, indent=2, ensure_ascii=False)
            
            print(f"\n✅ 精细化训练完成!")
            print(f"最佳模型: {best_stage}")
            print(f"最终mAP50: {results[best_stage]['metrics']['mAP50']:.4f}")
            print(f"模型路径: {best_model_path}")
            
            return final_results
            
        except Exception as e:
            print(f"精细化训练失败: {e}")
            return None
            
    def test_inference_performance(self, model_path):
        """测试推理性能"""
        print(f"\n🔍 测试推理性能...")
        
        try:
            model = YOLO(str(model_path))
            
            # 测试图像
            test_image_dir = self.dataset_path / "images" / "test"
            test_images = list(test_image_dir.glob("*.jpg"))[:10]
            
            if not test_images:
                return {"error": "没有测试图像"}
            
            # 测试不同置信度阈值
            thresholds = [0.3, 0.4, 0.5]
            results = {}
            
            for threshold in thresholds:
                total_detections = 0
                difficult_detections = {"CA002_牙刷": 0, "CB001_果冻": 0}
                inference_times = []
                
                for image_path in test_images:
                    import cv2
                    image = cv2.imread(str(image_path))
                    if image is None:
                        continue
                        
                    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    
                    start_time = time.time()
                    model_results = model(image_rgb, conf=threshold, verbose=False)
                    inference_time = time.time() - start_time
                    inference_times.append(inference_time)
                    
                    if model_results[0].boxes is not None:
                        classes = model_results[0].boxes.cls.cpu().numpy().astype(int)
                        
                        category_mapping = {
                            0: 'CA001_衣架', 1: 'CA002_牙刷', 2: 'CB001_果冻',
                            3: 'CB002_长方形状饼干', 4: 'CC001_罐装饮料', 5: 'CC002_瓶装饮料',
                            6: 'CD001_香蕉', 7: 'CD002_橙子', 8: 'Wxxx_未知物品'
                        }
                        
                        for cls in classes:
                            total_detections += 1
                            category_name = category_mapping.get(int(cls), 'Unknown')
                            if category_name in difficult_detections:
                                difficult_detections[category_name] += 1
                
                results[f"threshold_{threshold}"] = {
                    "avg_total_detections": total_detections / len(test_images),
                    "avg_difficult_detections": {k: v / len(test_images) 
                                               for k, v in difficult_detections.items()},
                    "avg_inference_time": np.mean(inference_times)
                }
            
            return results
            
        except Exception as e:
            return {"error": str(e)}

def main():
    """主函数"""
    print("=== 精细化训练系统 ===")
    print("基于实际推理测试结果的系统性改进")
    
    # 检查数据集
    dataset_path = "competition_2025_dataset"
    if not Path(dataset_path).exists():
        print(f"错误: 数据集不存在: {dataset_path}")
        return
    
    # 创建精细化训练系统
    trainer = RefinedTrainingSystem(dataset_path)
    
    # 开始精细化训练
    print("\n开始YOLOv11n精细化训练...")
    result = trainer.train_refined_model("yolo11n", two_stage=True)
    
    if result:
        print(f"\n🎉 精细化训练成功完成!")
        print(f"训练方法: {result['training_approach']}")
        print(f"最佳阶段: {result['best_stage']}")
        print(f"总训练时间: {result['total_training_time']:.2f}小时")
        
        # 显示推理性能
        if "inference_performance" in result and "error" not in result["inference_performance"]:
            print(f"\n📊 推理性能测试:")
            for threshold, perf in result["inference_performance"].items():
                if isinstance(perf, dict) and "avg_total_detections" in perf:
                    print(f"  {threshold}: 平均{perf['avg_total_detections']:.1f}个物体, "
                          f"牙刷{perf['avg_difficult_detections']['CA002_牙刷']:.1f}, "
                          f"果冻{perf['avg_difficult_detections']['CB001_果冻']:.1f}")
        
        print(f"\n🚀 下一步:")
        print(f"1. 使用最佳模型进行完整测试")
        print(f"2. 实现自适应置信度阈值推理")
        print(f"3. 考虑多模型集成策略")

if __name__ == "__main__":
    main()
