#!/usr/bin/env python3
"""
2025年比赛数据集准备脚本
根据新的比赛规则重新整理RGBD数据集
"""

import os
import json
import shutil
from pathlib import Path
import cv2
import numpy as np
import yaml
from typing import Dict, List

class Competition2025DatasetPreparator:
    def __init__(self, source_data_dir="3D", output_dir="competition_2025_dataset"):
        self.source_data_dir = Path(source_data_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 2025年比赛的8个类别映射
        self.competition_categories = {
            'CA001': {'id': 0, 'name': 'CA001_衣架'},
            'CA002': {'id': 1, 'name': 'CA002_牙刷'},
            'CB001': {'id': 2, 'name': 'CB001_果冻'},
            'CB002': {'id': 3, 'name': 'CB002_长方形状饼干'},
            'CC001': {'id': 4, 'name': 'CC001_罐装饮料'},
            'CC002': {'id': 5, 'name': 'CC002_瓶装饮料'},
            'CD001': {'id': 6, 'name': 'CD001_香蕉'},
            'CD002': {'id': 7, 'name': 'CD002_橙子'},
            'Wxxx': {'id': 8, 'name': 'Wxxx_未知物品'}
        }
        
        self.setup_output_structure()
        
    def setup_output_structure(self):
        """创建输出目录结构"""
        dirs = [
            self.output_dir / "images" / "train",
            self.output_dir / "images" / "val",
            self.output_dir / "images" / "test",
            self.output_dir / "depth" / "train", 
            self.output_dir / "depth" / "val",
            self.output_dir / "depth" / "test",
            self.output_dir / "labels" / "train",
            self.output_dir / "labels" / "val",
            self.output_dir / "labels" / "test"
        ]
        
        for dir_path in dirs:
            dir_path.mkdir(parents=True, exist_ok=True)
            
    def load_coco_annotations(self, coco_file: str) -> Dict:
        """加载COCO格式标注文件"""
        with open(coco_file, 'r', encoding='utf-8') as f:
            return json.load(f)
            
    def coco_to_yolo_bbox(self, bbox: List[float], img_width: int, img_height: int) -> List[float]:
        """将COCO格式的bbox转换为YOLO格式"""
        x, y, w, h = bbox
        x_center = (x + w / 2) / img_width
        y_center = (y + h / 2) / img_height
        width = w / img_width
        height = h / img_height
        return [x_center, y_center, width, height]
        
    def process_dataset(self, dataset_path: str, dataset_name: str, split_ratio: tuple = (0.7, 0.2, 0.1)):
        """处理单个数据集"""
        dataset_path = Path(dataset_path)
        
        # 查找COCO标注文件
        coco_file = dataset_path / "_annotations.coco.json"
        if not coco_file.exists():
            print(f"警告: 在 {dataset_path} 中未找到COCO标注文件")
            return
            
        print(f"处理数据集: {dataset_name}")
        coco_data = self.load_coco_annotations(coco_file)
        
        # 过滤只保留比赛需要的类别
        valid_categories = set(self.competition_categories.keys())
        filtered_categories = []
        category_id_mapping = {}
        
        for cat in coco_data['categories']:
            if cat['name'] in valid_categories:
                new_id = self.competition_categories[cat['name']]['id']
                category_id_mapping[cat['id']] = new_id
                filtered_categories.append({
                    'id': new_id,
                    'name': cat['name']
                })
        
        # 创建图像ID到文件名的映射
        image_id_to_filename = {img['id']: img['file_name'] for img in coco_data['images']}
        image_id_to_size = {img['id']: (img['width'], img['height']) for img in coco_data['images']}
        
        # 过滤标注，只保留有效类别
        filtered_annotations = []
        annotations_by_image = {}
        
        for ann in coco_data['annotations']:
            if ann['category_id'] in category_id_mapping:
                # 更新类别ID
                ann['category_id'] = category_id_mapping[ann['category_id']]
                filtered_annotations.append(ann)
                
                image_id = ann['image_id']
                if image_id not in annotations_by_image:
                    annotations_by_image[image_id] = []
                annotations_by_image[image_id].append(ann)
        
        # 获取有标注的图像文件
        valid_image_files = []
        for img_info in coco_data['images']:
            if img_info['id'] in annotations_by_image:
                filename = img_info['file_name']
                # 查找RGB图像
                rgb_paths = list(dataset_path.rglob(filename))
                if rgb_paths:
                    valid_image_files.append((img_info['id'], rgb_paths[0]))
        
        print(f"  找到 {len(valid_image_files)} 张有效图像")
        
        # 数据集分割
        total_images = len(valid_image_files)
        train_count = int(total_images * split_ratio[0])
        val_count = int(total_images * split_ratio[1])
        
        splits = {
            'train': valid_image_files[:train_count],
            'val': valid_image_files[train_count:train_count + val_count],
            'test': valid_image_files[train_count + val_count:]
        }
        
        for split_name, split_files in splits.items():
            print(f"  处理 {split_name} 集: {len(split_files)} 张图像")
            
            for image_id, rgb_path in split_files:
                filename = image_id_to_filename[image_id]
                base_name = Path(filename).stem
                
                # 复制RGB图像
                new_rgb_name = f"{dataset_name}_{base_name}.jpg"
                rgb_dst = self.output_dir / "images" / split_name / new_rgb_name
                shutil.copy2(rgb_path, rgb_dst)
                
                # 查找并复制深度图像
                depth_name = f"{base_name}_depth.png"
                depth_paths = list(dataset_path.rglob(depth_name))
                if depth_paths:
                    new_depth_name = f"{dataset_name}_{base_name}_depth.png"
                    depth_dst = self.output_dir / "depth" / split_name / new_depth_name
                    shutil.copy2(depth_paths[0], depth_dst)
                
                # 生成YOLO格式标注
                if image_id in annotations_by_image:
                    img_width, img_height = image_id_to_size[image_id]
                    yolo_annotations = []
                    
                    for ann in annotations_by_image[image_id]:
                        class_id = ann['category_id']
                        bbox = self.coco_to_yolo_bbox(ann['bbox'], img_width, img_height)
                        yolo_annotations.append(f"{class_id} {' '.join(map(str, bbox))}")
                    
                    # 保存YOLO标注文件
                    if yolo_annotations:
                        label_file = self.output_dir / "labels" / split_name / f"{dataset_name}_{base_name}.txt"
                        with open(label_file, 'w') as f:
                            f.write('\n'.join(yolo_annotations))
        
        return len(valid_image_files)
        
    def create_dataset_yaml(self):
        """创建YOLO数据集配置文件"""
        yaml_content = f"""# 2025年比赛RGBD数据集配置
path: {self.output_dir.absolute()}
train: images/train
val: images/val
test: images/test

# 比赛类别 (8个基本类别 + 未知物品)
nc: 9
names: ['CA001_衣架', 'CA002_牙刷', 'CB001_果冻', 'CB002_长方形状饼干',
        'CC001_罐装饮料', 'CC002_瓶装饮料', 'CD001_香蕉', 'CD002_橙子', 'Wxxx_未知物品']

# 深度图像路径
depth_train: depth/train
depth_val: depth/val
depth_test: depth/test

# 比赛特定配置
competition_format: true
output_format: "ID;Num"
"""
        
        yaml_file = self.output_dir / "competition_dataset.yaml"
        with open(yaml_file, 'w', encoding='utf-8') as f:
            f.write(yaml_content)
            
        print(f"比赛数据集配置文件已保存: {yaml_file}")
        
    def generate_statistics(self):
        """生成数据集统计信息"""
        stats = {
            'competition_info': {
                'year': 2025,
                'categories': self.competition_categories,
                'total_categories': len(self.competition_categories)
            },
            'dataset_splits': {}
        }
        
        for split in ['train', 'val', 'test']:
            rgb_dir = self.output_dir / "images" / split
            depth_dir = self.output_dir / "depth" / split
            label_dir = self.output_dir / "labels" / split
            
            rgb_count = len(list(rgb_dir.glob("*.jpg")))
            depth_count = len(list(depth_dir.glob("*.png")))
            label_count = len(list(label_dir.glob("*.txt")))
            
            stats['dataset_splits'][split] = {
                'rgb_images': rgb_count,
                'depth_images': depth_count,
                'labels': label_count
            }
        
        # 统计各类别数量
        category_counts = {cat: 0 for cat in self.competition_categories.keys()}
        
        for split in ['train', 'val', 'test']:
            label_dir = self.output_dir / "labels" / split
            for label_file in label_dir.glob("*.txt"):
                with open(label_file, 'r') as f:
                    for line in f:
                        if line.strip():
                            class_id = int(line.split()[0])
                            # 根据class_id找到对应的类别名
                            for cat_name, cat_info in self.competition_categories.items():
                                if cat_info['id'] == class_id:
                                    category_counts[cat_name] += 1
                                    break
        
        stats['category_distribution'] = category_counts
        
        # 保存统计信息
        stats_file = self.output_dir / "competition_dataset_stats.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)
            
        print("\n=== 2025年比赛数据集统计 ===")
        print(f"总类别数: {len(self.competition_categories)}")
        for split, counts in stats['dataset_splits'].items():
            print(f"{split}: RGB={counts['rgb_images']}, Depth={counts['depth_images']}, Labels={counts['labels']}")
            
        print("\n类别分布:")
        for cat_name, count in category_counts.items():
            display_name = self.competition_categories[cat_name]['name']
            print(f"  {display_name}: {count}")
            
        return stats

def main():
    print("=== 2025年比赛数据集准备 ===")
    
    # 创建数据集准备器
    preparator = Competition2025DatasetPreparator()
    
    # 处理两个源数据集
    dataset_paths = [
        ("3D/dataset", "dataset1"),
        ("3D/dataset-20250723-coco", "dataset2")
    ]
    
    total_images = 0
    for dataset_path, dataset_name in dataset_paths:
        if Path(dataset_path).exists():
            count = preparator.process_dataset(dataset_path, dataset_name)
            total_images += count
        else:
            print(f"警告: 数据集路径不存在: {dataset_path}")
    
    # 创建配置文件和统计信息
    preparator.create_dataset_yaml()
    stats = preparator.generate_statistics()
    
    print(f"\n=== 数据集准备完成 ===")
    print(f"总图像数: {total_images}")
    print(f"输出目录: {preparator.output_dir}")
    print(f"配置文件: {preparator.output_dir}/competition_dataset.yaml")
    
    print("\n下一步:")
    print("1. 使用新数据集训练YOLOv11模型")
    print("2. 实现文字识别功能")
    print("3. 创建比赛输出格式")
    print("4. 优化模型加载时间")

if __name__ == "__main__":
    main()
