#!/usr/bin/env python3
"""
Enhanced Professional YOLOv8 Training System
For 2025 China Robot Competition - Advanced Vision Challenge 3D Recognition

This script provides a comprehensive training pipeline for YOLOv8 models
with enhanced interference detection and competition-specific optimizations.

Features:
- Enhanced data augmentation for interference scenarios
- Competition-specific class handling
- Optimized for 2D printed interference detection
- Spatial constraint training

Date: 2025-01-17
Version: 2.0
"""

import os
import sys
import time
import logging
from pathlib import Path
import torch
from ultralytics import YOLO
from typing import Dict, Tuple, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('yolov8_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class YOLOv8TrainingSystem:
    """
    Professional YOLOv8 Training System
    
    Features:
    - Automatic dataset validation
    - Optimized training configurations
    - Comprehensive model evaluation
    - Professional logging and reporting
    """
    
    def __init__(self, config_file: str = 'enhanced_competition_dataset.yaml'):
        """
        Initialize enhanced training system

        Args:
            config_file: Path to dataset configuration file
        """
        self.config_file = config_file
        self.device = self._setup_device()
        self.model = None
        self.training_results = None
        self.competition_mode = True
        self.interference_detection = True
        
    def _setup_device(self) -> str:
        """Setup and validate device configuration"""
        if torch.cuda.is_available():
            device = 'cuda'
            logger.info(f"CUDA available: {torch.cuda.get_device_name(0)}")
            logger.info(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
        else:
            device = 'cpu'
            logger.warning("CUDA not available, using CPU")
        
        return device
    
    def validate_dataset(self) -> bool:
        """
        Validate dataset structure and configuration
        
        Returns:
            True if dataset is valid, False otherwise
        """
        logger.info("Validating dataset structure...")
        
        # Check for enhanced dataset first, fallback to original
        enhanced_paths = [
            self.config_file,
            'enhanced_yolo_dataset/train/images',
            'enhanced_yolo_dataset/train/labels',
            'enhanced_yolo_dataset/val/images',
            'enhanced_yolo_dataset/val/labels'
        ]

        original_paths = [
            'custom_dataset.yaml',
            'yolo_dataset/train/images',
            'yolo_dataset/train/labels',
            'yolo_dataset/val/images',
            'yolo_dataset/val/labels'
        ]

        # Try enhanced dataset first
        required_paths = enhanced_paths
        if not all(os.path.exists(path) for path in enhanced_paths[1:]):
            logger.info("Enhanced dataset not found, using original dataset")
            required_paths = original_paths
            self.config_file = 'custom_dataset.yaml'
        
        for path in required_paths:
            if not os.path.exists(path):
                logger.error(f"Missing required file/directory: {path}")
                return False
            else:
                logger.info(f"Found: {path}")
        
        # Count dataset samples
        train_images = len(list(Path('yolo_dataset/train/images').glob('*.jpg')))
        val_images = len(list(Path('yolo_dataset/val/images').glob('*.jpg')))
        
        logger.info(f"Dataset statistics:")
        logger.info(f"  Training images: {train_images}")
        logger.info(f"  Validation images: {val_images}")
        
        if train_images < 10:
            logger.warning("Training dataset is very small, may affect training quality")
        
        return True
    
    def get_training_config(self, mode: str = 'professional') -> Dict:
        """
        Get training configuration based on mode
        
        Args:
            mode: Training mode ('quick', 'professional', 'competition')
            
        Returns:
            Dictionary containing training parameters
        """
        base_config = {
            'data': self.config_file,
            'device': self.device,
            'project': 'runs/detect',
            'exist_ok': True,
            'pretrained': True,
            'optimizer': 'AdamW',
            'verbose': True,
            'seed': 42,
            'amp': True,
            'save': True,
        }
        
        if mode == 'quick':
            # Quick training for testing
            config = {
                **base_config,
                'epochs': 50,
                'imgsz': 640,
                'batch': 8,
                'lr0': 0.01,
                'patience': 15,
                'save_period': 10,
                'cache': False,
                'workers': 4,
                'name': 'quick_yolov8_training',
            }
        elif mode == 'professional':
            # Professional training configuration
            config = {
                **base_config,
                'epochs': 100,
                'imgsz': 640,
                'batch': 16,
                'lr0': 0.01,
                'lrf': 0.01,
                'momentum': 0.937,
                'weight_decay': 0.0005,
                'warmup_epochs': 3,
                'patience': 20,
                'save_period': 10,
                'cache': True,
                'workers': 8,
                'name': 'professional_yolov8_training',
                # Data augmentation
                'hsv_h': 0.015,
                'hsv_s': 0.7,
                'hsv_v': 0.4,
                'degrees': 0.0,
                'translate': 0.1,
                'scale': 0.5,
                'fliplr': 0.5,
                'mosaic': 1.0,
                'mixup': 0.0,
            }
        elif mode == 'competition':
            # Competition-ready training
            config = {
                **base_config,
                'epochs': 200,
                'imgsz': 640,
                'batch': 32,
                'lr0': 0.01,
                'lrf': 0.01,
                'momentum': 0.937,
                'weight_decay': 0.0005,
                'warmup_epochs': 5,
                'patience': 30,
                'save_period': 5,
                'cache': True,
                'workers': 8,
                'name': 'competition_yolov8_training',
                # Enhanced data augmentation
                'hsv_h': 0.015,
                'hsv_s': 0.7,
                'hsv_v': 0.4,
                'degrees': 5.0,
                'translate': 0.1,
                'scale': 0.5,
                'shear': 2.0,
                'perspective': 0.0001,
                'fliplr': 0.5,
                'mosaic': 1.0,
                'mixup': 0.1,
                'copy_paste': 0.1,
            }
        else:
            raise ValueError(f"Unknown training mode: {mode}")
        
        return config
    
    def train_model(self, mode: str = 'professional', 
                   model_size: str = 'n') -> Tuple[bool, Optional[str]]:
        """
        Train YOLOv8 model
        
        Args:
            mode: Training mode ('quick', 'professional', 'competition')
            model_size: Model size ('n', 's', 'm', 'l', 'x')
            
        Returns:
            Tuple of (success, model_path)
        """
        logger.info(f"Starting {mode} YOLOv8 training with model size: {model_size}")
        
        # Load pre-trained model
        model_name = f'yolov8{model_size}.pt'
        logger.info(f"Loading pre-trained model: {model_name}")
        self.model = YOLO(model_name)
        
        # Get training configuration
        train_config = self.get_training_config(mode)
        logger.info(f"Training configuration: {mode} mode")
        
        start_time = time.time()
        
        try:
            # Start training
            logger.info("Starting training process...")
            self.training_results = self.model.train(**train_config)
            
            training_time = time.time() - start_time
            logger.info(f"Training completed in {training_time/60:.1f} minutes")
            logger.info(f"Model saved to: {self.training_results.save_dir}")
            
            # Get best model path
            best_model_path = self.training_results.save_dir / 'weights' / 'best.pt'
            logger.info(f"Best model: {best_model_path}")
            
            return True, str(best_model_path)
            
        except Exception as e:
            logger.error(f"Training failed: {e}")
            return False, None
    
    def validate_model(self, model_path: str) -> Dict:
        """
        Validate trained model performance
        
        Args:
            model_path: Path to trained model
            
        Returns:
            Dictionary containing validation metrics
        """
        logger.info(f"Validating model: {model_path}")
        
        if not os.path.exists(model_path):
            logger.error(f"Model file not found: {model_path}")
            return {}
        
        try:
            # Load model for validation
            model = YOLO(model_path)
            
            # Run validation
            metrics = model.val()
            
            validation_results = {
                'map50': float(metrics.box.map50),
                'map50_95': float(metrics.box.map),
                'precision': float(metrics.box.mp),
                'recall': float(metrics.box.mr),
                'model_path': model_path,
                'class_names': list(model.names.values()) if hasattr(model, 'names') else []
            }
            
            logger.info(f"Validation results:")
            logger.info(f"  mAP@0.5: {validation_results['map50']:.3f}")
            logger.info(f"  mAP@0.5:0.95: {validation_results['map50_95']:.3f}")
            logger.info(f"  Precision: {validation_results['precision']:.3f}")
            logger.info(f"  Recall: {validation_results['recall']:.3f}")
            
            return validation_results
            
        except Exception as e:
            logger.error(f"Validation failed: {e}")
            return {}
    
    def test_model_inference(self, model_path: str, num_test_images: int = 5) -> bool:
        """
        Test model inference on sample images
        
        Args:
            model_path: Path to trained model
            num_test_images: Number of test images to process
            
        Returns:
            True if test successful, False otherwise
        """
        logger.info(f"Testing model inference: {model_path}")
        
        try:
            # Load model
            model = YOLO(model_path)
            
            # Get test images
            test_dir = 'yolo_dataset/val/images'
            test_images = list(Path(test_dir).glob('*.jpg'))[:num_test_images]
            
            if not test_images:
                logger.warning("No test images found")
                return False
            
            logger.info(f"Testing on {len(test_images)} images...")
            
            for i, img_path in enumerate(test_images):
                logger.info(f"Testing image {i+1}: {img_path.name}")
                
                results = model(str(img_path), conf=0.25, verbose=False)
                result = results[0]
                
                if result.boxes is not None and len(result.boxes) > 0:
                    detections = len(result.boxes)
                    classes = [model.names[int(box.cls.item())] for box in result.boxes]
                    logger.info(f"  Detected {detections} objects: {', '.join(set(classes))}")
                else:
                    logger.info(f"  No objects detected")
            
            logger.info("Model inference test completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Inference test failed: {e}")
            return False

def main():
    """Main training execution function"""
    logger.info("Starting YOLOv8 Professional Training System")
    
    try:
        # Initialize training system
        trainer = YOLOv8TrainingSystem()
        
        # Validate dataset
        if not trainer.validate_dataset():
            logger.error("Dataset validation failed")
            return
        
        # Train model
        success, model_path = trainer.train_model(mode='professional', model_size='n')
        
        if not success:
            logger.error("Training failed")
            return
        
        # Validate trained model
        validation_results = trainer.validate_model(model_path)
        
        if validation_results:
            logger.info("Model validation completed successfully")
        
        # Test model inference
        if trainer.test_model_inference(model_path):
            logger.info("Model inference test passed")
        
        logger.info("Training system completed successfully")
        logger.info(f"Best model available at: {model_path}")
        
    except Exception as e:
        logger.error(f"Training system error: {e}")
        raise

if __name__ == '__main__':
    main()
