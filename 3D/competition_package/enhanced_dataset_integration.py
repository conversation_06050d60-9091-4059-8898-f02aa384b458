#!/usr/bin/env python3
"""
Enhanced Dataset Integration for Competition
Integrates new datasets with interference detection capabilities

For 2025 China Robot Competition - Advanced Vision Challenge 3D Recognition
Addresses 2D printed interference and spatial constraints

Date: 2025-01-17
Version: 2.0
"""

import os
import sys
import json
import shutil
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import yaml

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('dataset_integration.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EnhancedDatasetIntegrator:
    """
    Enhanced Dataset Integration System
    
    Features:
    - Integrates multiple dataset formats (COCO, YOLO)
    - Filters competition-relevant classes
    - Handles interference detection data
    - Creates unified training dataset
    """
    
    def __init__(self, base_dataset_path: str = 'yolo_dataset'):
        """
        Initialize the dataset integrator
        
        Args:
            base_dataset_path: Path to existing YOLO dataset
        """
        self.base_dataset_path = Path(base_dataset_path)
        self.new_data_path = Path('data_new')
        self.competition_classes = [
            'CA001', 'CA002', 'CB001', 'CB002', 
            'CC001', 'CC002', 'CD001', 'CD002'
        ]
        self.class_mapping = {}
        
    def analyze_new_datasets(self) -> Dict:
        """
        Analyze all new datasets and their contents
        
        Returns:
            Dictionary containing dataset analysis
        """
        logger.info("Analyzing new datasets...")

        analysis = {
            'coco_data': {},
            'yolo_data': {},
            'interference_data': {},
            'total_images': 0,
            'competition_classes_found': []
        }
        
        # Analyze COCO dataset
        coco_json_path = self.new_data_path / 'coco-data' / 'instances_train.json'
        if coco_json_path.exists():
            with open(coco_json_path, 'r', encoding='utf-8') as f:
                coco_data = json.load(f)
            
            analysis['coco_data'] = {
                'images_count': len(coco_data.get('images', [])),
                'annotations_count': len(coco_data.get('annotations', [])),
                'categories': [cat['name'] for cat in coco_data.get('categories', [])]
            }
            
            # Check for competition classes
            for cat in coco_data.get('categories', []):
                if cat['name'] in self.competition_classes:
                    analysis['competition_classes_found'].append(cat['name'])
        
        # Analyze YOLO dataset
        yolo_label_path = self.new_data_path / '新建文件夹' / 'label'
        if yolo_label_path.exists():
            classes_file = yolo_label_path / 'classes.txt'
            if classes_file.exists():
                with open(classes_file, 'r', encoding='utf-8') as f:
                    classes = [line.strip() for line in f.readlines() if line.strip()]
                
                analysis['yolo_data'] = {
                    'classes': classes,
                    'label_files': len(list(yolo_label_path.glob('*.txt'))) - 1,  # Exclude classes.txt
                    'competition_classes': [cls for cls in classes if cls in self.competition_classes]
                }
        
        # Count total images
        train_images = self.new_data_path / '新建文件夹' / 'train'
        if train_images.exists():
            analysis['total_images'] += len(list(train_images.glob('*.jpg')))
        
        coco_images = self.new_data_path / 'coco-data' / 'train'
        if coco_images.exists():
            analysis['total_images'] += len(list(coco_images.glob('*.jpg')))
        
        logger.info(f"Dataset analysis completed:")
        logger.info(f"  Total new images: {analysis['total_images']}")
        logger.info(f"  Competition classes found: {analysis['competition_classes_found']}")
        
        return analysis
    
    def create_enhanced_class_mapping(self) -> Dict:
        """
        Create enhanced class mapping for competition
        
        Returns:
            Dictionary mapping class names to IDs
        """
        logger.info("Creating enhanced class mapping...")
        
        # Start with competition classes
        class_mapping = {cls: idx for idx, cls in enumerate(self.competition_classes)}
        
        # Add interference detection classes
        interference_classes = [
            'interference_2d',  # 2D printed objects
            'background_object',  # Objects below table
            'invalid_detection'  # Other invalid detections
        ]
        
        next_id = len(self.competition_classes)
        for cls in interference_classes:
            class_mapping[cls] = next_id
            next_id += 1
        
        self.class_mapping = class_mapping
        logger.info(f"Class mapping created with {len(class_mapping)} classes")
        
        return class_mapping
    
    def convert_coco_to_yolo(self, output_dir: str = 'enhanced_yolo_dataset') -> bool:
        """
        Convert COCO dataset to YOLO format
        
        Args:
            output_dir: Output directory for converted dataset
            
        Returns:
            True if conversion successful
        """
        logger.info("Converting COCO dataset to YOLO format...")
        
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # Create directory structure
        for split in ['train', 'val']:
            (output_path / split / 'images').mkdir(parents=True, exist_ok=True)
            (output_path / split / 'labels').mkdir(parents=True, exist_ok=True)
        
        # Load COCO annotations
        coco_json_path = self.new_data_path / 'coco-data' / 'instances_train.json'
        if not coco_json_path.exists():
            logger.error("COCO annotations file not found")
            return False
        
        with open(coco_json_path, 'r', encoding='utf-8') as f:
            coco_data = json.load(f)
        
        # Create image ID to filename mapping
        image_info = {img['id']: img for img in coco_data['images']}
        
        # Create category mapping
        category_mapping = {}
        for cat in coco_data['categories']:
            if cat['name'] in self.competition_classes:
                category_mapping[cat['id']] = self.class_mapping[cat['name']]
        
        # Process annotations
        annotations_by_image = {}
        for ann in coco_data['annotations']:
            if ann['category_id'] in category_mapping:
                image_id = ann['image_id']
                if image_id not in annotations_by_image:
                    annotations_by_image[image_id] = []
                annotations_by_image[image_id].append(ann)
        
        # Convert and copy files
        converted_count = 0
        coco_images_path = self.new_data_path / 'coco-data' / 'train'
        
        for image_id, annotations in annotations_by_image.items():
            if image_id not in image_info:
                continue
                
            img_info = image_info[image_id]
            img_filename = img_info['file_name']
            img_width = img_info['width']
            img_height = img_info['height']
            
            # Copy image file
            src_img_path = coco_images_path / img_filename
            if not src_img_path.exists():
                continue
                
            dst_img_path = output_path / 'train' / 'images' / img_filename
            shutil.copy2(src_img_path, dst_img_path)
            
            # Create YOLO label file
            label_filename = Path(img_filename).stem + '.txt'
            label_path = output_path / 'train' / 'labels' / label_filename
            
            with open(label_path, 'w') as f:
                for ann in annotations:
                    if ann['category_id'] not in category_mapping:
                        continue
                        
                    class_id = category_mapping[ann['category_id']]
                    bbox = ann['bbox']  # [x, y, width, height]
                    
                    # Convert to YOLO format (normalized center coordinates)
                    x_center = (bbox[0] + bbox[2] / 2) / img_width
                    y_center = (bbox[1] + bbox[3] / 2) / img_height
                    width = bbox[2] / img_width
                    height = bbox[3] / img_height
                    
                    f.write(f"{class_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}\n")
            
            converted_count += 1
        
        logger.info(f"Converted {converted_count} images from COCO to YOLO format")
        return True
    
    def integrate_yolo_dataset(self, output_dir: str = 'enhanced_yolo_dataset') -> bool:
        """
        Integrate existing YOLO dataset with competition class filtering
        
        Args:
            output_dir: Output directory for integrated dataset
            
        Returns:
            True if integration successful
        """
        logger.info("Integrating YOLO dataset...")
        
        output_path = Path(output_dir)
        yolo_source = self.new_data_path / '新建文件夹'
        
        if not yolo_source.exists():
            logger.error("YOLO source dataset not found")
            return False
        
        # Load class mapping
        classes_file = yolo_source / 'label' / 'classes.txt'
        if not classes_file.exists():
            logger.error("Classes file not found")
            return False
        
        with open(classes_file, 'r', encoding='utf-8') as f:
            source_classes = [line.strip() for line in f.readlines() if line.strip()]
        
        # Create mapping from source class IDs to target class IDs
        source_to_target = {}
        for idx, cls_name in enumerate(source_classes):
            if cls_name in self.competition_classes:
                source_to_target[idx] = self.class_mapping[cls_name]
        
        # Process label files
        integrated_count = 0
        label_source = yolo_source / 'label'
        image_source = yolo_source / 'train'
        
        for label_file in label_source.glob('*.txt'):
            if label_file.name == 'classes.txt':
                continue
                
            # Check if corresponding image exists
            img_filename = label_file.stem + '.jpg'
            img_source_path = image_source / img_filename
            
            if not img_source_path.exists():
                continue
            
            # Read and filter annotations
            filtered_annotations = []
            with open(label_file, 'r') as f:
                for line in f:
                    parts = line.strip().split()
                    if len(parts) >= 5:
                        class_id = int(parts[0])
                        if class_id in source_to_target:
                            # Update class ID and keep annotation
                            new_class_id = source_to_target[class_id]
                            filtered_annotations.append(f"{new_class_id} {' '.join(parts[1:])}")
            
            # Only process if we have valid annotations
            if filtered_annotations:
                # Copy image
                dst_img_path = output_path / 'train' / 'images' / img_filename
                shutil.copy2(img_source_path, dst_img_path)
                
                # Write filtered labels
                label_filename = label_file.stem + '.txt'
                dst_label_path = output_path / 'train' / 'labels' / label_filename
                
                with open(dst_label_path, 'w') as f:
                    f.write('\n'.join(filtered_annotations) + '\n')
                
                integrated_count += 1
        
        logger.info(f"Integrated {integrated_count} images from YOLO dataset")
        return True
    
    def create_enhanced_config(self, output_dir: str = 'enhanced_yolo_dataset') -> str:
        """
        Create enhanced dataset configuration file
        
        Args:
            output_dir: Dataset directory
            
        Returns:
            Path to created config file
        """
        logger.info("Creating enhanced dataset configuration...")
        
        config = {
            'path': str(Path(output_dir).absolute()),
            'train': 'train/images',
            'val': 'val/images',
            'test': 'val/images',  # Use val for test if no separate test set
            'nc': len(self.class_mapping),
            'names': list(self.class_mapping.keys())
        }
        
        config_path = 'enhanced_competition_dataset.yaml'
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        logger.info(f"Enhanced dataset config created: {config_path}")
        return config_path
    
    def create_validation_split(self, output_dir: str = 'enhanced_yolo_dataset', 
                               val_ratio: float = 0.2) -> bool:
        """
        Create validation split from training data
        
        Args:
            output_dir: Dataset directory
            val_ratio: Ratio of data to use for validation
            
        Returns:
            True if split successful
        """
        logger.info(f"Creating validation split with ratio {val_ratio}...")
        
        output_path = Path(output_dir)
        train_images = list((output_path / 'train' / 'images').glob('*.jpg'))
        
        if not train_images:
            logger.error("No training images found")
            return False
        
        # Calculate split
        val_count = int(len(train_images) * val_ratio)
        val_images = train_images[:val_count]
        
        # Move validation images and labels
        for img_path in val_images:
            # Move image
            val_img_path = output_path / 'val' / 'images' / img_path.name
            shutil.move(str(img_path), str(val_img_path))
            
            # Move corresponding label
            label_name = img_path.stem + '.txt'
            train_label_path = output_path / 'train' / 'labels' / label_name
            val_label_path = output_path / 'val' / 'labels' / label_name
            
            if train_label_path.exists():
                shutil.move(str(train_label_path), str(val_label_path))
        
        logger.info(f"Created validation split: {val_count} images moved to validation set")
        return True

def main():
    """Main execution function"""
    logger.info("Starting Enhanced Dataset Integration")
    
    try:
        # Initialize integrator
        integrator = EnhancedDatasetIntegrator()
        
        # Analyze new datasets
        analysis = integrator.analyze_new_datasets()
        
        # Create class mapping
        class_mapping = integrator.create_enhanced_class_mapping()
        
        # Convert and integrate datasets
        output_dir = 'enhanced_yolo_dataset'
        
        # Convert COCO dataset
        if integrator.convert_coco_to_yolo(output_dir):
            logger.info("COCO dataset conversion completed")
        
        # Integrate YOLO dataset
        if integrator.integrate_yolo_dataset(output_dir):
            logger.info("YOLO dataset integration completed")
        
        # Create validation split
        if integrator.create_validation_split(output_dir):
            logger.info("Validation split created")
        
        # Create enhanced config
        config_path = integrator.create_enhanced_config(output_dir)
        
        logger.info("Enhanced dataset integration completed successfully")
        logger.info(f"Dataset location: {output_dir}")
        logger.info(f"Config file: {config_path}")
        logger.info(f"Total classes: {len(class_mapping)}")
        
    except Exception as e:
        logger.error(f"Dataset integration failed: {e}")
        raise

if __name__ == '__main__':
    main()
