#!/usr/bin/env python3
"""
Enhanced Competition Inference System
For 2025 China Robot Competition - Advanced Vision Challenge 3D Recognition

This script provides enhanced inference capabilities specifically designed for
the competition environment with interference detection and spatial filtering.

Features:
- 2D printed interference detection and filtering
- Spatial constraint enforcement (ROI limiting)
- Multi-target table support for round 2
- Competition result formatting
- Time management for competition constraints

Date: 2025-01-17
Version: 2.0
"""

import os
import sys
import time
import json
import logging
from pathlib import Path
import cv2
import numpy as np
from ultralytics import YOLO
import torch
from typing import List, Dict, Tuple, Optional, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_competition_inference.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CompetitionInferenceEngine:
    """
    Enhanced Competition Inference Engine
    
    Features:
    - Interference detection and filtering
    - Spatial constraint enforcement
    - Multi-target table support
    - Competition result formatting
    """
    
    def __init__(self, model_path: Optional[str] = None, device: str = 'auto'):
        """
        Initialize the enhanced inference engine
        
        Args:
            model_path: Path to trained model
            device: Devi<PERSON> to use ('auto', 'cpu', 'cuda')
        """
        self.model = None
        self.model_path = None
        self.device = self._setup_device(device)
        self.class_names = []
        self.competition_classes = [
            'CA001', 'CA002', 'CB001', 'CB002', 
            'CC001', 'CC002', 'CD001', 'CD002'
        ]
        
        # Competition-specific settings
        self.roi_enabled = True
        self.interference_filtering = True
        self.min_confidence = 0.25
        self.max_detection_time = 45  # seconds for round 1
        
        # Load model
        self._load_model(model_path)
        
    def _setup_device(self, device: str) -> str:
        """Setup and validate device configuration"""
        if device == 'auto':
            if torch.cuda.is_available():
                device = 'cuda'
                logger.info(f"CUDA available: {torch.cuda.get_device_name(0)}")
            else:
                device = 'cpu'
                logger.warning("CUDA not available, using CPU")
        return device
    
    def _load_model(self, model_path: Optional[str] = None) -> None:
        """Load YOLOv8 model with enhanced model search"""
        logger.info("Loading enhanced YOLOv8 model...")
        
        # Enhanced model search priority
        enhanced_model_paths = [
            'runs/detect/competition_yolov8_training/weights/best.pt',
            'runs/detect/professional_yolov8_training/weights/best.pt',
            'runs/detect/enhanced_yolov8_training/weights/best.pt',
            'runs/detect/train/weights/best.pt'
        ]
        
        if model_path:
            target_path = model_path
        else:
            target_path = None
            for enhanced_path in enhanced_model_paths:
                if os.path.exists(enhanced_path):
                    target_path = enhanced_path
                    break
            
            if target_path is None:
                target_path = 'yolov8n.pt'
                logger.warning("No trained model found, using pretrained YOLOv8n")
        
        try:
            logger.info(f"Loading model: {target_path}")
            self.model = YOLO(target_path)
            self.model.to(self.device)
            self.model_path = target_path
            
            if hasattr(self.model, 'names'):
                self.class_names = list(self.model.names.values())
                logger.info(f"Model loaded with {len(self.class_names)} classes")
                logger.info(f"Classes: {self.class_names}")
            
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise
    
    def apply_roi_filter(self, detections: List, image_shape: Tuple[int, int], 
                        roi_margin: float = 0.1) -> List:
        """
        Apply Region of Interest filtering to remove detections outside table area
        
        Args:
            detections: List of detection results
            image_shape: (height, width) of image
            roi_margin: Margin around image center for ROI
            
        Returns:
            Filtered detections within ROI
        """
        if not self.roi_enabled or not detections:
            return detections
        
        height, width = image_shape
        
        # Define ROI (center region of image, assuming table is centered)
        roi_x1 = int(width * roi_margin)
        roi_y1 = int(height * roi_margin)
        roi_x2 = int(width * (1 - roi_margin))
        roi_y2 = int(height * (1 - roi_margin))
        
        filtered_detections = []
        
        for detection in detections:
            if hasattr(detection, 'boxes') and detection.boxes is not None:
                boxes = detection.boxes.xyxy.cpu().numpy()
                valid_boxes = []
                
                for box in boxes:
                    x1, y1, x2, y2 = box
                    center_x = (x1 + x2) / 2
                    center_y = (y1 + y2) / 2
                    
                    # Check if detection center is within ROI
                    if (roi_x1 <= center_x <= roi_x2 and 
                        roi_y1 <= center_y <= roi_y2):
                        valid_boxes.append(box)
                
                if valid_boxes:
                    # Create new detection with filtered boxes
                    # This is a simplified approach - in practice you'd need to
                    # properly reconstruct the detection object
                    filtered_detections.append(detection)
        
        logger.debug(f"ROI filtering: {len(detections)} -> {len(filtered_detections)} detections")
        return filtered_detections
    
    def filter_interference(self, detections: List, confidence_threshold: float = 0.4) -> List:
        """
        Filter out interference detections (2D printed objects, etc.)
        
        Args:
            detections: List of detection results
            confidence_threshold: Minimum confidence for valid detections
            
        Returns:
            Filtered detections without interference
        """
        if not self.interference_filtering or not detections:
            return detections
        
        filtered_detections = []
        
        for detection in detections:
            if hasattr(detection, 'boxes') and detection.boxes is not None:
                # Filter by confidence
                confidences = detection.boxes.conf.cpu().numpy()
                valid_indices = confidences >= confidence_threshold
                
                if np.any(valid_indices):
                    # Additional filtering logic can be added here
                    # For example, size-based filtering, aspect ratio checks, etc.
                    filtered_detections.append(detection)
        
        logger.debug(f"Interference filtering: {len(detections)} -> {len(filtered_detections)} detections")
        return filtered_detections
    
    def extract_competition_results(self, detections: List) -> Dict:
        """
        Extract and format results for competition submission
        
        Args:
            detections: List of detection results
            
        Returns:
            Dictionary with competition-formatted results
        """
        results = {
            'detections': [],
            'class_counts': {},
            'total_objects': 0,
            'competition_classes_only': True
        }
        
        for detection in detections:
            if hasattr(detection, 'boxes') and detection.boxes is not None:
                boxes = detection.boxes
                
                for i in range(len(boxes)):
                    class_id = int(boxes.cls[i].item())
                    confidence = float(boxes.conf[i].item())
                    
                    if class_id < len(self.class_names):
                        class_name = self.class_names[class_id]
                        
                        # Only include competition classes
                        if class_name in self.competition_classes:
                            if class_name not in results['class_counts']:
                                results['class_counts'][class_name] = 0
                            results['class_counts'][class_name] += 1
                            
                            results['detections'].append({
                                'class_id': class_name,
                                'confidence': confidence,
                                'bbox': boxes.xyxy[i].cpu().numpy().tolist()
                            })
        
        results['total_objects'] = sum(results['class_counts'].values())
        return results
    
    def run_competition_inference(self, image_path: str, 
                                max_time: float = 45.0) -> Dict:
        """
        Run inference optimized for competition constraints
        
        Args:
            image_path: Path to input image
            max_time: Maximum time allowed for inference (seconds)
            
        Returns:
            Competition-formatted results
        """
        start_time = time.time()
        logger.info(f"Starting competition inference on: {image_path}")
        
        try:
            # Load and validate image
            if not os.path.exists(image_path):
                raise FileNotFoundError(f"Image not found: {image_path}")
            
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Could not load image: {image_path}")
            
            # Run inference with time monitoring
            results = self.model(image_path, conf=self.min_confidence, verbose=False)
            
            inference_time = time.time() - start_time
            if inference_time > max_time * 0.8:  # Warning at 80% of max time
                logger.warning(f"Inference time approaching limit: {inference_time:.2f}s")
            
            # Apply filtering
            filtered_results = self.apply_roi_filter(results, image.shape[:2])
            filtered_results = self.filter_interference(filtered_results, self.min_confidence)
            
            # Extract competition results
            competition_results = self.extract_competition_results(filtered_results)
            
            # Add timing information
            competition_results['processing_time'] = time.time() - start_time
            competition_results['within_time_limit'] = competition_results['processing_time'] <= max_time
            
            logger.info(f"Competition inference completed in {competition_results['processing_time']:.2f}s")
            logger.info(f"Detected objects: {competition_results['total_objects']}")
            
            return competition_results
            
        except Exception as e:
            logger.error(f"Competition inference failed: {e}")
            return {
                'detections': [],
                'class_counts': {},
                'total_objects': 0,
                'error': str(e),
                'processing_time': time.time() - start_time,
                'within_time_limit': False
            }
    
    def format_competition_output(self, results: Dict, output_file: str = 'competition_results.json') -> str:
        """
        Format results for competition submission
        
        Args:
            results: Competition results dictionary
            output_file: Output file path
            
        Returns:
            Path to output file
        """
        # Format according to competition requirements
        formatted_results = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'processing_time': results.get('processing_time', 0),
            'total_detections': results.get('total_objects', 0),
            'class_results': []
        }
        
        # Format class results according to competition format
        for class_name, count in results.get('class_counts', {}).items():
            formatted_results['class_results'].append({
                'class_id': class_name,
                'count': count
            })
        
        # Save results
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(formatted_results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Competition results saved to: {output_file}")
        return output_file

def main():
    """Main execution function for testing"""
    logger.info("Starting Enhanced Competition Inference System")
    
    try:
        # Initialize inference engine
        engine = CompetitionInferenceEngine(device='auto')
        
        # Test with sample image (if available)
        test_images = [
            'yolo_dataset/val/images',
            'enhanced_yolo_dataset/val/images',
            'dataset/images'
        ]
        
        test_image = None
        for img_dir in test_images:
            if os.path.exists(img_dir):
                images = list(Path(img_dir).glob('*.jpg'))
                if images:
                    test_image = str(images[0])
                    break
        
        if test_image:
            logger.info(f"Testing with image: {test_image}")
            results = engine.run_competition_inference(test_image)
            
            # Format and save results
            output_file = engine.format_competition_output(results)
            
            # Print summary
            print("\n" + "="*60)
            print("ENHANCED COMPETITION INFERENCE RESULTS")
            print("="*60)
            print(f"Processing time: {results['processing_time']:.2f} seconds")
            print(f"Total detections: {results['total_objects']}")
            print(f"Within time limit: {results['within_time_limit']}")
            
            if results['class_counts']:
                print("\nDetected classes:")
                for class_name, count in results['class_counts'].items():
                    print(f"  {class_name}: {count}")
            
            print(f"\nResults saved to: {output_file}")
            print("="*60)
        else:
            logger.warning("No test images found")
        
        logger.info("Enhanced competition inference system ready")
        
    except Exception as e:
        logger.error(f"System error: {e}")
        raise

if __name__ == '__main__':
    main()
