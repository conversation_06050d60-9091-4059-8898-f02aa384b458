#!/usr/bin/env python3
"""
Competition-Ready Inference System
For 2025 China Robot Competition - Advanced Vision Challenge 3D Recognition

This script provides the final competition-ready inference system with all
optimizations for the actual competition environment.

Features:
- Real-time interference detection and filtering
- Spatial constraint enforcement (ROI limiting)
- Competition result formatting
- Time management for competition constraints
- Multi-target table support for round 2

Date: 2025-01-17
Version: 3.0 - Competition Ready
"""

import os
import sys
import time
import json
import logging
from pathlib import Path
import cv2
import numpy as np
from ultralytics import YOLO
import torch
from typing import List, Dict, Tuple, Optional, Any
from model_config import get_competition_model_path, verify_competition_setup

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('competition_inference.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CompetitionReadyInference:
    """
    Competition-Ready Inference System
    
    Optimized for the actual competition environment with all
    interference detection and spatial filtering capabilities.
    """
    
    def __init__(self, model_path: Optional[str] = None):
        """
        Initialize the competition-ready inference system
        
        Args:
            model_path: Path to trained model (auto-detect if None)
        """
        self.model = None
        self.model_path = None
        self.device = self._setup_device()
        
        # Competition classes (exactly as specified in competition rules)
        self.competition_classes = [
            'CA001', 'CA002', 'CB001', 'CB002', 
            'CC001', 'CC002', 'CD001', 'CD002'
        ]
        
        # Competition settings
        self.round1_max_time = 50  # seconds
        self.round2_max_time = 150  # seconds
        self.min_confidence = 0.25
        self.roi_margin = 0.1  # 10% margin from edges
        
        # Load model
        self._load_competition_model(model_path)
        
    def _setup_device(self) -> str:
        """Setup optimal device for competition"""
        if torch.cuda.is_available():
            device = 'cuda'
            logger.info(f"Using GPU: {torch.cuda.get_device_name(0)}")
        else:
            device = 'cpu'
            logger.warning("Using CPU - may be slower")
        return device
    
    def _load_competition_model(self, model_path: Optional[str] = None) -> None:
        """Load the best available trained model"""
        logger.info("Loading competition model...")
        
        if model_path and os.path.exists(model_path):
            target_path = model_path
        else:
            target_path = get_competition_model_path()

            if not os.path.exists(target_path):
                raise FileNotFoundError("No trained model found! Please train a model first.")
        
        try:
            self.model = YOLO(target_path)
            self.model.to(self.device)
            self.model_path = target_path
            logger.info(f"Loaded model: {target_path}")
            logger.info(f"Model classes: {list(self.model.names.values())}")
            
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise
    
    def apply_competition_filtering(self, results, image_shape: tuple) -> List:
        """
        Apply all competition-specific filtering
        
        Args:
            results: YOLO detection results
            image_shape: (height, width) of input image
            
        Returns:
            Filtered results with only valid competition detections
        """
        if not results or not results[0].boxes:
            return []
        
        result = results[0]
        boxes = result.boxes
        height, width = image_shape[:2]
        
        # Define ROI (exclude table edges and floor areas)
        roi_x1 = int(width * self.roi_margin)
        roi_y1 = int(height * self.roi_margin)
        roi_x2 = int(width * (1 - self.roi_margin))
        roi_y2 = int(height * (1 - self.roi_margin))
        
        valid_detections = []
        
        for i in range(len(boxes)):
            # Check confidence threshold
            confidence = float(boxes.conf[i])
            if confidence < self.min_confidence:
                continue
            
            # Check class validity
            class_id = int(boxes.cls[i])
            if class_id >= len(self.model.names):
                continue
            
            class_name = self.model.names[class_id]
            if class_name not in self.competition_classes:
                continue
            
            # Check spatial constraints (ROI)
            x1, y1, x2, y2 = boxes.xyxy[i].cpu().numpy()
            center_x = (x1 + x2) / 2
            center_y = (y1 + y2) / 2
            
            if not (roi_x1 <= center_x <= roi_x2 and roi_y1 <= center_y <= roi_y2):
                continue
            
            # Check size constraints (filter very small detections - likely 2D interference)
            box_width = x2 - x1
            box_height = y2 - y1
            box_area = box_width * box_height
            
            # Minimum area threshold (0.1% of image area)
            min_area = width * height * 0.001
            if box_area < min_area:
                continue
            
            # Aspect ratio check (filter very thin objects - likely printed edges)
            aspect_ratio = max(box_width, box_height) / min(box_width, box_height)
            if aspect_ratio > 8:  # Very elongated objects
                continue
            
            # Valid detection
            valid_detections.append({
                'class_id': class_name,
                'confidence': confidence,
                'bbox': [x1, y1, x2, y2]
            })
        
        return valid_detections
    
    def run_competition_round(self, image_path: str, round_num: int = 1) -> Dict:
        """
        Run inference for competition round
        
        Args:
            image_path: Path to input image
            round_num: Competition round (1 or 2)
            
        Returns:
            Competition-formatted results
        """
        max_time = self.round1_max_time if round_num == 1 else self.round2_max_time
        start_time = time.time()
        
        logger.info(f"Starting Round {round_num} inference: {image_path}")
        logger.info(f"Time limit: {max_time} seconds")
        
        try:
            # Load and validate image
            if not os.path.exists(image_path):
                raise FileNotFoundError(f"Image not found: {image_path}")
            
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Could not load image: {image_path}")
            
            # Run inference
            results = self.model(image_path, conf=self.min_confidence, verbose=False)
            
            # Apply competition filtering
            valid_detections = self.apply_competition_filtering(results, image.shape)
            
            # Count detections by class
            class_counts = {}
            for detection in valid_detections:
                class_id = detection['class_id']
                if class_id not in class_counts:
                    class_counts[class_id] = 0
                class_counts[class_id] += 1
            
            # Calculate timing
            processing_time = time.time() - start_time
            within_time_limit = processing_time <= max_time
            
            # Format results for competition
            competition_results = {
                'round': round_num,
                'processing_time': processing_time,
                'within_time_limit': within_time_limit,
                'total_detections': len(valid_detections),
                'class_counts': class_counts,
                'detections': valid_detections,
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            
            logger.info(f"Round {round_num} completed in {processing_time:.2f}s")
            logger.info(f"Total valid detections: {len(valid_detections)}")
            logger.info(f"Within time limit: {within_time_limit}")
            
            if class_counts:
                logger.info("Detected classes:")
                for class_name, count in class_counts.items():
                    logger.info(f"  {class_name}: {count}")
            
            return competition_results
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Round {round_num} inference failed: {e}")
            
            return {
                'round': round_num,
                'processing_time': processing_time,
                'within_time_limit': processing_time <= max_time,
                'total_detections': 0,
                'class_counts': {},
                'detections': [],
                'error': str(e),
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            }
    
    def format_competition_output(self, results: Dict, output_file: str) -> str:
        """
        Format results according to competition requirements
        
        Args:
            results: Competition results
            output_file: Output file path
            
        Returns:
            Path to formatted output file
        """
        # Format for competition submission
        formatted_output = {
            'team_info': {
                'round': results.get('round', 1),
                'timestamp': results.get('timestamp', time.strftime('%Y-%m-%d %H:%M:%S')),
                'processing_time': results.get('processing_time', 0)
            },
            'detection_results': []
        }
        
        # Add detection results in competition format
        for class_name, count in results.get('class_counts', {}).items():
            formatted_output['detection_results'].append({
                'class_id': class_name,
                'count': count
            })
        
        # Save formatted results
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(formatted_output, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Competition results saved to: {output_file}")
        return output_file

def main():
    """Main function for testing the competition system"""
    logger.info("Initializing Competition-Ready Inference System")
    
    try:
        # Initialize system
        competition_system = CompetitionReadyInference()
        
        # Test with available images
        test_dirs = [
            'enhanced_yolo_dataset/val/images',
            'yolo_dataset/val/images',
            'dataset/images'
        ]
        
        test_image = None
        for test_dir in test_dirs:
            if os.path.exists(test_dir):
                images = list(Path(test_dir).glob('*.jpg'))
                if images:
                    test_image = str(images[0])
                    break
        
        if test_image:
            logger.info(f"Testing with image: {test_image}")
            
            # Test Round 1
            results = competition_system.run_competition_round(test_image, round_num=1)
            
            # Format and save results
            output_file = competition_system.format_competition_output(
                results, 'competition_round1_results.json'
            )
            
            # Print summary
            print("\n" + "="*80)
            print("COMPETITION-READY INFERENCE SYSTEM TEST")
            print("="*80)
            print(f"Round: {results['round']}")
            print(f"Processing time: {results['processing_time']:.2f} seconds")
            print(f"Within time limit: {results['within_time_limit']}")
            print(f"Total detections: {results['total_detections']}")
            
            if results['class_counts']:
                print("\nDetected classes:")
                for class_name, count in results['class_counts'].items():
                    print(f"  {class_name}: {count}")
            
            print(f"\nResults saved to: {output_file}")
            print("="*80)
            
        else:
            logger.warning("No test images found")
        
        logger.info("Competition system ready for deployment")
        
    except Exception as e:
        logger.error(f"System initialization failed: {e}")
        raise

if __name__ == '__main__':
    main()
