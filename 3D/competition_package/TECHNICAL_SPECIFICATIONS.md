# Technical Specifications

## System Architecture

### Core Components

#### 1. Dataset Integration System
- **File**: `enhanced_dataset_integration.py`
- **Purpose**: Automated integration of multiple dataset formats
- **Features**:
  - COCO to YOLO format conversion
  - Competition class filtering
  - Validation set creation
  - Enhanced class mapping

#### 2. Enhanced Training System
- **File**: `yolov8_professional_training.py`
- **Purpose**: Optimized model training for competition environment
- **Features**:
  - Automatic enhanced dataset detection
  - Competition-specific data augmentation
  - Professional logging and validation
  - Model performance evaluation

#### 3. Enhanced Inference System
- **File**: `yolov8_professional_inference.py`
- **Purpose**: Advanced inference with interference filtering
- **Features**:
  - ROI-based spatial filtering
  - Interference detection and suppression
  - Competition class prioritization
  - Performance optimization

#### 4. Competition-Ready System
- **File**: `competition_ready_inference.py`
- **Purpose**: Final competition deployment system
- **Features**:
  - Time management for competition rounds
  - Competition-compliant output formatting
  - Real-time performance monitoring
  - Multi-round support

## Algorithm Details

### Interference Detection Algorithm

#### 2D Printed Object Filtering
```
1. Size-based filtering:
   - Minimum area threshold: 0.1% of image area
   - Filters out small printed objects

2. Shape-based filtering:
   - Maximum aspect ratio: 8:1
   - Eliminates thin printed edges

3. Confidence optimization:
   - Minimum confidence: 0.25
   - Reduces false positives
```

#### Spatial Constraint Implementation
```
1. ROI definition:
   - Center region: 80% of image area
   - Edge margin: 10% boundary exclusion

2. Position validation:
   - Detection center must be within ROI
   - Filters ground-level objects

3. Height inference:
   - Based on vertical position in image
   - Table-level object prioritization
```

### Performance Optimization

#### Processing Pipeline
```
1. Image loading and validation
2. YOLO inference execution
3. Competition filtering application
4. Result formatting and validation
5. Output generation
```

#### Time Management
```
Round 1: 50-second limit
- Target processing: <5 seconds
- Actual performance: ~3.85 seconds

Round 2: 150-second limit
- Multi-table processing support
- Camera rotation coordination
```

## Model Specifications

### Training Configuration
- **Base Model**: YOLOv8n (nano version for speed)
- **Input Resolution**: 640x640 pixels
- **Batch Size**: 16 (professional mode)
- **Epochs**: 100
- **Optimizer**: AdamW
- **Learning Rate**: 0.01 (initial)

### Performance Metrics
- **mAP@0.5**: 0.802
- **mAP@0.5:0.95**: 0.545
- **Precision**: 0.955
- **Recall**: 0.944
- **Processing Speed**: 3.85 seconds per image

### Class Configuration
```yaml
names:
- CA001  # Daily necessities - Spoon
- CA002  # Daily necessities - Chopsticks  
- CB001  # Food products - Sachima
- CB002  # Food products - Canned preserved food
- CC001  # Beverages - Canned drinks
- CC002  # Beverages - Bottled drinks
- CD001  # Fruits - Apple
- CD002  # Fruits - Orange
nc: 8
```

## Data Processing

### Dataset Statistics
- **Training Images**: 391
- **Validation Images**: 97
- **Total Classes**: 8 (competition) + 3 (interference detection)
- **Source Datasets**: 3 integrated datasets

### Preprocessing Pipeline
```
1. Format conversion (COCO → YOLO)
2. Class filtering and mapping
3. Validation split creation
4. Quality validation
5. Configuration generation
```

### Augmentation Strategy
```
- HSV adjustment: h=0.015, s=0.7, v=0.4
- Geometric transforms: scale=0.5, translate=0.1
- Flip probability: 0.5
- Mosaic augmentation: 1.0
- Mixup probability: 0.0 (professional mode)
```

## Competition Compliance

### Output Format Specification
```json
{
  "team_info": {
    "round": <round_number>,
    "timestamp": "<YYYY-MM-DD HH:MM:SS>",
    "processing_time": <seconds>
  },
  "detection_results": [
    {
      "class_id": "<class_name>",
      "count": <detection_count>
    }
  ]
}
```

### Time Constraints
- **Round 1**: Maximum 50 seconds
- **Round 2**: Maximum 150 seconds
- **Actual Performance**: 3-5 seconds typical

### Accuracy Requirements
- **Minimum Confidence**: 0.25
- **Competition Classes Only**: CA001-CD002
- **Interference Suppression**: Active
- **Spatial Filtering**: Enabled

## Hardware Optimization

### GPU Utilization
- **CUDA Support**: Required for optimal performance
- **Memory Usage**: ~2.31GB GPU memory
- **Batch Processing**: Optimized for single image inference

### CPU Fallback
- **Compatibility**: Full CPU support available
- **Performance Impact**: 3-5x slower processing
- **Memory Requirements**: 4GB RAM minimum

## Error Handling

### Robustness Features
```
1. Image validation:
   - Format verification
   - Corruption detection
   - Size validation

2. Model loading:
   - Automatic model detection
   - Fallback model support
   - Error recovery

3. Processing pipeline:
   - Exception handling
   - Graceful degradation
   - Performance monitoring
```

### Logging System
- **Level**: INFO (configurable)
- **Output**: File and console
- **Format**: Timestamp, level, message
- **Performance Tracking**: Processing time monitoring

## Deployment Considerations

### File Structure
```
competition_package/
├── enhanced_dataset_integration.py
├── yolov8_professional_training.py
├── yolov8_professional_inference.py
├── competition_ready_inference.py
├── enhanced_competition_inference.py
├── enhanced_competition_dataset.yaml
├── requirements.txt
├── README.md
├── TECHNICAL_SPECIFICATIONS.md
└── DEPLOYMENT_GUIDE.md
```

### Dependencies
```
torch>=2.0.0
ultralytics>=8.0.0
opencv-python>=4.0.0
numpy>=1.21.0
pyyaml>=6.0
```

### Model Requirements
- **Trained Model**: Required for inference
- **Model Path**: Auto-detection from training results
- **Fallback**: Pre-trained YOLOv8n if no custom model available

This technical specification provides comprehensive details for system implementation, optimization, and deployment in the competition environment.
