# Deployment Guide

## Pre-Competition Setup

### Environment Preparation

#### 1. System Requirements Verification
```bash
# Check Python version (3.8+ required)
python --version

# Check CUDA availability (recommended)
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"

# Check available GPU memory
nvidia-smi
```

#### 2. Dependency Installation
```bash
# Install required packages
pip install -r requirements.txt

# Verify installation
python -c "from ultralytics import YOLO; print('YOLOv8 ready')"
python -c "import cv2; print(f'OpenCV version: {cv2.__version__}')"
```

#### 3. Model Preparation
```bash
# If using pre-trained model (for testing)
python -c "from ultralytics import YOLO; YOLO('yolov8n.pt')"

# If using custom trained model, ensure model file exists:
# runs/detect/professional_yolov8_training/weights/best.pt
```

### Competition Day Setup

#### 1. System Validation
```bash
# Test the competition system
python competition_ready_inference.py

# Verify output format
cat competition_round1_results.json
```

#### 2. Performance Verification
```bash
# Check processing speed with test image
time python competition_ready_inference.py

# Expected output: Processing time < 5 seconds
```

## Competition Execution

### Round 1 Deployment

#### 1. Single Target Table Setup
```python
from competition_ready_inference import CompetitionReadyInference

# Initialize system
system = CompetitionReadyInference()

# Run Round 1 inference
results = system.run_competition_round(
    image_path="path/to/competition/image.jpg",
    round_num=1
)

# Format results for submission
output_file = system.format_competition_output(
    results, 
    "round1_submission.json"
)
```

#### 2. Expected Performance
- **Time Limit**: 50 seconds
- **Expected Processing**: 3-5 seconds
- **Object Range**: 7-15 objects
- **Classes**: CA001-CD002

### Round 2 Deployment

#### 1. Multi-Target Table Setup
```python
# Extended system for Round 2
system = CompetitionReadyInference()

# Process multiple tables (requires camera rotation)
for table_id in range(3):
    # Camera rotation command (implementation specific)
    # rotate_camera_to_table(table_id)
    
    results = system.run_competition_round(
        image_path=f"table_{table_id}_image.jpg",
        round_num=2
    )
    
    # Aggregate results for final submission
```

#### 2. Expected Performance
- **Time Limit**: 150 seconds
- **Expected Processing**: 10-15 seconds total
- **Tables**: 3 target tables
- **Objects per Table**: 7-15 objects

## System Configuration

### Competition Parameters

#### 1. Detection Thresholds
```python
# In competition_ready_inference.py
self.min_confidence = 0.25      # Minimum detection confidence
self.roi_margin = 0.1           # ROI boundary margin (10%)
self.round1_max_time = 50       # Round 1 time limit
self.round2_max_time = 150      # Round 2 time limit
```

#### 2. Filtering Parameters
```python
# Interference filtering settings
min_area_ratio = 0.001          # Minimum object area (0.1% of image)
max_aspect_ratio = 8            # Maximum width/height ratio
roi_center_ratio = 0.8          # ROI center region (80%)
```

### Performance Optimization

#### 1. GPU Optimization
```python
# Ensure GPU utilization
device = 'cuda' if torch.cuda.is_available() else 'cpu'
model.to(device)

# Monitor GPU memory usage
torch.cuda.empty_cache()  # Clear cache if needed
```

#### 2. Processing Speed
```python
# Disable verbose output for speed
results = model(image_path, conf=0.25, verbose=False)

# Use appropriate image resolution
# Default: 640x640 (optimal speed/accuracy balance)
```

## Troubleshooting

### Common Issues

#### 1. Model Loading Errors
```bash
# Issue: Model file not found
# Solution: Verify model path
ls runs/detect/*/weights/best.pt

# Issue: CUDA out of memory
# Solution: Use CPU or reduce batch size
export CUDA_VISIBLE_DEVICES=""  # Force CPU usage
```

#### 2. Performance Issues
```bash
# Issue: Slow processing
# Check: GPU availability
python -c "import torch; print(torch.cuda.is_available())"

# Check: Model size
ls -lh runs/detect/*/weights/best.pt

# Solution: Use smaller model if needed
```

#### 3. Detection Issues
```python
# Issue: No detections
# Check: Confidence threshold
results = model(image, conf=0.1, verbose=True)  # Lower threshold

# Issue: Too many false positives
# Check: Filtering parameters
# Increase min_confidence or adjust ROI settings
```

### Validation Procedures

#### 1. Pre-Competition Testing
```bash
# Test with sample images
python competition_ready_inference.py

# Verify output format
python -c "import json; print(json.load(open('competition_round1_results.json')))"

# Check processing time
time python competition_ready_inference.py
```

#### 2. Competition Environment Testing
```python
# Test with competition-like conditions
import time

start_time = time.time()
results = system.run_competition_round("test_image.jpg", 1)
processing_time = time.time() - start_time

assert processing_time < 50, f"Processing too slow: {processing_time}s"
assert results['within_time_limit'], "Time limit exceeded"
```

## Competition Day Checklist

### Pre-Competition
- [ ] Environment setup verified
- [ ] Dependencies installed and tested
- [ ] Model files present and validated
- [ ] System performance tested
- [ ] Output format verified

### During Competition
- [ ] System initialized successfully
- [ ] Camera connection established
- [ ] Image capture working
- [ ] Processing time within limits
- [ ] Output format correct

### Post-Processing
- [ ] Results saved in correct format
- [ ] File naming convention followed
- [ ] Submission format validated
- [ ] Backup files created

## Emergency Procedures

### Fallback Options

#### 1. Model Fallback
```python
# If custom model fails, use pre-trained
try:
    model = YOLO('runs/detect/professional_yolov8_training/weights/best.pt')
except:
    model = YOLO('yolov8n.pt')  # Fallback to pre-trained
```

#### 2. Processing Fallback
```python
# If GPU fails, use CPU
try:
    model.to('cuda')
except:
    model.to('cpu')
    logger.warning("Using CPU fallback")
```

#### 3. Time Management
```python
# If processing is slow, reduce quality
if processing_time > time_limit * 0.8:
    # Reduce confidence threshold for speed
    results = model(image, conf=0.1, verbose=False)
```

This deployment guide ensures reliable system operation during the competition with comprehensive fallback procedures and performance optimization strategies.
