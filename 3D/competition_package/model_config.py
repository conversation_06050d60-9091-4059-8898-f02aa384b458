#!/usr/bin/env python3
"""
Model Configuration for Competition
Provides model path configuration and loading utilities

For 2025 China Robot Competition - Advanced Vision Challenge 3D Recognition

Date: 2025-01-17
Version: 1.0
"""

import os
from pathlib import Path

class ModelConfig:
    """
    Model configuration and path management for competition deployment
    """
    
    def __init__(self):
        """Initialize model configuration"""
        self.base_path = Path(__file__).parent
        self.model_weights_dir = self.base_path / "model_weights"
        
    def get_best_model_path(self):
        """
        Get the path to the best trained model
        
        Returns:
            str: Path to the best model file
        """
        # Priority order for model selection
        model_candidates = [
            self.model_weights_dir / "best.pt",
            "runs/detect/professional_yolov8_training/weights/best.pt",
            "runs/detect/competition_yolov8_training/weights/best.pt",
            "runs/detect/enhanced_yolov8_training/weights/best.pt",
            "runs/detect/train/weights/best.pt"
        ]
        
        for model_path in model_candidates:
            if os.path.exists(model_path):
                return str(model_path)
        
        # Fallback to pre-trained model
        return "yolov8n.pt"
    
    def verify_model_exists(self, model_path=None):
        """
        Verify that the model file exists
        
        Args:
            model_path: Optional specific model path to check
            
        Returns:
            bool: True if model exists, False otherwise
        """
        if model_path is None:
            model_path = self.get_best_model_path()
        
        return os.path.exists(model_path)
    
    def get_model_info(self):
        """
        Get information about the available model
        
        Returns:
            dict: Model information
        """
        model_path = self.get_best_model_path()
        
        info = {
            "model_path": model_path,
            "exists": self.verify_model_exists(model_path),
            "is_custom_trained": "best.pt" in model_path and "yolov8n.pt" not in model_path,
            "size_mb": 0
        }
        
        if info["exists"]:
            try:
                info["size_mb"] = round(os.path.getsize(model_path) / (1024 * 1024), 2)
            except:
                info["size_mb"] = 0
        
        return info

# Global model configuration instance
model_config = ModelConfig()

def get_competition_model_path():
    """
    Convenience function to get the competition model path
    
    Returns:
        str: Path to the best available model
    """
    return model_config.get_best_model_path()

def verify_competition_setup():
    """
    Verify that the competition setup is ready
    
    Returns:
        dict: Setup verification results
    """
    model_info = model_config.get_model_info()
    
    verification = {
        "model_available": model_info["exists"],
        "model_path": model_info["model_path"],
        "model_size_mb": model_info["size_mb"],
        "is_custom_trained": model_info["is_custom_trained"],
        "ready_for_competition": model_info["exists"]
    }
    
    return verification

if __name__ == "__main__":
    """Test model configuration"""
    print("Competition Model Configuration")
    print("=" * 50)
    
    verification = verify_competition_setup()
    
    for key, value in verification.items():
        print(f"{key}: {value}")
    
    if verification["ready_for_competition"]:
        print("\nSystem ready for competition deployment!")
    else:
        print("\nWarning: Model not found. Please ensure model is trained or available.")
