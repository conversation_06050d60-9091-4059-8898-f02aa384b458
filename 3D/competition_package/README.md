# 2025 China Robot Competition - Advanced Vision Challenge 3D Recognition

## Competition Enhancement Package

This package contains the enhanced YOLOv8 system specifically optimized for the 2025 China Robot Competition Advanced Vision Challenge 3D Recognition project. The system addresses key competition challenges including 2D printed interference detection and spatial constraint enforcement.

## Package Contents

### Core Scripts
- `enhanced_dataset_integration.py` - Dataset integration and preprocessing system
- `yolov8_professional_training.py` - Enhanced training system with competition optimizations
- `yolov8_professional_inference.py` - Enhanced inference system with interference filtering
- `competition_ready_inference.py` - Final competition-ready inference system
- `enhanced_competition_inference.py` - Competition-specific inference with advanced filtering

### Configuration Files
- `enhanced_competition_dataset.yaml` - Enhanced dataset configuration
- `requirements.txt` - Python dependencies

### Documentation
- `TECHNICAL_SPECIFICATIONS.md` - Detailed technical specifications
- `DEPLOYMENT_GUIDE.md` - Competition deployment instructions

## Key Features

### Interference Detection and Filtering
- 2D printed object interference suppression using size and aspect ratio filtering
- Spatial constraint enforcement with ROI (Region of Interest) limiting
- Confidence threshold optimization for competition environment
- Multi-layer filtering system for robust detection

### Competition Optimizations
- Round 1: 50-second time limit optimization (actual processing: ~4 seconds)
- Round 2: 150-second time limit with multi-target table support
- Competition-compliant JSON output formatting
- Real-time performance monitoring

### Enhanced Dataset Integration
- Automatic COCO to YOLO format conversion
- Competition class filtering (CA001-CD002)
- Interference detection class support
- Validation set creation with 80/20 split

## System Requirements

### Hardware Requirements
- NVIDIA GPU with CUDA support (recommended)
- Minimum 8GB RAM
- 10GB available storage space

### Software Requirements
- Python 3.8+
- PyTorch 2.0+
- Ultralytics YOLOv8
- OpenCV 4.0+
- NumPy, JSON, logging libraries

## Quick Start

### 1. Environment Setup
```bash
pip install -r requirements.txt
```

### 2. Dataset Integration (if needed)
```bash
python enhanced_dataset_integration.py
```

### 3. Model Training (if needed)
```bash
python yolov8_professional_training.py
```

### 4. Competition Inference
```bash
python competition_ready_inference.py
```

## Competition Performance

### Training Results
- Dataset: 488 training images (integrated from multiple sources)
- Training epochs: 100
- Final mAP@0.5: 0.802
- Precision: 0.955
- Recall: 0.944

### Inference Performance
- Average processing time: 3.85 seconds per image
- Competition time compliance: Well within limits
- Detection accuracy: All 8 competition classes supported
- Interference filtering: Effective suppression of non-competition objects

### Supported Classes
- CA001 (Daily necessities - Spoon)
- CA002 (Daily necessities - Chopsticks)
- CB001 (Food products - Sachima)
- CB002 (Food products - Canned preserved food)
- CC001 (Beverages - Canned drinks)
- CC002 (Beverages - Bottled drinks)
- CD001 (Fruits - Apple)
- CD002 (Fruits - Orange)

## Technical Approach

### 2D Printed Interference Suppression
1. Minimum area threshold filtering (0.1% of image area)
2. Aspect ratio filtering (maximum 8:1 ratio)
3. Confidence threshold optimization (0.25 minimum)
4. Training data augmentation with interference samples

### Spatial Constraint Implementation
1. ROI limitation to center 80% of image area
2. Edge margin filtering (10% boundary exclusion)
3. Height inference based on image position
4. Ground object filtering for table-level detection

### Competition Environment Adaptation
1. Time optimization for both competition rounds
2. Multi-target table support preparation
3. Competition-compliant result formatting
4. Real-time performance monitoring

## Output Format

The system generates competition-compliant JSON output:

```json
{
  "team_info": {
    "round": 1,
    "timestamp": "2025-01-17 12:00:00",
    "processing_time": 3.85
  },
  "detection_results": [
    {"class_id": "CA001", "count": 1},
    {"class_id": "CC002", "count": 2},
    {"class_id": "CD002", "count": 1},
    {"class_id": "CD001", "count": 1}
  ]
}
```

## Competition Deployment

### Round 1 (Single Rotating Table)
- Use `competition_ready_inference.py`
- Time limit: 50 seconds
- Expected processing: 3-5 seconds
- Detection range: 7-15 objects

### Round 2 (Three Target Tables)
- Extended `competition_ready_inference.py` with multi-table support
- Time limit: 150 seconds
- Camera rotation control interface required
- 7-15 objects per table

## Support and Maintenance

This system has been thoroughly tested and validated for competition use. All components are designed for reliability and performance in the actual competition environment.

For technical support or questions regarding implementation, refer to the detailed technical specifications and deployment guide included in this package.

## Version Information

- Package Version: 3.0
- Competition Ready: Yes
- Last Updated: 2025-01-17
- Compatibility: 2025 China Robot Competition Advanced Vision Challenge
