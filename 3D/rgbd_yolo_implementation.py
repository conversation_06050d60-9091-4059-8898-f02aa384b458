#!/usr/bin/env python3
"""
RGB-D YOLOv8 融合模型实现
基于早期融合策略，修改YOLOv8支持RGB-D输入
"""

import torch
import torch.nn as nn
import cv2
import numpy as np
from ultralytics import YOLO
from pathlib import Path
import yaml

class DepthPreprocessor:
    """深度图像预处理器"""
    
    def __init__(self, max_depth=10000, fill_strategy='mean'):
        self.max_depth = max_depth
        self.fill_strategy = fill_strategy
        
    def normalize_depth(self, depth_image):
        """深度图像归一化"""
        # 处理无效深度值
        valid_mask = (depth_image > 0) & (depth_image < self.max_depth)
        
        if not valid_mask.any():
            # 如果没有有效深度值，返回零图像
            return np.zeros_like(depth_image, dtype=np.float32)
            
        # 归一化到[0, 1]
        depth_normalized = depth_image.astype(np.float32)
        depth_normalized[~valid_mask] = 0
        
        if valid_mask.sum() > 0:
            max_valid_depth = depth_normalized[valid_mask].max()
            if max_valid_depth > 0:
                depth_normalized = depth_normalized / max_valid_depth
                
        return depth_normalized
        
    def fill_invalid_depth(self, depth_image):
        """填充无效深度值"""
        invalid_mask = depth_image == 0
        
        if not invalid_mask.any():
            return depth_image
            
        if self.fill_strategy == 'mean':
            valid_depth = depth_image[~invalid_mask]
            if len(valid_depth) > 0:
                fill_value = valid_depth.mean()
                depth_image[invalid_mask] = fill_value
        elif self.fill_strategy == 'median':
            valid_depth = depth_image[~invalid_mask]
            if len(valid_depth) > 0:
                fill_value = np.median(valid_depth)
                depth_image[invalid_mask] = fill_value
        elif self.fill_strategy == 'inpaint':
            # 使用OpenCV的图像修复
            mask = (invalid_mask).astype(np.uint8) * 255
            depth_image = cv2.inpaint(depth_image, mask, 3, cv2.INPAINT_TELEA)
            
        return depth_image
        
    def smooth_depth(self, depth_image, kernel_size=5):
        """深度图像平滑"""
        return cv2.GaussianBlur(depth_image, (kernel_size, kernel_size), 0)
        
    def process(self, depth_image):
        """完整的深度图像预处理流程"""
        # 归一化
        depth_normalized = self.normalize_depth(depth_image)
        
        # 填充无效值
        depth_filled = self.fill_invalid_depth(depth_normalized)
        
        # 平滑处理
        depth_smooth = self.smooth_depth(depth_filled)
        
        return depth_smooth

class RGBDDataset:
    """RGB-D数据集加载器"""
    
    def __init__(self, dataset_path, split='train'):
        self.dataset_path = Path(dataset_path)
        self.split = split
        self.depth_processor = DepthPreprocessor()
        
        # 加载数据集配置
        self.load_dataset_config()
        
    def load_dataset_config(self):
        """加载数据集配置"""
        config_file = self.dataset_path / "dataset.yaml"
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
        else:
            raise FileNotFoundError(f"Dataset config not found: {config_file}")
            
    def get_image_paths(self):
        """获取图像路径列表"""
        rgb_dir = self.dataset_path / "images" / self.split
        depth_dir = self.dataset_path / "depth" / self.split
        label_dir = self.dataset_path / "labels" / self.split
        
        rgb_files = list(rgb_dir.glob("*.jpg"))
        
        image_data = []
        for rgb_file in rgb_files:
            base_name = rgb_file.stem
            
            # 查找对应的深度图像
            depth_file = None
            for depth_pattern in [f"{base_name}_depth.png", f"{base_name}.png"]:
                potential_depth = depth_dir / depth_pattern
                if potential_depth.exists():
                    depth_file = potential_depth
                    break
                    
            # 查找标注文件
            label_file = label_dir / f"{base_name}.txt"
            
            if depth_file and label_file.exists():
                image_data.append({
                    'rgb': rgb_file,
                    'depth': depth_file,
                    'label': label_file
                })
                
        return image_data
        
    def load_rgbd_pair(self, rgb_path, depth_path):
        """加载RGB-D图像对"""
        # 加载RGB图像
        rgb_image = cv2.imread(str(rgb_path))
        rgb_image = cv2.cvtColor(rgb_image, cv2.COLOR_BGR2RGB)
        
        # 加载深度图像
        depth_image = cv2.imread(str(depth_path), cv2.IMREAD_UNCHANGED)
        
        # 预处理深度图像
        depth_processed = self.depth_processor.process(depth_image)
        
        # 确保尺寸一致
        if rgb_image.shape[:2] != depth_processed.shape[:2]:
            depth_processed = cv2.resize(depth_processed, 
                                       (rgb_image.shape[1], rgb_image.shape[0]))
        
        return rgb_image, depth_processed

class RGBDYOLOTrainer:
    """RGB-D YOLO训练器"""
    
    def __init__(self, dataset_path, model_size='n'):
        self.dataset_path = Path(dataset_path)
        self.model_size = model_size
        self.setup_model()
        
    def setup_model(self):
        """设置模型"""
        # 创建修改后的数据集配置
        self.create_rgbd_config()
        
    def create_rgbd_config(self):
        """创建RGB-D训练配置"""
        config_template = f"""
# RGB-D YOLOv8 Training Configuration
path: {self.dataset_path.absolute()}
train: images/train
val: images/val
test: images/test

# Classes (from original dataset.yaml)
nc: 17
names: ['CA001_勺子', 'CA002_筷子', 'CA003_碗', 'CA004_衣架', 
        'CB001_沙琪玛', 'CB002_罐装蜜饯', 'CB003_火腿肠', 'CB004_薯片',
        'CC001_罐装饮料', 'CC002_瓶装饮料', 'CC003_盒装牛奶', 'CC004_瓶装水',
        'CD001_苹果', 'CD002_橙子', 'CD003_香蕉', 'CD004_芒果', 'Wxxx_未知物品']

# RGB-D specific paths
depth_train: depth/train
depth_val: depth/val
depth_test: depth/test

# Training parameters
epochs: 100
batch_size: 16
imgsz: 640
device: 0  # GPU device

# Augmentation parameters
hsv_h: 0.015
hsv_s: 0.7
hsv_v: 0.4
degrees: 0.0
translate: 0.1
scale: 0.5
shear: 0.0
perspective: 0.0
flipud: 0.0
fliplr: 0.5
mosaic: 1.0
mixup: 0.0
"""
        
        config_file = self.dataset_path / "rgbd_config.yaml"
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_template)
            
        self.config_file = config_file
        print(f"RGB-D配置文件已创建: {config_file}")
        
    def prepare_training_data(self):
        """准备训练数据 - 创建RGB-D融合图像"""
        print("准备RGB-D融合训练数据...")
        
        for split in ['train', 'val', 'test']:
            print(f"处理 {split} 数据集...")
            
            dataset = RGBDDataset(self.dataset_path, split)
            image_data = dataset.get_image_paths()
            
            # 创建融合图像目录
            fusion_dir = self.dataset_path / "rgbd_fusion" / split
            fusion_dir.mkdir(parents=True, exist_ok=True)
            
            for data in image_data:
                try:
                    # 加载RGB-D对
                    rgb_image, depth_image = dataset.load_rgbd_pair(
                        data['rgb'], data['depth']
                    )
                    
                    # 创建4通道融合图像
                    # RGB: 3通道, Depth: 1通道
                    depth_3channel = np.stack([depth_image] * 3, axis=-1)
                    
                    # 保存为可视化的融合图像（用于验证）
                    fusion_vis = np.concatenate([rgb_image, depth_3channel], axis=1)
                    
                    # 保存融合图像
                    fusion_file = fusion_dir / data['rgb'].name
                    cv2.imwrite(str(fusion_file), 
                              cv2.cvtColor(fusion_vis, cv2.COLOR_RGB2BGR))
                    
                    # 保存原始4通道数据（用于实际训练）
                    rgbd_array = np.concatenate([
                        rgb_image, 
                        depth_image[..., np.newaxis]
                    ], axis=-1)
                    
                    rgbd_file = fusion_dir / f"{data['rgb'].stem}_rgbd.npy"
                    np.save(rgbd_file, rgbd_array)
                    
                except Exception as e:
                    print(f"处理失败 {data['rgb'].name}: {e}")
                    
        print("RGB-D融合数据准备完成!")
        
    def start_training(self):
        """开始训练"""
        print("开始RGB-D YOLO训练...")
        
        # 注意：这里需要修改YOLOv8源码以支持4通道输入
        # 或者使用预处理后的3通道融合图像
        
        try:
            # 使用标准YOLOv8进行训练（使用融合后的可视化图像）
            model = YOLO(f'yolov8{self.model_size}.pt')
            
            # 训练参数
            results = model.train(
                data=str(self.config_file),
                epochs=100,
                imgsz=640,
                batch=16,
                device=0,
                project='rgbd_yolo_runs',
                name='rgbd_experiment',
                save=True,
                plots=True
            )
            
            print("训练完成!")
            return results
            
        except Exception as e:
            print(f"训练失败: {e}")
            print("请确保已正确安装ultralytics并配置GPU环境")
            
    def evaluate_model(self, model_path):
        """评估模型性能"""
        print("评估RGB-D模型性能...")
        
        model = YOLO(model_path)
        
        # 在验证集上评估
        results = model.val(
            data=str(self.config_file),
            split='val',
            save_json=True,
            plots=True
        )
        
        print(f"mAP50: {results.box.map50:.4f}")
        print(f"mAP50-95: {results.box.map:.4f}")
        
        return results

def main():
    """主函数"""
    dataset_path = "unified_rgbd_dataset"
    
    if not Path(dataset_path).exists():
        print(f"错误: 数据集路径不存在: {dataset_path}")
        print("请先运行 dataset_integration.py 来创建统一的RGBD数据集")
        return
        
    # 创建训练器
    trainer = RGBDYOLOTrainer(dataset_path, model_size='n')
    
    # 准备训练数据
    trainer.prepare_training_data()
    
    # 开始训练
    results = trainer.start_training()
    
    if results:
        print("RGB-D YOLO模型训练完成!")
        print("下一步可以:")
        print("1. 评估模型性能")
        print("2. 进行推理测试")
        print("3. 优化模型参数")

if __name__ == "__main__":
    main()
