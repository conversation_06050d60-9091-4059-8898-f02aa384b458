#!/usr/bin/env python3
"""
测试YOLO训练的简单脚本
"""

import sys
import os
from pathlib import Path

# 添加路径
sys.path.append('/home/<USER>/claude/SpatialVLA/3D')

def test_yolo_training():
    """测试YOLO训练"""
    print("=== 测试YOLO训练 ===")
    
    try:
        # 导入YOLO
        from ultralytics import YOLO
        print("✓ YOLO导入成功")
        
        # 检查数据集配置
        dataset_config = "/home/<USER>/claude/SpatialVLA/3D/yolo11x/dataset/dataset.yaml"
        if not Path(dataset_config).exists():
            print(f"✗ 数据集配置文件不存在: {dataset_config}")
            return False
        
        print(f"✓ 数据集配置文件存在: {dataset_config}")
        
        # 检查数据集内容
        with open(dataset_config, 'r') as f:
            import yaml
            config = yaml.safe_load(f)
            print(f"✓ 数据集配置: {config}")
        
        # 初始化模型
        print("初始化YOLO11x模型...")
        model = YOLO('yolo11x.pt')
        print("✓ 模型初始化成功")
        
        # 创建输出目录
        output_dir = Path("/home/<USER>/claude/SpatialVLA/3D/yolo11x")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 开始短期训练测试
        print("开始短期训练测试（2个epoch）...")
        results = model.train(
            data=dataset_config,
            epochs=2,
            batch=4,
            imgsz=640,
            device='0',
            project=str(output_dir),
            name='test_training',
            exist_ok=True,
            verbose=True
        )
        
        print("✓ 训练测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 训练测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_yolo_training()
    if success:
        print("🎉 YOLO训练测试成功！可以开始正式训练。")
    else:
        print("❌ YOLO训练测试失败，需要检查配置。")
