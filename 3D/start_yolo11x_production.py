#!/usr/bin/env python3
"""
YOLO11x生产环境训练脚本
基于测试成功的配置，启动完整的200 epoch训练
"""

import os
import time
import json
from pathlib import Path
from ultralytics import YOL<PERSON>

def main():
    """主训练函数"""
    print("=== YOLO11x生产环境训练 ===")
    print(f"开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 创建输出目录
    output_dir = Path("/home/<USER>/claude/SpatialVLA/3D/yolo11x")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 数据集配置
    dataset_config = "/home/<USER>/claude/SpatialVLA/3D/yolo11x/dataset/dataset.yaml"
    
    try:
        # 初始化YOLO11x模型
        print("加载YOLO11x模型...")
        start_time = time.time()
        model = YOLO('yolo11x.pt')
        load_time = time.time() - start_time
        print(f"✓ 模型加载完成，耗时: {load_time:.2f}秒")
        
        # 开始完整训练
        print("开始完整训练（200 epochs）...")
        train_start_time = time.time()
        
        results = model.train(
            # 基本配置
            data=dataset_config,
            epochs=200,
            patience=50,
            batch=8,
            imgsz=640,
            device='0',
            workers=8,
            
            # 保存配置
            project=str(output_dir),
            name='yolo11x_merged_dataset_production',
            exist_ok=True,
            save=True,
            save_period=10,
            plots=True,
            
            # 优化器配置（基于YOLO11n成功配置）
            optimizer='AdamW',
            lr0=0.001,
            lrf=0.01,
            momentum=0.937,
            weight_decay=0.0005,
            warmup_epochs=3,
            warmup_momentum=0.8,
            warmup_bias_lr=0.1,
            
            # 损失函数权重
            box=7.5,
            cls=0.5,
            dfl=1.5,
            
            # 数据增强
            hsv_h=0.015,
            hsv_s=0.7,
            hsv_v=0.4,
            degrees=15.0,
            translate=0.1,
            scale=0.5,
            shear=0.0,
            perspective=0.0,
            flipud=0.0,
            fliplr=0.5,
            mosaic=1.0,
            mixup=0.0,
            copy_paste=0.0,
            
            # 其他配置
            val=True,
            cache=False,
            amp=True,
            close_mosaic=10,
            verbose=True,
            seed=0,
            deterministic=True
        )
        
        train_time = time.time() - train_start_time
        print(f"✓ 训练完成！总耗时: {train_time/3600:.2f}小时")
        
        # 评估模型
        print("=== 评估最佳模型 ===")
        best_model_path = output_dir / 'yolo11x_merged_dataset_production' / 'weights' / 'best.pt'
        
        if best_model_path.exists():
            best_model = YOLO(str(best_model_path))
            val_results = best_model.val()
            
            metrics = {
                'mAP50': float(val_results.box.map50),
                'mAP50_95': float(val_results.box.map),
                'precision': float(val_results.box.mp),
                'recall': float(val_results.box.mr)
            }
            
            print(f"✓ 最终模型性能:")
            print(f"  mAP@0.5: {metrics['mAP50']:.4f}")
            print(f"  mAP@0.5:0.95: {metrics['mAP50_95']:.4f}")
            print(f"  Precision: {metrics['precision']:.4f}")
            print(f"  Recall: {metrics['recall']:.4f}")
        else:
            print("⚠ 未找到最佳模型文件")
            metrics = {}
        
        # 保存完整的训练结果
        training_results = {
            'model_name': 'yolo11x',
            'description': 'YOLO11x在merged_rgbd_dataset上的完整训练',
            'dataset': {
                'name': 'merged_rgbd_dataset',
                'total_images': 1418,
                'train_images': 1213,
                'val_images': 205,
                'classes': 9,
                'rgb_depth_paired': True
            },
            'training_config': {
                'epochs': 200,
                'batch_size': 8,
                'image_size': 640,
                'optimizer': 'AdamW',
                'learning_rate': 0.001,
                'patience': 50,
                'data_augmentation': True
            },
            'performance': {
                'training_time_hours': train_time / 3600,
                'model_loading_time_seconds': load_time,
                'final_metrics': metrics
            },
            'model_files': {
                'best_model': str(best_model_path) if best_model_path.exists() else None,
                'last_model': str(output_dir / 'yolo11x_merged_dataset_production' / 'weights' / 'last.pt'),
                'training_results': str(output_dir / 'yolo11x_merged_dataset_production' / 'results.csv')
            },
            'comparison_with_yolo11n': {
                'model_size': 'YOLO11x (56.8M parameters) vs YOLO11n (2.6M parameters)',
                'dataset_improvement': 'merged_rgbd_dataset (1418 images) vs competition_2025_dataset (971 images)',
                'expected_improvement': '更高精度，更好的泛化能力'
            },
            'training_date': time.strftime('%Y-%m-%d %H:%M:%S'),
            'status': 'completed'
        }
        
        # 保存结果
        results_file = output_dir / 'training_results_yolo11x_production.json'
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(training_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n=== 训练完成总结 ===")
        print(f"模型: YOLO11x (56.8M parameters)")
        print(f"数据集: merged_rgbd_dataset (1418 images)")
        print(f"训练时间: {training_results['performance']['training_time_hours']:.2f} 小时")
        if metrics:
            print(f"最终性能:")
            print(f"  mAP@0.5: {metrics['mAP50']:.4f}")
            print(f"  mAP@0.5:0.95: {metrics['mAP50_95']:.4f}")
        print(f"模型保存: {training_results['model_files']['best_model']}")
        print(f"结果文件: {results_file}")
        print("🎉 YOLO11x训练完成！")
        
        return True
        
    except Exception as e:
        print(f"✗ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ YOLO11x训练流程成功完成！")
    else:
        print("\n❌ YOLO11x训练流程失败！")
