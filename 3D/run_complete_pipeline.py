#!/usr/bin/env python3
"""
2025年比赛RGB-D YOLO检测系统 - 一键运行脚本
支持完整流程或分步骤执行
"""

import os
import sys
import time
import argparse
import subprocess
from pathlib import Path
import json

class CompetitionPipeline:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.steps_completed = []
        self.start_time = time.time()
        
    def log(self, message, level="INFO"):
        """日志输出"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
        
    def run_command(self, command, description, cwd=None):
        """运行命令并处理结果"""
        self.log(f"开始: {description}")
        self.log(f"命令: {command}")
        
        try:
            if cwd is None:
                cwd = self.project_root
                
            result = subprocess.run(
                command, 
                shell=True, 
                cwd=cwd,
                capture_output=True, 
                text=True,
                timeout=7200  # 2小时超时
            )
            
            if result.returncode == 0:
                self.log(f"✅ 完成: {description}")
                return True
            else:
                self.log(f"❌ 失败: {description}", "ERROR")
                self.log(f"错误输出: {result.stderr}", "ERROR")
                return False
                
        except subprocess.TimeoutExpired:
            self.log(f"⏰ 超时: {description}", "ERROR")
            return False
        except Exception as e:
            self.log(f"❌ 异常: {description} - {e}", "ERROR")
            return False
            
    def check_prerequisites(self):
        """检查前置条件"""
        self.log("🔍 检查前置条件...")
        
        # 检查Python版本
        if sys.version_info < (3, 8):
            self.log("❌ Python版本需要>=3.8", "ERROR")
            return False
            
        # 检查必要的包
        required_packages = {
            "torch": "torch",
            "ultralytics": "ultralytics",
            "opencv-python": "cv2",
            "numpy": "numpy"
        }
        missing_packages = []

        for package_name, import_name in required_packages.items():
            try:
                __import__(import_name)
            except ImportError:
                missing_packages.append(package_name)
                
        if missing_packages:
            self.log(f"❌ 缺少必要包: {missing_packages}", "ERROR")
            self.log("请运行: pip install -r requirements.txt", "INFO")
            return False
            
        # 检查数据集
        dataset_paths = [
            self.project_root / "dataset",
            self.project_root / "dataset-20250723-coco"
        ]
        
        dataset_exists = any(path.exists() for path in dataset_paths)
        if not dataset_exists:
            self.log("⚠️  原始数据集不存在，将跳过数据准备步骤", "WARNING")
            
        self.log("✅ 前置条件检查完成")
        return True
        
    def step_data_preparation(self):
        """步骤1: 数据准备"""
        self.log("📦 步骤1: 数据准备")
        
        # 检查原始数据集是否存在
        dataset_paths = [
            self.project_root / "dataset",
            self.project_root / "dataset-20250723-coco"
        ]
        
        if not any(path.exists() for path in dataset_paths):
            self.log("⚠️  原始数据集不存在，跳过数据准备", "WARNING")
            return True
            
        # 运行数据准备脚本
        success = self.run_command(
            "python competition_2025_dataset_preparation.py",
            "重新整理数据集"
        )
        
        if success:
            self.steps_completed.append("data_preparation")
            
        return success
        
    def step_training(self):
        """步骤2: 模型训练"""
        self.log("🏋️ 步骤2: 模型训练")
        
        # 检查数据集是否准备好
        dataset_path = self.project_root / "competition_2025_dataset"
        if not dataset_path.exists():
            self.log("❌ 数据集未准备，请先运行数据准备步骤", "ERROR")
            return False
            
        training_steps = [
            ("python competition_2025_training.py", "基础YOLOv11n训练"),
            ("python optimized_training_for_thin_small_objects.py", "困难类别优化训练"),
            ("python refined_training_system.py", "精细化两阶段训练"),
        ]
        
        success_count = 0
        for command, description in training_steps:
            if self.run_command(command, description, cwd=self.project_root / "training"):
                success_count += 1
            else:
                self.log(f"⚠️  {description}失败，继续下一个训练步骤", "WARNING")
                
        if success_count > 0:
            self.steps_completed.append("training")
            return True
        else:
            self.log("❌ 所有训练步骤都失败了", "ERROR")
            return False
            
    def step_evaluation(self):
        """步骤3: 模型评估"""
        self.log("📊 步骤3: 模型评估")
        
        # 检查是否有训练好的模型
        model_dirs = [
            self.project_root / "competition_2025_models",
            self.project_root / "optimized_models",
            self.project_root / "refined_models"
        ]
        
        if not any(path.exists() for path in model_dirs):
            self.log("❌ 没有找到训练好的模型，请先运行训练步骤", "ERROR")
            return False
            
        # 运行评估脚本
        success = self.run_command(
            "python comprehensive_model_evaluation.py",
            "全面模型评估",
            cwd=self.project_root / "evaluation"
        )
        
        if success:
            self.steps_completed.append("evaluation")
            
        return success
        
    def step_inference_test(self):
        """步骤4: 推理测试"""
        self.log("🎯 步骤4: 推理测试")
        
        # 运行推理测试
        success = self.run_command(
            "python ultimate_competition_system.py",
            "终极比赛系统测试",
            cwd=self.project_root / "inference"
        )
        
        if success:
            self.steps_completed.append("inference_test")
            
        return success
        
    def generate_summary_report(self):
        """生成总结报告"""
        self.log("📋 生成总结报告...")
        
        total_time = time.time() - self.start_time
        
        report = {
            "pipeline_execution": {
                "start_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(self.start_time)),
                "end_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                "total_time_hours": total_time / 3600,
                "steps_completed": self.steps_completed,
                "success_rate": len(self.steps_completed) / 4 * 100
            },
            "next_steps": [],
            "model_recommendations": {
                "highest_accuracy": "refined_models/refined_yolo11n_stage1_balanced/weights/best.pt",
                "difficult_categories": "optimized_models_s/class_weighted_yolo11s_thin_small/weights/best.pt",
                "balanced_performance": "optimized_models/class_weighted_yolo11n_thin_small/weights/best.pt"
            }
        }
        
        # 根据完成的步骤给出建议
        if "data_preparation" not in self.steps_completed:
            report["next_steps"].append("运行数据准备: python run_complete_pipeline.py --mode data_prep")
            
        if "training" not in self.steps_completed:
            report["next_steps"].append("运行模型训练: python run_complete_pipeline.py --mode training")
            
        if "evaluation" not in self.steps_completed:
            report["next_steps"].append("运行模型评估: python run_complete_pipeline.py --mode evaluation")
            
        if "inference_test" not in self.steps_completed:
            report["next_steps"].append("运行推理测试: python run_complete_pipeline.py --mode inference")
            
        # 保存报告
        report_file = self.project_root / "pipeline_execution_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
            
        self.log(f"📄 总结报告已保存: {report_file}")
        
        # 显示总结
        self.log("🎉 流水线执行完成!")
        self.log(f"⏱️  总耗时: {total_time/3600:.2f}小时")
        self.log(f"✅ 完成步骤: {len(self.steps_completed)}/4")
        self.log(f"📈 成功率: {len(self.steps_completed)/4*100:.1f}%")
        
        if report["next_steps"]:
            self.log("📋 建议的下一步操作:")
            for step in report["next_steps"]:
                self.log(f"  - {step}")
        else:
            self.log("🏆 所有步骤都已完成，系统已就绪!")
            
    def run_full_pipeline(self):
        """运行完整流水线"""
        self.log("🚀 开始运行完整流水线")
        
        if not self.check_prerequisites():
            return False
            
        steps = [
            self.step_data_preparation,
            self.step_training,
            self.step_evaluation,
            self.step_inference_test
        ]
        
        for step in steps:
            if not step():
                self.log("⚠️  步骤失败，但继续执行后续步骤", "WARNING")
                
        self.generate_summary_report()
        return True
        
    def run_single_step(self, step_name):
        """运行单个步骤"""
        self.log(f"🎯 运行单个步骤: {step_name}")
        
        if not self.check_prerequisites():
            return False
            
        step_map = {
            "data_prep": self.step_data_preparation,
            "training": self.step_training,
            "evaluation": self.step_evaluation,
            "inference": self.step_inference_test
        }
        
        if step_name not in step_map:
            self.log(f"❌ 未知步骤: {step_name}", "ERROR")
            return False
            
        success = step_map[step_name]()
        self.generate_summary_report()
        return success

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="2025年比赛RGB-D YOLO检测系统 - 一键运行脚本")
    parser.add_argument(
        "--mode", 
        choices=["full", "data_prep", "training", "evaluation", "inference"],
        default="full",
        help="运行模式: full(完整流程), data_prep(数据准备), training(训练), evaluation(评估), inference(推理)"
    )
    parser.add_argument(
        "--verbose", 
        action="store_true",
        help="详细输出"
    )
    
    args = parser.parse_args()
    
    # 创建流水线实例
    pipeline = CompetitionPipeline()
    
    # 运行指定模式
    if args.mode == "full":
        success = pipeline.run_full_pipeline()
    else:
        success = pipeline.run_single_step(args.mode)
        
    # 退出码
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
