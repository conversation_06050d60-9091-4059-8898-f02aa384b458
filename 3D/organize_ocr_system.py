#!/usr/bin/env python3
"""
整理OCR推理系统到专门文件夹
"""

import shutil
import os
from pathlib import Path

def organize_ocr_system():
    """整理OCR系统文件"""
    print("🗂️  整理OCR推理系统文件...")
    
    # 创建OCR系统目录
    ocr_dir = Path("OCR_Inference_System")
    ocr_dir.mkdir(exist_ok=True)
    
    # 需要移动的文件
    files_to_move = [
        # 核心推理系统
        "enhanced_inference_with_ocr.py",
        "competition_2025_inference_system.py",
        
        # OCR测试工具
        "test_ocr_on_unknown_items.py", 
        "test_ocr_on_text_images.py",
        "create_text_test_images.py",
        
        # 基础推理测试
        "run_inference_test.py",
        
        # 结果文件
        "enhanced_inference_results.json",
        "text_image_ocr_results.json",
        "ocr_test_results.json",
        "inference_test_results.json",
        
        # 报告文件
        "final_competition_system_report.md",
        "inference_results_summary.md"
    ]
    
    # 需要移动的目录
    dirs_to_move = [
        "ocr_test_images",
        "ocr_test_rois"
    ]
    
    moved_files = []
    
    # 移动文件
    for file_name in files_to_move:
        source = Path(file_name)
        if source.exists():
            target = ocr_dir / file_name
            shutil.copy2(source, target)
            moved_files.append(file_name)
            print(f"  ✅ {file_name}")
        else:
            print(f"  ⚠️  文件不存在: {file_name}")
    
    # 移动目录
    for dir_name in dirs_to_move:
        source = Path(dir_name)
        if source.exists():
            target = ocr_dir / dir_name
            if target.exists():
                shutil.rmtree(target)
            shutil.copytree(source, target)
            moved_files.append(dir_name)
            print(f"  ✅ {dir_name}/")
        else:
            print(f"  ⚠️  目录不存在: {dir_name}")
    
    # 复制模型文件链接
    model_dir = Path("competition_2025_models")
    if model_dir.exists():
        target_model = ocr_dir / "models"
        target_model.mkdir(exist_ok=True)
        
        # 创建模型路径说明文件
        model_info = target_model / "model_path.txt"
        with open(model_info, 'w', encoding='utf-8') as f:
            f.write("模型路径: ../competition_2025_models/competition_2025_yolo11n/weights/best.pt\n")
            f.write("请确保该路径下的模型文件存在\n")
        
        print(f"  ✅ 模型路径说明: models/model_path.txt")
    
    print(f"\n✅ 整理完成!")
    print(f"📁 目标目录: {ocr_dir}")
    print(f"📊 移动文件数: {len(moved_files)}")
    
    return ocr_dir

def main():
    print("=== OCR推理系统文件整理 ===")
    
    ocr_dir = organize_ocr_system()
    
    print(f"\n📋 使用说明:")
    print(f"1. 进入目录: cd {ocr_dir}")
    print(f"2. 测试真实未知物品: python test_real_unknown_items.py")
    print(f"3. 运行完整推理: python enhanced_inference_with_ocr.py")
    print(f"4. 查看系统报告: cat final_competition_system_report.md")

if __name__ == "__main__":
    main()
