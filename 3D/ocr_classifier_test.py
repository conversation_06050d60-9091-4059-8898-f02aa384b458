#!/usr/bin/env python3
"""
OCR分类器测试器
验证OCR关键词映射的分类准确率，确保达到90%以上的要求
"""

from ocr_keyword_mapping import OCRKeywordMapper
from typing import List, Tuple, Dict
import json

class OCRClassifierTester:
    """OCR分类器测试器"""
    
    def __init__(self):
        self.mapper = OCRKeywordMapper()
        
        # 基于未知物txt文件的真实测试用例
        self.test_cases = [
            # W001 数学类书籍
            ("高等数学", "W001"),
            ("工程数学", "W001"),
            ("概率论与数理统计", "W001"),
            ("数学", "W001"),
            ("数理", "W001"),
            ("线性代数", "W001"),
            ("微积分", "W001"),
            
            # W002 物理类书籍
            ("电路", "W002"),
            ("大学物理", "W002"),
            ("物理", "W002"),
            ("电磁场与电磁波", "W002"),
            ("电磁场电磁波", "W002"),
            ("电磁场", "W002"),
            ("电磁波", "W002"),
            
            # W003 药盒类
            ("布洛芬胶囊", "W003"),
            ("胶囊", "W003"),
            ("感冒清热颗粒", "W003"),
            ("颗粒", "W003"),
            ("金银花口服液", "W003"),
            ("口服液", "W003"),
            ("莲花清瘟胶囊", "W003"),
            
            # W004 电子类
            ("小米充电宝", "W004"),
            ("xiaomi", "W004"),
            ("南孚电池", "W004"),
            ("南孚", "W004"),
            ("华为手机包装盒", "W004"),
            ("华为", "W004"),
            ("充电宝", "W004"),
            
            # W005 英语类书籍
            ("新世界交互英语", "W005"),
            ("英语", "W005"),
            ("雅思写作", "W005"),
            ("雅思", "W005"),
            ("IELTS", "W005"),
            ("英语字母", "W005"),
            ("雅思王阅读", "W005"),
            
            # W006 文学类书籍
            ("习近平新时代特色主义思想", "W006"),
            ("思想", "W006"),
            ("毛泽东思想", "W006"),
            ("毛泽东", "W006"),
            
            # W007 速食类
            ("香辣牛肉面", "W007"),
            ("方便面", "W007"),
            ("茄皇牛肉面", "W007"),
            ("鲜虾鱼板面", "W007"),
            ("老母鸡汤面", "W007"),
            ("红烧牛肉面", "W007"),
            ("牛肉面", "W007"),
            
            # W008 文具类
            ("订书钉", "W008"),
            ("墨水", "W008"),
            ("ink", "W008"),
            ("修正带", "W008"),
            ("笔芯", "W008"),
            ("文具", "W008"),
        ]
        
        # 困难测试用例（模糊匹配）
        self.challenging_cases = [
            ("高等數學", "W001"),  # 繁体字
            ("xiaomi充电宝", "W004"),  # 中英混合
            ("雅思IELTS", "W005"),  # 中英混合
            ("方便面速食", "W007"),  # 多关键词
            ("墨水ink", "W008"),  # 中英混合
            ("物理电路", "W002"),  # 多关键词
            ("胶囊药盒", "W003"),  # 多关键词
        ]
        
        # 负面测试用例（应该无法分类或分类错误）
        self.negative_cases = [
            "完全不相关的文本",
            "随机字符串abcdef",
            "123456789",
            "空白内容",
            "",
        ]
    
    def run_basic_tests(self) -> Dict[str, float]:
        """运行基础测试"""
        print("🧪 运行基础分类测试...")
        
        correct = 0
        total = len(self.test_cases)
        category_stats = {}
        
        for text, expected_category in self.test_cases:
            predicted_category, confidence = self.mapper.classify_text(text)
            
            # 统计各类别的准确率
            if expected_category not in category_stats:
                category_stats[expected_category] = {'correct': 0, 'total': 0}
            
            category_stats[expected_category]['total'] += 1
            
            if predicted_category == expected_category:
                correct += 1
                category_stats[expected_category]['correct'] += 1
                status = "✅"
            else:
                status = "❌"
            
            print(f"  {status} '{text}' -> 预测: {predicted_category}, 期望: {expected_category} [置信度: {confidence:.3f}]")
        
        accuracy = correct / total
        print(f"\n📊 基础测试结果: {correct}/{total} = {accuracy:.3f} ({accuracy*100:.1f}%)")
        
        # 打印各类别统计
        print("\n📋 各类别准确率:")
        for category, stats in category_stats.items():
            cat_accuracy = stats['correct'] / stats['total']
            category_name = self.mapper.get_category_info(category).get('name', category)
            print(f"  {category} ({category_name}): {stats['correct']}/{stats['total']} = {cat_accuracy:.3f} ({cat_accuracy*100:.1f}%)")
        
        return {'overall_accuracy': accuracy, 'category_stats': category_stats}
    
    def run_challenging_tests(self) -> Dict[str, float]:
        """运行困难测试用例"""
        print("\n🔥 运行困难测试用例...")
        
        correct = 0
        total = len(self.challenging_cases)
        
        for text, expected_category in self.challenging_cases:
            predicted_category, confidence = self.mapper.classify_text(text)
            
            if predicted_category == expected_category:
                correct += 1
                status = "✅"
            else:
                status = "❌"
            
            print(f"  {status} '{text}' -> 预测: {predicted_category}, 期望: {expected_category} [置信度: {confidence:.3f}]")
        
        accuracy = correct / total
        print(f"\n📊 困难测试结果: {correct}/{total} = {accuracy:.3f} ({accuracy*100:.1f}%)")
        
        return {'challenging_accuracy': accuracy}
    
    def run_negative_tests(self) -> Dict[str, int]:
        """运行负面测试用例"""
        print("\n🚫 运行负面测试用例...")
        
        correctly_rejected = 0
        total = len(self.negative_cases)
        
        for text in self.negative_cases:
            predicted_category, confidence = self.mapper.classify_text(text)
            
            if predicted_category is None:
                correctly_rejected += 1
                status = "✅"
                result = "正确拒绝"
            else:
                status = "⚠️"
                category_name = self.mapper.get_category_info(predicted_category).get('name', predicted_category)
                result = f"错误分类为 {predicted_category} ({category_name})"
            
            print(f"  {status} '{text}' -> {result} [置信度: {confidence:.3f}]")
        
        rejection_rate = correctly_rejected / total
        print(f"\n📊 负面测试结果: {correctly_rejected}/{total} = {rejection_rate:.3f} ({rejection_rate*100:.1f}%)")
        
        return {'rejection_rate': rejection_rate, 'correctly_rejected': correctly_rejected}
    
    def run_comprehensive_test(self) -> Dict[str, any]:
        """运行综合测试"""
        print("🚀 开始OCR分类器综合测试")
        print("="*60)
        
        # 运行各项测试
        basic_results = self.run_basic_tests()
        challenging_results = self.run_challenging_tests()
        negative_results = self.run_negative_tests()
        
        # 计算综合得分
        overall_score = (
            basic_results['overall_accuracy'] * 0.7 +  # 基础测试权重70%
            challenging_results['challenging_accuracy'] * 0.2 +  # 困难测试权重20%
            negative_results['rejection_rate'] * 0.1  # 负面测试权重10%
        )
        
        print("\n" + "="*60)
        print("📈 综合测试结果")
        print("="*60)
        print(f"基础分类准确率: {basic_results['overall_accuracy']*100:.1f}%")
        print(f"困难用例准确率: {challenging_results['challenging_accuracy']*100:.1f}%")
        print(f"负面用例拒绝率: {negative_results['rejection_rate']*100:.1f}%")
        print(f"综合得分: {overall_score*100:.1f}%")
        
        # 判断是否达到要求
        if overall_score >= 0.9:
            print("✅ 测试通过！分类准确率达到90%以上要求")
            test_passed = True
        else:
            print("❌ 测试未通过！分类准确率未达到90%要求")
            test_passed = False
        
        print("="*60)
        
        # 返回完整结果
        return {
            'test_passed': test_passed,
            'overall_score': overall_score,
            'basic_results': basic_results,
            'challenging_results': challenging_results,
            'negative_results': negative_results
        }
    
    def save_test_results(self, results: Dict, output_path: str = "ocr_test_results.json"):
        """保存测试结果"""
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"📄 测试结果已保存到: {output_path}")

def main():
    """主函数"""
    tester = OCRClassifierTester()
    results = tester.run_comprehensive_test()
    tester.save_test_results(results)
    
    return results['test_passed']

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
