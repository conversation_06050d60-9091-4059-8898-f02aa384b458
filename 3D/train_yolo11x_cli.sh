#!/bin/bash

# YOLO11x训练脚本 - 使用命令行工具
# 基于YOLO11n的成功配置

echo "=== YOLO11x训练开始 ==="
echo "时间: $(date)"

# 激活conda环境
source /home/<USER>/jing_audio/anaconda3/bin/activate efficientdet

# 设置工作目录
cd /home/<USER>/claude/SpatialVLA/3D

# 检查环境
echo "Python版本: $(python --version)"
echo "检查YOLO..."
python -c "from ultralytics import YOLO; print('✓ YOLO导入成功')"

# 创建输出目录
mkdir -p /home/<USER>/claude/SpatialVLA/3D/yolo11x/training_logs

# 使用yolo命令行工具训练
echo "开始YOLO11x训练..."

yolo detect train \
    model=yolo11x.pt \
    data=/home/<USER>/claude/SpatialVLA/3D/yolo11x/dataset/dataset.yaml \
    epochs=200 \
    patience=50 \
    batch=8 \
    imgsz=640 \
    device=0 \
    workers=8 \
    project=/home/<USER>/claude/SpatialVLA/3D/yolo11x \
    name=yolo11x_merged_dataset \
    exist_ok=True \
    save=True \
    save_period=10 \
    plots=True \
    optimizer=AdamW \
    lr0=0.001 \
    lrf=0.01 \
    momentum=0.937 \
    weight_decay=0.0005 \
    warmup_epochs=3 \
    warmup_momentum=0.8 \
    warmup_bias_lr=0.1 \
    box=7.5 \
    cls=0.5 \
    dfl=1.5 \
    hsv_h=0.015 \
    hsv_s=0.7 \
    hsv_v=0.4 \
    degrees=15.0 \
    translate=0.1 \
    scale=0.5 \
    shear=0.0 \
    perspective=0.0 \
    flipud=0.0 \
    fliplr=0.5 \
    mosaic=1.0 \
    mixup=0.0 \
    copy_paste=0.0 \
    val=True \
    cache=False \
    amp=True \
    close_mosaic=10 \
    verbose=True \
    seed=0 \
    deterministic=True \
    2>&1 | tee /home/<USER>/claude/SpatialVLA/3D/yolo11x/training_logs/training_$(date +%Y%m%d_%H%M%S).log

echo "训练完成时间: $(date)"
echo "日志保存在: /home/<USER>/claude/SpatialVLA/3D/yolo11x/training_logs/"

# 评估模型
if [ -f "/home/<USER>/claude/SpatialVLA/3D/yolo11x/yolo11x_merged_dataset/weights/best.pt" ]; then
    echo "评估最佳模型..."
    yolo detect val model=/home/<USER>/claude/SpatialVLA/3D/yolo11x/yolo11x_merged_dataset/weights/best.pt \
        data=/home/<USER>/claude/SpatialVLA/3D/yolo11x/dataset/dataset.yaml
fi

echo "✅ YOLO11x训练流程完成！"
