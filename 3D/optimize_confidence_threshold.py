#!/usr/bin/env python3
"""
优化置信度阈值以提升困难类别的检测效果
"""

import os
import numpy as np
from pathlib import Path
from ultralytics import YOLO
import cv2
import json

class ConfidenceThresholdOptimizer:
    def __init__(self, test_image_dir="competition_2025_dataset/images/test"):
        self.test_image_dir = Path(test_image_dir)
        self.models = {}
        
        # 比赛类别映射
        self.category_mapping = {
            0: 'CA001_衣架',
            1: 'CA002_牙刷', 
            2: 'CB001_果冻',
            3: 'CB002_长方形状饼干',
            4: 'CC001_罐装饮料',
            5: 'CC002_瓶装饮料',
            6: 'CD001_香蕉',
            7: 'CD002_橙子',
            8: 'Wxxx_未知物品'
        }
        
        # 困难类别
        self.difficult_categories = ['CA002_牙刷', 'CB001_果冻']
        
    def load_models(self):
        """加载模型"""
        model_paths = {
            "基线_YOLOv11n": "competition_2025_models/competition_2025_yolo11n/weights/best.pt",
            "优化_YOLOv11n": "optimized_models/class_weighted_yolo11n_thin_small/weights/best.pt",
        }
        
        for model_name, model_path in model_paths.items():
            if os.path.exists(model_path):
                print(f"加载模型: {model_name}")
                self.models[model_name] = YOLO(model_path)
            else:
                print(f"模型不存在: {model_path}")
                
    def test_confidence_thresholds(self, conf_thresholds=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7]):
        """测试不同置信度阈值的效果"""
        if not self.test_image_dir.exists():
            print(f"测试图像目录不存在: {self.test_image_dir}")
            return
            
        test_images = list(self.test_image_dir.glob("*.jpg"))[:20]  # 测试20张图像
        
        if not test_images:
            print("没有找到测试图像")
            return
            
        results = {}
        
        for model_name, model in self.models.items():
            print(f"\n测试模型: {model_name}")
            model_results = {}
            
            for conf_threshold in conf_thresholds:
                print(f"  测试置信度阈值: {conf_threshold}")
                
                total_detections = 0
                difficult_detections = {cat: 0 for cat in self.difficult_categories}
                
                for image_path in test_images:
                    image = cv2.imread(str(image_path))
                    if image is None:
                        continue
                        
                    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    
                    # 运行推理
                    model_results_raw = model(image_rgb, conf=conf_threshold, verbose=False)
                    
                    # 解析结果
                    if model_results_raw[0].boxes is not None:
                        classes = model_results_raw[0].boxes.cls.cpu().numpy().astype(int)
                        
                        for cls in classes:
                            total_detections += 1
                            category_name = self.category_mapping.get(cls, 'Unknown')
                            if category_name in self.difficult_categories:
                                difficult_detections[category_name] += 1
                
                # 计算平均值
                avg_total = total_detections / len(test_images)
                avg_difficult = {cat: count / len(test_images) 
                               for cat, count in difficult_detections.items()}
                
                model_results[conf_threshold] = {
                    'avg_total_detections': avg_total,
                    'avg_difficult_detections': avg_difficult,
                    'total_difficult': sum(avg_difficult.values())
                }
                
                print(f"    平均检测数: {avg_total:.1f}, 困难类别: {sum(avg_difficult.values()):.1f}")
            
            results[model_name] = model_results
        
        return results
        
    def find_optimal_thresholds(self, results):
        """找到最优的置信度阈值"""
        print(f"\n{'='*80}")
        print("置信度阈值优化结果")
        print(f"{'='*80}")
        
        for model_name, model_results in results.items():
            print(f"\n模型: {model_name}")
            print(f"{'置信度阈值':<12} {'总检测数':<10} {'困难类别':<10} {'牙刷':<8} {'果冻':<8}")
            print("-" * 60)
            
            best_threshold = None
            best_difficult_score = 0
            
            for conf_threshold, result in model_results.items():
                total_difficult = result['total_difficult']
                ca002_count = result['avg_difficult_detections']['CA002_牙刷']
                cb001_count = result['avg_difficult_detections']['CB001_果冻']
                
                print(f"{conf_threshold:<12} {result['avg_total_detections']:<10.1f} "
                      f"{total_difficult:<10.1f} {ca002_count:<8.1f} {cb001_count:<8.1f}")
                
                # 寻找困难类别检测数最多的阈值
                if total_difficult > best_difficult_score:
                    best_difficult_score = total_difficult
                    best_threshold = conf_threshold
            
            print(f"\n推荐置信度阈值: {best_threshold} (困难类别检测数: {best_difficult_score:.1f})")
            
        return results
        
    def generate_optimized_inference_config(self, results):
        """生成优化的推理配置"""
        config = {
            "inference_optimization": {
                "description": "基于置信度阈值优化的推理配置",
                "models": {}
            }
        }
        
        for model_name, model_results in results.items():
            # 找到最优阈值
            best_threshold = None
            best_score = 0
            
            for conf_threshold, result in model_results.items():
                total_difficult = result['total_difficult']
                if total_difficult > best_score:
                    best_score = total_difficult
                    best_threshold = conf_threshold
            
            config["inference_optimization"]["models"][model_name] = {
                "optimal_confidence_threshold": best_threshold,
                "expected_difficult_detections": best_score,
                "model_path": self.get_model_path(model_name)
            }
        
        # 保存配置
        config_file = "optimized_inference_config.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
            
        print(f"\n优化配置已保存: {config_file}")
        return config
        
    def get_model_path(self, model_name):
        """获取模型路径"""
        model_paths = {
            "基线_YOLOv11n": "competition_2025_models/competition_2025_yolo11n/weights/best.pt",
            "优化_YOLOv11n": "optimized_models/class_weighted_yolo11n_thin_small/weights/best.pt",
        }
        return model_paths.get(model_name, "")
        
    def test_with_optimal_thresholds(self, config):
        """使用优化阈值进行测试"""
        print(f"\n{'='*80}")
        print("使用优化阈值的测试结果")
        print(f"{'='*80}")
        
        test_images = list(self.test_image_dir.glob("*.jpg"))[:10]
        
        for model_name, model_config in config["inference_optimization"]["models"].items():
            if model_name not in self.models:
                continue
                
            optimal_threshold = model_config["optimal_confidence_threshold"]
            model = self.models[model_name]
            
            print(f"\n模型: {model_name} (置信度阈值: {optimal_threshold})")
            
            total_detections = 0
            difficult_detections = {cat: 0 for cat in self.difficult_categories}
            
            for i, image_path in enumerate(test_images, 1):
                image = cv2.imread(str(image_path))
                if image is None:
                    continue
                    
                image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                
                # 使用优化阈值进行推理
                model_results = model(image_rgb, conf=optimal_threshold, verbose=False)
                
                # 解析结果
                image_detections = 0
                image_difficult = {cat: 0 for cat in self.difficult_categories}
                
                if model_results[0].boxes is not None:
                    classes = model_results[0].boxes.cls.cpu().numpy().astype(int)
                    
                    for cls in classes:
                        image_detections += 1
                        total_detections += 1
                        category_name = self.category_mapping.get(cls, 'Unknown')
                        if category_name in self.difficult_categories:
                            difficult_detections[category_name] += 1
                            image_difficult[category_name] += 1
                
                print(f"  图像{i}: {image_detections}个物体, "
                      f"牙刷:{image_difficult['CA002_牙刷']}, "
                      f"果冻:{image_difficult['CB001_果冻']}")
            
            avg_total = total_detections / len(test_images)
            avg_difficult = {cat: count / len(test_images) 
                           for cat, count in difficult_detections.items()}
            
            print(f"\n平均结果:")
            print(f"  总检测数: {avg_total:.1f}")
            print(f"  牙刷检测: {avg_difficult['CA002_牙刷']:.1f}")
            print(f"  果冻检测: {avg_difficult['CB001_果冻']:.1f}")

def main():
    """主函数"""
    print("=== 置信度阈值优化 ===")
    
    # 创建优化器
    optimizer = ConfidenceThresholdOptimizer()
    
    # 加载模型
    optimizer.load_models()
    
    if not optimizer.models:
        print("没有可用的模型")
        return
    
    # 测试不同置信度阈值
    print("\n开始测试不同置信度阈值...")
    results = optimizer.test_confidence_thresholds()
    
    if results:
        # 找到最优阈值
        optimizer.find_optimal_thresholds(results)
        
        # 生成优化配置
        config = optimizer.generate_optimized_inference_config(results)
        
        # 使用优化阈值进行测试
        optimizer.test_with_optimal_thresholds(config)
    
    print("\n=== 优化完成 ===")
    print("建议:")
    print("1. 使用优化后的置信度阈值进行推理")
    print("2. 针对困难类别可以使用更低的阈值")
    print("3. 考虑实现类别特定的置信度阈值")

if __name__ == "__main__":
    main()
