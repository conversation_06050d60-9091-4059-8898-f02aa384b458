#!/usr/bin/env python3
"""
OCR关键词映射模块
基于未知物txt文件分析结果，建立完整的OCR关键词到W001-W008类别的映射字典
实现智能文字匹配算法，支持模糊匹配和多候选词评分机制
"""

import re
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Set
from difflib import SequenceMatcher
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OCRKeywordMapper:
    """OCR关键词映射器"""
    
    def __init__(self):
        # 基于未知物txt文件分析的完整关键词映射
        self.category_keywords = {
            'W001': {
                'name': '数学类书籍',
                'keywords': [
                    '高等数学', '数学', '工程数学', '概率论与数理统计', 
                    '数理', '概率论', '数理统计', '线性代数', '微积分'
                ],
                'weight': 1.0
            },
            'W002': {
                'name': '物理类书籍',
                'keywords': [
                    '电路', '大学物理', '物理', '电磁场与电磁波', 
                    '电磁场电磁波', '电磁场', '电磁波', '力学', '光学'
                ],
                'weight': 1.0
            },
            'W003': {
                'name': '药盒类',
                'keywords': [
                    '布洛芬胶囊', '胶囊', '感冒清热颗粒', '颗粒', 
                    '金银花口服液', '口服液', '莲花清瘟胶囊', '药', '片剂'
                ],
                'weight': 1.0
            },
            'W004': {
                'name': '电子类',
                'keywords': [
                    '小米充电宝', 'xiaomi', '南孚电池', '南孚', 
                    '华为手机包装盒', '华为', '充电宝', '电池', '手机'
                ],
                'weight': 1.0
            },
            'W005': {
                'name': '英语类书籍',
                'keywords': [
                    '新世界交互英语', '英语', '雅思写作', '雅思', 
                    'IELTS', '英语字母', '雅思王阅读', 'English', 'english'
                ],
                'weight': 1.0
            },
            'W006': {
                'name': '文学类书籍',
                'keywords': [
                    '习近平新时代特色主义思想', '思想', '毛泽东思想', 
                    '毛泽东', '文学', '哲学', '政治'
                ],
                'weight': 1.0
            },
            'W007': {
                'name': '速食类',
                'keywords': [
                    '香辣牛肉面', '方便面', '茄皇牛肉面', '鲜虾鱼板面',
                    '老母鸡汤面', '红烧牛肉面', '牛肉面', '速食', '泡面'
                ],
                'weight': 1.0
            },
            'W008': {
                'name': '文具类',
                'keywords': [
                    '订书钉', '墨水', 'ink', '修正带', '笔芯', 
                    '文具', '笔', '橡皮', '尺子'
                ],
                'weight': 1.0
            }
        }
        
        # 构建反向映射：关键词 -> 类别
        self.keyword_to_category = {}
        self._build_keyword_mapping()
        
        # 模糊匹配阈值
        self.fuzzy_threshold = 0.6
        self.min_confidence = 0.4  # 提高最低置信度阈值
        
    def _build_keyword_mapping(self):
        """构建关键词到类别的反向映射"""
        for category, info in self.category_keywords.items():
            for keyword in info['keywords']:
                if keyword not in self.keyword_to_category:
                    self.keyword_to_category[keyword] = []
                self.keyword_to_category[keyword].append(category)
    
    def preprocess_text(self, text: str) -> List[str]:
        """预处理OCR识别的文本"""
        if not text:
            return []
        
        # 清理文本
        text = text.strip()
        text = re.sub(r'\s+', ' ', text)  # 合并多个空格
        
        # 分词处理
        words = []

        # 简单的中文分词（按字符分割）
        chinese_chars = re.findall(r'[\u4e00-\u9fff]+', text)
        for chars in chinese_chars:
            # 对于中文，我们保留完整的词组和单个字符
            words.append(chars)
            if len(chars) > 1:
                words.extend(list(chars))

        # 英文单词提取
        english_words = re.findall(r'[a-zA-Z]+', text)
        words.extend(english_words)

        # 数字和特殊字符组合
        special_patterns = re.findall(r'[a-zA-Z0-9]+', text)
        words.extend(special_patterns)

        # 去重并过滤短词
        words = list(set([w for w in words if len(w) > 0]))
        
        return words
    
    def calculate_similarity(self, text1: str, text2: str) -> float:
        """计算两个文本的相似度"""
        return SequenceMatcher(None, text1.lower(), text2.lower()).ratio()
    
    def exact_match(self, words: List[str]) -> Dict[str, float]:
        """精确匹配"""
        matches = {}
        
        for word in words:
            if word in self.keyword_to_category:
                for category in self.keyword_to_category[word]:
                    if category not in matches:
                        matches[category] = 0
                    matches[category] += 1.0  # 精确匹配得分最高
        
        return matches
    
    def fuzzy_match(self, words: List[str]) -> Dict[str, float]:
        """模糊匹配"""
        matches = {}
        
        for word in words:
            for category, info in self.category_keywords.items():
                for keyword in info['keywords']:
                    similarity = self.calculate_similarity(word, keyword)
                    
                    if similarity >= self.fuzzy_threshold:
                        if category not in matches:
                            matches[category] = 0
                        # 模糊匹配得分根据相似度调整
                        matches[category] += similarity * 0.8
        
        return matches
    
    def partial_match(self, text: str) -> Dict[str, float]:
        """部分匹配（子串匹配）"""
        matches = {}
        text_lower = text.lower()
        
        for category, info in self.category_keywords.items():
            for keyword in info['keywords']:
                keyword_lower = keyword.lower()
                
                # 检查关键词是否为文本的子串，或文本是否为关键词的子串
                if keyword_lower in text_lower or text_lower in keyword_lower:
                    if category not in matches:
                        matches[category] = 0
                    # 部分匹配得分中等
                    matches[category] += 0.6
        
        return matches
    
    def calculate_category_scores(self, text: str) -> Dict[str, float]:
        """计算各类别的综合得分"""
        if not text:
            return {}
        
        # 预处理文本
        words = self.preprocess_text(text)
        
        # 多种匹配策略
        exact_scores = self.exact_match(words)
        fuzzy_scores = self.fuzzy_match(words)
        partial_scores = self.partial_match(text)
        
        # 合并得分
        all_categories = set(exact_scores.keys()) | set(fuzzy_scores.keys()) | set(partial_scores.keys())
        final_scores = {}
        
        for category in all_categories:
            score = 0
            score += exact_scores.get(category, 0) * 1.0    # 精确匹配权重最高
            score += fuzzy_scores.get(category, 0) * 0.8    # 模糊匹配权重中等
            score += partial_scores.get(category, 0) * 0.6  # 部分匹配权重较低
            
            # 应用类别权重
            category_weight = self.category_keywords[category]['weight']
            final_scores[category] = score * category_weight
        
        return final_scores
    
    def classify_text(self, text: str, return_confidence: bool = True) -> Tuple[Optional[str], float]:
        """分类单个文本"""
        scores = self.calculate_category_scores(text)
        
        if not scores:
            return None, 0.0
        
        # 找到最高得分的类别
        best_category = max(scores.keys(), key=lambda k: scores[k])
        best_score = scores[best_category]
        
        # 计算置信度（归一化得分）
        total_score = sum(scores.values())
        confidence = best_score / total_score if total_score > 0 else 0.0
        
        # 检查最低置信度阈值
        if confidence < self.min_confidence:
            return None, confidence
        
        if return_confidence:
            return best_category, confidence
        else:
            return best_category, best_score
    
    def classify_multiple_texts(self, texts: List[str]) -> List[Tuple[Optional[str], float]]:
        """批量分类多个文本"""
        results = []
        for text in texts:
            result = self.classify_text(text)
            results.append(result)
        return results
    
    def get_category_info(self, category: str) -> Dict:
        """获取类别信息"""
        return self.category_keywords.get(category, {})
    
    def get_all_categories(self) -> List[str]:
        """获取所有类别"""
        return list(self.category_keywords.keys())
    
    def save_mapping_config(self, config_path: str):
        """保存映射配置到文件"""
        config_dir = Path(config_path).parent
        config_dir.mkdir(parents=True, exist_ok=True)
        
        config_data = {
            'category_keywords': self.category_keywords,
            'fuzzy_threshold': self.fuzzy_threshold,
            'min_confidence': self.min_confidence,
            'version': '1.0',
            'description': 'OCR关键词映射配置文件，支持W001-W008类别识别'
        }
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"映射配置已保存到: {config_path}")
    
    def load_mapping_config(self, config_path: str):
        """从文件加载映射配置"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        self.category_keywords = config_data['category_keywords']
        self.fuzzy_threshold = config_data.get('fuzzy_threshold', 0.6)
        self.min_confidence = config_data.get('min_confidence', 0.3)
        
        # 重建反向映射
        self.keyword_to_category = {}
        self._build_keyword_mapping()
        
        logger.info(f"映射配置已从 {config_path} 加载")

def create_default_mapper() -> OCRKeywordMapper:
    """创建默认的OCR关键词映射器"""
    return OCRKeywordMapper()

def test_ocr_classifier():
    """测试OCR分类器"""
    mapper = create_default_mapper()
    
    # 测试用例
    test_cases = [
        "高等数学",
        "大学物理",
        "布洛芬胶囊",
        "xiaomi充电宝",
        "雅思写作",
        "毛泽东思想",
        "方便面",
        "墨水笔芯",
        "不相关的文本",
        "数学物理混合文本"
    ]
    
    print("🧪 OCR分类器测试")
    print("="*50)
    
    for text in test_cases:
        category, confidence = mapper.classify_text(text)
        
        if category:
            category_name = mapper.get_category_info(category).get('name', category)
            print(f"文本: '{text}' -> {category} ({category_name}) [置信度: {confidence:.3f}]")
        else:
            print(f"文本: '{text}' -> 未分类 [置信度: {confidence:.3f}]")
    
    print("="*50)
    print(f"测试完成")

if __name__ == "__main__":
    # 创建映射器
    mapper = create_default_mapper()
    
    # 保存配置文件
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)
    mapper.save_mapping_config("config/ocr_categories.json")
    
    # 运行测试
    test_ocr_classifier()
    
    print("\n✅ OCR关键词映射模块创建完成！")
