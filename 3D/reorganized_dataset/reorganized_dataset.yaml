# 重组数据集配置文件
# 基于 /home/<USER>/claude/SpatialVLA/3D/111 中的数据重新组织

# 数据集路径
path: /home/<USER>/claude/SpatialVLA/3D/reorganized_dataset

# 训练、验证、测试集路径
train: images/train
val: images/val  # 目前为空，可根据需要从train中分割
test: images/test  # 目前为空，可根据需要从train中分割

# 深度图像路径
depth_train: depth/train
depth_val: depth/val
depth_test: depth/test

# 类别数量
nc: 12

# 类别名称（基于合并的COCO标注文件）
names:
  0: 'class_0'
  1: 'class_1'
  2: 'class_2'
  3: 'class_3'
  4: 'class_4'
  5: 'class_5'
  6: 'class_6'
  7: 'class_7'
  8: 'class_8'
  9: 'class_9'
  10: 'class_10'
  11: 'class_11'

# 数据集统计信息
dataset_info:
  total_images: 873
  total_depth_images: 740
  total_annotations: 8170
  
  # 数据来源分布
  source_distribution:
    d1: 154  # d1目录的图像数量
    d2: 200  # d2目录的图像数量（估算）
    d3: 124  # d3目录的图像数量
    d4: 200  # d4目录的图像数量（估算）
    dataset: 195  # dataset目录的图像数量

  # 文件命名规则
  naming_convention:
    rgb_images: "{source}_{original_id}.jpg"
    depth_images: "{source}_{original_id}_depth.png"
    
  # 原始数据路径
  original_paths:
    - "/home/<USER>/claude/SpatialVLA/3D/111/d1/images（rgb+深度）"
    - "/home/<USER>/claude/SpatialVLA/3D/111/d2/dataset/images（rgb+深度）"
    - "/home/<USER>/claude/SpatialVLA/3D/111/d3/images"
    - "/home/<USER>/claude/SpatialVLA/3D/111/d4/dataset/images（rgb+深度）"
    - "/home/<USER>/claude/SpatialVLA/3D/111/dataset/images"
    - "/home/<USER>/claude/SpatialVLA/3D/111/dataset/deep"

# 标注文件
annotations:
  train: labels/train/annotations.json
  val: labels/val/  # 待创建
  test: labels/test/  # 待创建
  format: "coco"

# 数据增强建议
augmentation:
  rgb:
    - "horizontal_flip"
    - "rotation"
    - "brightness_contrast"
    - "gaussian_noise"
  
  depth:
    - "horizontal_flip"  # 与RGB保持一致
    - "rotation"  # 与RGB保持一致
    - "depth_normalization"

# 使用说明
usage_notes: |
  1. 当前所有数据都在train目录中，建议根据需要分割为train/val/test
  2. 深度图像数量(740)少于RGB图像数量(873)，部分RGB图像可能没有对应的深度图像
  3. 标注文件已更新以匹配重命名后的文件名
  4. 73个标注条目未找到对应的图像文件，可能需要进一步清理
  5. 建议在使用前验证图像和标注的对应关系

# 兼容性
compatible_with:
  - "YOLOv8"
  - "EfficientDet"
  - "RGB-D fusion models"
  
# 创建时间
created: "2025-01-12"
version: "1.0"
