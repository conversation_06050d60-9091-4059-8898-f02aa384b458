#!/usr/bin/env python3
"""
使用更大的YOLOv11s模型进行优化训练
基于之前成功的优化策略
"""

import os
import sys
from pathlib import Path

# 添加当前目录到路径
sys.path.append(str(Path(__file__).parent))

from optimized_training_for_thin_small_objects import OptimizedTrainerForThinSmallObjects

def main():
    """使用YOLOv11s进行优化训练"""
    print("=== YOLOv11s 优化训练 ===")
    print("基于成功的优化策略，使用更大的模型进一步提升性能")
    
    # 检查数据集
    dataset_path = "competition_2025_dataset"
    if not Path(dataset_path).exists():
        print(f"错误: 数据集不存在: {dataset_path}")
        return
    
    # 创建优化训练器
    trainer = OptimizedTrainerForThinSmallObjects(dataset_path, output_dir="optimized_models_s")
    
    # 使用YOLOv11s进行训练
    print("开始YOLOv11s优化训练...")
    print("预期训练时间: 约1-2小时")
    
    result = trainer.train_optimized_model("yolo11s", use_class_weights=True)
    
    if result:
        print(f"\n🎉 YOLOv11s优化训练完成!")
        print(f"mAP50: {result['metrics']['mAP50']:.4f}")
        print(f"mAP50-95: {result['metrics']['mAP50_95']:.4f}")
        
        # 困难类别性能
        if result.get("class_metrics"):
            print(f"\n困难类别性能:")
            ca002_score = result["class_metrics"].get('CA002_牙刷', 0)
            cb001_score = result["class_metrics"].get('CB001_果冻', 0)
            print(f"CA002_牙刷: {ca002_score:.4f}")
            print(f"CB001_果冻: {cb001_score:.4f}")
            
            # 与YOLOv11n对比
            print(f"\n与YOLOv11n优化版对比:")
            print(f"CA002_牙刷: {ca002_score:.4f} vs 0.4235 (YOLOv11n)")
            print(f"CB001_果冻: {cb001_score:.4f} vs 0.3317 (YOLOv11n)")
        
        print(f"\n模型保存位置: {result['model_path']}")
        
        # 性能建议
        print(f"\n📋 性能分析:")
        if result['metrics']['mAP50'] > 0.95:
            print("✅ 优秀! mAP50超过95%")
        elif result['metrics']['mAP50'] > 0.94:
            print("✅ 很好! mAP50超过94%")
        else:
            print("⚠️  可以进一步优化")
            
        print(f"\n🚀 下一步建议:")
        print(f"1. 如果性能满意，可以部署到比赛环境")
        print(f"2. 考虑使用TTA(Test Time Augmentation)进一步提升")
        print(f"3. 在目标硬件(香橙派)上测试性能")
        print(f"4. 实现完整的比赛推理系统")
        
    else:
        print("❌ YOLOv11s训练失败")

if __name__ == "__main__":
    main()
