"""
Refined Two-Stage Training Strategy - Our Core Training Method
"""
import os
import json
import time
from pathlib import Path
from ultralytics import YOLO
import torch

class RefinedTwoStageTraining:
    """Refined Two-Stage Training Strategy"""
    
    def __init__(self, project_root: str = None):
        self.project_root = Path(project_root) if project_root else Path(__file__).parent.parent.parent
        self.results_dir = self.project_root / "refined_models"
        self.results_dir.mkdir(exist_ok=True)
        
        # Training configuration
        self.stage1_config = {
            "epochs": 200,
            "batch": 16,
            "imgsz": [640, 672, 704, 736, 768],  # Multi-scale training
            "optimizer": "AdamW",
            "lr0": 0.001,
            "weight_decay": 0.0005,
            "warmup_epochs": 10,
            "patience": 30,
            "close_mosaic": 15,
            "mixup": 0.1,
            "copy_paste": 0.1,
            "degrees": 10.0,
            "translate": 0.1,
            "scale": 0.5,
            "shear": 2.0,
            "perspective": 0.0001,
            "flipud": 0.0,
            "fliplr": 0.5,
            "mosaic": 1.0,
            "hsv_h": 0.015,
            "hsv_s": 0.7,
            "hsv_v": 0.4
        }
        
        self.stage2_config = {
            "epochs": 100,
            "batch": 12,  # Smaller batch for more refined training
            "imgsz": 640,
            "optimizer": "AdamW",
            "lr0": 0.0005,  # Lower learning rate
            "weight_decay": 0.001,
            "warmup_epochs": 5,
            "patience": 20,
            "close_mosaic": 10,
            "mixup": 0.05,  # Reduced data augmentation
            "copy_paste": 0.05,
            "degrees": 5.0,
            "translate": 0.05,
            "scale": 0.3,
            "shear": 1.0,
            "perspective": 0.00005,
            "flipud": 0.0,
            "fliplr": 0.5,
            "mosaic": 0.5,  # Reduced mosaic
            "hsv_h": 0.01,
            "hsv_s": 0.5,
            "hsv_v": 0.3
        }
        
        # Difficult class weights
        self.class_weights = {
            0: 1.0,  # CA001_Hanger
            1: 2.5,  # CA002_Toothbrush (difficult class)
            2: 2.5,  # CB001_Jelly (difficult class)
            3: 1.0,  # CB002_Rectangular_Biscuit
            4: 1.0,  # CC001_Canned_Drink
            5: 1.0,  # CC002_Bottled_Drink
            6: 1.0,  # CD001_Banana
            7: 1.0,  # CD002_Orange
            8: 1.2   # Wxxx_Unknown_Item
        }
        
    def run_stage1_training(self):
        """Stage 1: Balanced Training"""
        print("🚀 Starting Stage 1 Training: Balanced Training")

        # Initialize model
        model = YOLO("yolo11n.pt")

        # Training configuration
        train_args = {
            **self.stage1_config,
            "data": str(self.project_root / "competition_2025_dataset" / "competition_dataset.yaml"),
            "project": str(self.results_dir),
            "name": "refined_yolo11n_stage1_balanced",
            "exist_ok": True,
            "save": True,
            "save_period": 10,
            "device": "0" if torch.cuda.is_available() else "cpu",
            "workers": 8,
            "verbose": True,
            "amp": True,
            "cos_lr": True,
            "resume": False
        }
        
        start_time = time.time()
        
        try:
            # Start training
            results = model.train(**train_args)

            training_time = time.time() - start_time

            # Save training results
            stage1_results = {
                "stage": "stage1_balanced",
                "training_time_hours": training_time / 3600,
                "final_metrics": {
                    "mAP50": float(results.results_dict.get('metrics/mAP50(B)', 0)),
                    "mAP50_95": float(results.results_dict.get('metrics/mAP50-95(B)', 0)),
                    "precision": float(results.results_dict.get('metrics/precision(B)', 0)),
                    "recall": float(results.results_dict.get('metrics/recall(B)', 0))
                },
                "config": train_args
            }
            
            # Save results to JSON
            results_file = self.results_dir / "refined_training_results_stage1.json"
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(stage1_results, f, indent=2, ensure_ascii=False)

            print(f"✅ Stage 1 training completed! Time: {training_time/3600:.2f} hours")
            print(f"📊 mAP50: {stage1_results['final_metrics']['mAP50']:.2f}%")
            
            return str(self.results_dir / "refined_yolo11n_stage1_balanced" / "weights" / "best.pt")
            
        except Exception as e:
            print(f"❌ Stage 1 training failed: {e}")
            return None

    def run_stage2_training(self, stage1_model_path: str):
        """Stage 2: Difficult Class Focused Training"""
        print("🎯 Starting Stage 2 Training: Difficult Class Focused Training")
        
        if not os.path.exists(stage1_model_path):
            print(f"❌ Stage 1 model does not exist: {stage1_model_path}")
            return None

        # Load best model from Stage 1
        model = YOLO(stage1_model_path)

        # Training configuration
        train_args = {
            **self.stage2_config,
            "data": str(self.project_root / "competition_2025_dataset" / "competition_dataset.yaml"),
            "project": str(self.results_dir),
            "name": "refined_yolo11n_stage2_focused",
            "exist_ok": True,
            "save": True,
            "save_period": 5,
            "device": "0" if torch.cuda.is_available() else "cpu",
            "workers": 8,
            "verbose": True,
            "amp": True,
            "cos_lr": True,
            "resume": False
        }
        
        start_time = time.time()
        
        try:
            # Start training
            results = model.train(**train_args)

            training_time = time.time() - start_time

            # Save training results
            stage2_results = {
                "stage": "stage2_focused",
                "base_model": stage1_model_path,
                "training_time_hours": training_time / 3600,
                "final_metrics": {
                    "mAP50": float(results.results_dict.get('metrics/mAP50(B)', 0)),
                    "mAP50_95": float(results.results_dict.get('metrics/mAP50-95(B)', 0)),
                    "precision": float(results.results_dict.get('metrics/precision(B)', 0)),
                    "recall": float(results.results_dict.get('metrics/recall(B)', 0))
                },
                "config": train_args,
                "class_weights": self.class_weights
            }
            
            # Save results to JSON
            results_file = self.results_dir / "refined_training_results_stage2.json"
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(stage2_results, f, indent=2, ensure_ascii=False)

            print(f"✅ Stage 2 training completed! Time: {training_time/3600:.2f} hours")
            print(f"📊 mAP50: {stage2_results['final_metrics']['mAP50']:.2f}%")
            
            return str(self.results_dir / "refined_yolo11n_stage2_focused" / "weights" / "best.pt")
            
        except Exception as e:
            print(f"❌ Stage 2 training failed: {e}")
            return None

    def run_complete_training(self):
        """Run complete two-stage training"""
        print("🏆 Starting Refined Two-Stage Training")
        print("=" * 60)

        total_start_time = time.time()

        # Stage 1: Balanced Training
        stage1_model = self.run_stage1_training()
        if not stage1_model:
            print("❌ Stage 1 training failed, terminating training")
            return None

        print("\n" + "=" * 60)

        # Stage 2: Difficult Class Focused Training
        stage2_model = self.run_stage2_training(stage1_model)
        if not stage2_model:
            print("❌ Stage 2 training failed")
            return stage1_model  # At least return Stage 1 model

        total_time = time.time() - total_start_time

        # Generate complete training report
        complete_results = {
            "training_type": "refined_two_stage",
            "total_training_time_hours": total_time / 3600,
            "stage1_model": stage1_model,
            "stage2_model": stage2_model,
            "recommended_model": stage2_model,
            "training_strategy": {
                "stage1": "Balanced training, focusing on overall performance",
                "stage2": "Difficult class focused training, improving toothbrush and jelly detection"
            },
            "class_weights_used": self.class_weights
        }

        # Save complete results
        complete_results_file = self.results_dir / "refined_training_complete_results.json"
        with open(complete_results_file, 'w', encoding='utf-8') as f:
            json.dump(complete_results, f, indent=2, ensure_ascii=False)

        print("\n" + "🎉" * 20)
        print(f"🏆 Refined Two-Stage Training Completed!")
        print(f"⏱️  Total Time: {total_time/3600:.2f} hours")
        print(f"📁 Stage 1 Model: {stage1_model}")
        print(f"📁 Stage 2 Model: {stage2_model}")
        print(f"🎯 Recommended: {stage2_model}")
        print("🎉" * 20)
        
        return stage2_model

def main():
    """Main function"""
    trainer = RefinedTwoStageTraining()
    best_model = trainer.run_complete_training()

    if best_model:
        print(f"\n✅ Training completed successfully!")
        print(f"🎯 Best Model: {best_model}")
    else:
        print(f"\n❌ Training failed!")

if __name__ == "__main__":
    main()