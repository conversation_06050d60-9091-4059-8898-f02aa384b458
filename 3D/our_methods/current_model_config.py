"""
当前模型配置 - 我们的方法核心配置
"""
import os
from pathlib import Path
from typing import Dict, Optional, Tuple

class CurrentModelConfig:
    """当前使用的模型配置管理"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        
        # 当前最佳模型配置（按优先级排序）
        self.model_priority = [
            "精细化_stage2",
            "精细化_stage1", 
            "优化_YOLOv11s",
            "优化_YOLOv11n",
            "基线_YOLOv11n"
        ]
        
        # 模型路径映射
        self.model_paths = {
            "精细化_stage2": "refined_models/refined_yolo11n_stage2_focused/weights/best.pt",
            "精细化_stage1": "refined_models/refined_yolo11n_stage1_balanced/weights/best.pt",
            "优化_YOLOv11s": "optimized_models_s/class_weighted_yolo11s_thin_small/weights/best.pt",
            "优化_YOLOv11n": "optimized_models/class_weighted_yolo11n_thin_small/weights/best.pt",
            "基线_YOLOv11n": "competition_2025_models/competition_2025_yolo11n/weights/best.pt"
        }
        
        # 模型性能数据
        self.model_performance = {
            "精细化_stage1": {
                "mAP50": 95.54,
                "mAP50_95": 68.18,
                "CA002_牙刷": 36.67,
                "CB001_果冻": 33.04,
                "inference_time_ms": 10,
                "loading_time_s": 0.037
            },
            "优化_YOLOv11s": {
                "mAP50": 92.13,
                "mAP50_95": 63.43,
                "CA002_牙刷": 43.47,
                "CB001_果冻": 37.64,
                "inference_time_ms": 15,
                "loading_time_s": 0.095
            },
            "优化_YOLOv11n": {
                "mAP50": 94.47,
                "mAP50_95": 69.14,
                "CA002_牙刷": 42.35,
                "CB001_果冻": 33.17,
                "inference_time_ms": 8,
                "loading_time_s": 0.037
            }
        }
        
        # 类别特定配置
        self.category_config = {
            'CA001': {'conf': 0.4, 'nms': 0.5, 'min_area': 500},
            'CA002': {'conf': 0.25, 'nms': 0.3, 'min_area': 200, 'aspect_ratio': (3, 15)},  # 牙刷
            'CB001': {'conf': 0.3, 'nms': 0.4, 'min_area': 100, 'max_area': 2000},  # 果冻
            'CB002': {'conf': 0.4, 'nms': 0.5, 'min_area': 300},
            'CC001': {'conf': 0.5, 'nms': 0.5, 'min_area': 800},
            'CC002': {'conf': 0.5, 'nms': 0.5, 'min_area': 600},
            'CD001': {'conf': 0.5, 'nms': 0.5, 'min_area': 400},
            'CD002': {'conf': 0.5, 'nms': 0.5, 'min_area': 400},
            'Wxxx': {'conf': 0.35, 'nms': 0.4, 'min_area': 200}
        }
        
    def get_best_available_model(self) -> Tuple[str, str]:
        """
        获取当前可用的最佳模型
        
        Returns:
            Tuple[str, str]: (模型名称, 模型路径)
        """
        for model_name in self.model_priority:
            model_path = self.project_root / self.model_paths[model_name]
            if model_path.exists():
                return model_name, str(model_path)
        
        # 如果没有找到任何模型，返回预训练模型
        return "预训练_YOLOv11n", "yolo11n.pt"
    
    def get_model_for_scenario(self, scenario: str) -> Tuple[str, str]:
        """
        根据使用场景获取推荐模型
        
        Args:
            scenario: 'accuracy' | 'difficult_categories' | 'balanced' | 'speed'
            
        Returns:
            Tuple[str, str]: (模型名称, 模型路径)
        """
        scenario_mapping = {
            'accuracy': '精细化_stage1',
            'difficult_categories': '优化_YOLOv11s', 
            'balanced': '优化_YOLOv11n',
            'speed': '优化_YOLOv11n'
        }
        
        model_name = scenario_mapping.get(scenario, '精细化_stage1')
        model_path = self.project_root / self.model_paths[model_name]
        
        if model_path.exists():
            return model_name, str(model_path)
        else:
            # 回退到可用的最佳模型
            return self.get_best_available_model()
    
    def get_model_performance(self, model_name: str) -> Dict:
        """获取模型性能数据"""
        return self.model_performance.get(model_name, {})
    
    def get_category_config(self, category: str) -> Dict:
        """获取类别特定配置"""
        return self.category_config.get(category, {'conf': 0.5, 'nms': 0.5, 'min_area': 300})

# 便捷函数
def get_best_model() -> str:
    """获取当前最佳可用模型路径"""
    config = CurrentModelConfig()
    _, model_path = config.get_best_available_model()
    return model_path

def get_model_for_accuracy() -> str:
    """获取最高精度模型"""
    config = CurrentModelConfig()
    _, model_path = config.get_model_for_scenario('accuracy')
    return model_path

def get_model_for_difficult_categories() -> str:
    """获取困难类别专用模型"""
    config = CurrentModelConfig()
    _, model_path = config.get_model_for_scenario('difficult_categories')
    return model_path

def get_current_model_status():
    """获取当前模型状态报告"""
    config = CurrentModelConfig()
    
    print("🎯 当前模型状态报告")
    print("=" * 50)
    
    for i, model_name in enumerate(config.model_priority, 1):
        model_path = config.project_root / config.model_paths[model_name]
        status = "✅ 可用" if model_path.exists() else "❌ 不存在"
        
        print(f"{i}. {model_name}: {status}")
        if model_path.exists():
            perf = config.get_model_performance(model_name)
            if perf:
                print(f"   - mAP50: {perf.get('mAP50', 'N/A')}%")
                print(f"   - 推理时间: {perf.get('inference_time_ms', 'N/A')}ms")
    
    best_name, best_path = config.get_best_available_model()
    print(f"\n🏆 当前最佳模型: {best_name}")
    print(f"📁 模型路径: {best_path}")

if __name__ == "__main__":
    get_current_model_status()