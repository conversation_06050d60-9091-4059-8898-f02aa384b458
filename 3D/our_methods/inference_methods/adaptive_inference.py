"""
自适应推理系统 - 我们的核心推理方法
"""
import cv2
import numpy as np
import time
from pathlib import Path
from ultralytics import YOLO
from typing import List, Dict, Tuple, Optional
import json

class AdaptiveInferenceSystem:
    """自适应推理系统"""
    
    def __init__(self, model_path: str = None):
        self.model = None
        self.model_path = model_path
        self.load_time = 0
        
        # 类别映射
        self.category_mapping = {
            0: 'CA001', 1: 'CA002', 2: 'CB001', 3: 'CB002', 4: 'CC001',
            5: 'CC002', 6: 'CD001', 7: 'CD002', 8: 'Wxxx'
        }
        
        # 类别特定的自适应配置
        self.adaptive_config = {
            'CA001': {  # 衣架
                'confidence_threshold': 0.4,
                'nms_threshold': 0.5,
                'min_area': 500,
                'max_area': 50000,
                'aspect_ratio_range': (0.3, 3.0),
                'post_process': 'standard'
            },
            'CA002': {  # 牙刷 (困难类别)
                'confidence_threshold': 0.25,  # 更低的阈值
                'nms_threshold': 0.3,
                'min_area': 200,
                'max_area': 5000,
                'aspect_ratio_range': (3.0, 15.0),  # 长条形
                'post_process': 'thin_object_enhancement'
            },
            'CB001': {  # 果冻 (困难类别)
                'confidence_threshold': 0.3,
                'nms_threshold': 0.4,
                'min_area': 100,
                'max_area': 2000,
                'aspect_ratio_range': (0.5, 2.0),  # 相对方形
                'post_process': 'small_object_enhancement'
            },
            'CB002': {  # 长方形状饼干
                'confidence_threshold': 0.4,
                'nms_threshold': 0.5,
                'min_area': 300,
                'max_area': 10000,
                'aspect_ratio_range': (1.2, 4.0),
                'post_process': 'standard'
            },
            'CC001': {  # 罐装饮料
                'confidence_threshold': 0.5,
                'nms_threshold': 0.5,
                'min_area': 800,
                'max_area': 20000,
                'aspect_ratio_range': (0.8, 1.5),
                'post_process': 'standard'
            },
            'CC002': {  # 瓶装饮料
                'confidence_threshold': 0.5,
                'nms_threshold': 0.5,
                'min_area': 600,
                'max_area': 15000,
                'aspect_ratio_range': (2.0, 5.0),
                'post_process': 'standard'
            },
            'CD001': {  # 香蕉
                'confidence_threshold': 0.5,
                'nms_threshold': 0.5,
                'min_area': 400,
                'max_area': 8000,
                'aspect_ratio_range': (2.0, 8.0),
                'post_process': 'standard'
            },
            'CD002': {  # 橙子
                'confidence_threshold': 0.5,
                'nms_threshold': 0.5,
                'min_area': 400,
                'max_area': 6000,
                'aspect_ratio_range': (0.8, 1.3),
                'post_process': 'standard'
            },
            'Wxxx': {  # 未知物品
                'confidence_threshold': 0.35,
                'nms_threshold': 0.4,
                'min_area': 200,
                'max_area': 30000,
                'aspect_ratio_range': (0.2, 10.0),
                'post_process': 'flexible'
            }
        }
        
        # 困难类别
        self.difficult_categories = ['CA002', 'CB001']
        
        # 加载模型
        if model_path:
            self.load_model(model_path)
    
    def load_model(self, model_path: str):
        """加载模型"""
        start_time = time.time()
        
        try:
            self.model = YOLO(model_path)
            self.model_path = model_path
            self.load_time = time.time() - start_time
            
            print(f"✅ 模型加载成功: {model_path}")
            print(f"⏱️  加载时间: {self.load_time:.3f}s")
            
            # 预热模型
            self._warmup_model()
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            self.model = None
    
    def _warmup_model(self):
        """模型预热"""
        if self.model:
            dummy_image = np.zeros((640, 640, 3), dtype=np.uint8)
            for _ in range(3):
                _ = self.model(dummy_image, verbose=False)
            print("🔥 模型预热完成")
    
    def predict(self, image_path: str, save_result: bool = False) -> Dict:
        """
        自适应推理预测
        
        Args:
            image_path: 图像路径
            save_result: 是否保存结果图像
            
        Returns:
            Dict: 预测结果
        """
        if not self.model:
            raise RuntimeError("模型未加载")
        
        start_time = time.time()
        
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        # 运行推理
        results = self.model(image, verbose=False)
        
        # 自适应后处理
        processed_detections = self._adaptive_post_process(results[0], image)
        
        inference_time = time.time() - start_time
        
        # 格式化结果
        formatted_result = {
            "image_path": image_path,
            "inference_time": inference_time,
            "detections": processed_detections,
            "detection_count": len(processed_detections),
            "model_used": self.model_path
        }
        
        # 保存结果图像
        if save_result:
            self._save_result_image(image, processed_detections, image_path)
        
        return formatted_result
    
    def _adaptive_post_process(self, result, image: np.ndarray) -> List[Dict]:
        """自适应后处理"""
        detections = []
        
        if result.boxes is not None:
            boxes = result.boxes.xyxy.cpu().numpy()
            confidences = result.boxes.conf.cpu().numpy()
            class_ids = result.boxes.cls.cpu().numpy().astype(int)
            
            for i, (box, conf, cls_id) in enumerate(zip(boxes, confidences, class_ids)):
                category = self.category_mapping.get(cls_id, 'Unknown')
                config = self.adaptive_config.get(category, {})
                
                # 应用类别特定的置信度阈值
                conf_threshold = config.get('confidence_threshold', 0.5)
                if conf < conf_threshold:
                    continue
                
                # 计算边界框属性
                x1, y1, x2, y2 = box
                width = x2 - x1
                height = y2 - y1
                area = width * height
                aspect_ratio = width / height if height > 0 else 0
                
                # 应用面积过滤
                min_area = config.get('min_area', 100)
                max_area = config.get('max_area', 100000)
                if area < min_area or area > max_area:
                    continue
                
                # 应用长宽比过滤
                aspect_range = config.get('aspect_ratio_range', (0.1, 10.0))
                if not (aspect_range[0] <= aspect_ratio <= aspect_range[1]):
                    continue
                
                # 应用特定后处理
                post_process_type = config.get('post_process', 'standard')
                if not self._apply_post_process(box, conf, cls_id, post_process_type, image):
                    continue
                
                # 添加检测结果
                detection = {
                    "category": category,
                    "confidence": float(conf),
                    "bbox": [float(x1), float(y1), float(x2), float(y2)],
                    "area": float(area),
                    "aspect_ratio": float(aspect_ratio),
                    "class_id": int(cls_id)
                }
                
                detections.append(detection)
        
        return detections
    
    def _apply_post_process(self, box, conf, cls_id, post_process_type: str, image: np.ndarray) -> bool:
        """应用特定的后处理逻辑"""
        
        if post_process_type == 'thin_object_enhancement':
            # 针对牙刷等细长物体的增强处理
            x1, y1, x2, y2 = box
            width = x2 - x1
            height = y2 - y1
            aspect_ratio = width / height if height > 0 else 0
            
            # 对于细长物体，放宽置信度要求
            if aspect_ratio > 5.0 and conf > 0.2:
                return True
            
        elif post_process_type == 'small_object_enhancement':
            # 针对果冻等小物体的增强处理
            x1, y1, x2, y2 = box
            area = (x2 - x1) * (y2 - y1)
            
            # 对于小物体，放宽置信度要求
            if area < 1000 and conf > 0.25:
                return True
                
        elif post_process_type == 'flexible':
            # 对未知物品采用灵活策略
            return conf > 0.3
        
        # 标准处理
        return True
    
    def _save_result_image(self, image: np.ndarray, detections: List[Dict], original_path: str):
        """保存结果图像"""
        result_image = image.copy()
        
        for detection in detections:
            x1, y1, x2, y2 = detection['bbox']
            category = detection['category']
            confidence = detection['confidence']
            
            # 绘制边界框
            color = self._get_category_color(category)
            cv2.rectangle(result_image, (int(x1), int(y1)), (int(x2), int(y2)), color, 2)
            
            # 绘制标签
            label = f"{category}: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
            cv2.rectangle(result_image, (int(x1), int(y1) - label_size[1] - 10),
                         (int(x1) + label_size[0], int(y1)), color, -1)
            cv2.putText(result_image, label, (int(x1), int(y1) - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
        
        # 保存图像
        output_path = Path(original_path).parent / f"adaptive_result_{Path(original_path).name}"
        cv2.imwrite(str(output_path), result_image)
        print(f"💾 结果图像已保存: {output_path}")
    
    def _get_category_color(self, category: str) -> Tuple[int, int, int]:
        """获取类别颜色"""
        colors = {
            'CA001': (255, 0, 0),    # 红色
            'CA002': (0, 255, 0),    # 绿色 (困难类别)
            'CB001': (0, 0, 255),    # 蓝色 (困难类别)
            'CB002': (255, 255, 0),  # 青色
            'CC001': (255, 0, 255),  # 品红
            'CC002': (0, 255, 255),  # 黄色
            'CD001': (128, 0, 128),  # 紫色
            'CD002': (255, 165, 0),  # 橙色
            'Wxxx': (128, 128, 128)  # 灰色
        }
        return colors.get(category, (255, 255, 255))
    
    def batch_predict(self, image_dir: str, output_dir: str = None) -> Dict:
        """批量预测"""
        image_dir = Path(image_dir)
        if output_dir:
            output_dir = Path(output_dir)
            output_dir.mkdir(exist_ok=True)
        
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
        image_files = []
        for ext in image_extensions:
            image_files.extend(image_dir.glob(f'*{ext}'))
            image_files.extend(image_dir.glob(f'*{ext.upper()}'))
        
        results = []
        total_detections = 0
        
        print(f"🔍 开始批量推理，共 {len(image_files)} 张图像")
        
        for i, image_file in enumerate(image_files, 1):
            try:
                result = self.predict(str(image_file), save_result=bool(output_dir))
                results.append(result)
                total_detections += result['detection_count']
                
                print(f"  [{i}/{len(image_files)}] {image_file.name}: {result['detection_count']} 个检测")
                
            except Exception as e:
                print(f"  ❌ {image_file.name}: {e}")
        
        # 生成批量结果报告
        batch_result = {
            "total_images": len(image_files),
            "successful_predictions": len(results),
            "total_detections": total_detections,
            "average_detections_per_image": total_detections / len(results) if results else 0,
            "results": results
        }
        
        # 保存批量结果
        if output_dir:
            result_file = output_dir / "adaptive_batch_results.json"
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(batch_result, f, indent=2, ensure_ascii=False)
            print(f"📊 批量结果已保存: {result_file}")
        
        return batch_result

def main():
    """测试函数"""
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from current_model_config import get_best_model

    # 获取最佳模型
    model_path = get_best_model()
    
    # 初始化自适应推理系统
    inference = AdaptiveInferenceSystem(model_path)
    
    # 测试单张图像推理
    test_image = "test_image.jpg"  # 替换为实际图像路径
    if Path(test_image).exists():
        result = inference.predict(test_image, save_result=True)
        print(f"🎯 检测结果: {result['detection_count']} 个目标")
        
        for detection in result['detections']:
            print(f"  - {detection['category']}: {detection['confidence']:.3f}")

if __name__ == "__main__":
    main()