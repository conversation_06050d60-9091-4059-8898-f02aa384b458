"""
增强版推理系统
集成TTA (Test Time Augmentation) + Soft-NMS + WBF

核心优化：
1. YOLOv5风格TTA: 水平翻转 + 3个尺度 (预期+2-3% mAP)
2. Soft-NMS: 高斯衰减替代硬阈值 (预期+0.5-1% mAP)
3. WBF集成: 加权框融合 (预期+2-4% mAP)
4. 选择性TTA: 基于置信度的智能增强

总体预期性能提升: +4-8% mAP50-95
"""
import cv2
import numpy as np
import torch
import time
from pathlib import Path
from ultralytics import YOLO
from typing import List, Dict, Tuple, Optional, Union
import json
from scipy import ndimage
from ensemble_boxes import weighted_boxes_fusion

class TestTimeAugmentation:
    """
    测试时增强 (TTA) 实现
    基于YOLOv5风格的多尺度+翻转增强
    """
    
    def __init__(self, 
                 scales=[0.83, 1.0, 1.17],  # 多尺度
                 flip_horizontal=True,       # 水平翻转
                 confidence_threshold=0.6):  # 选择性TTA阈值
        
        self.scales = scales
        self.flip_horizontal = flip_horizontal
        self.confidence_threshold = confidence_threshold
        
        print(f"🔄 TTA初始化完成")
        print(f"   尺度: {scales}")
        print(f"   水平翻转: {flip_horizontal}")
        print(f"   选择性阈值: {confidence_threshold}")
    
    def apply_tta(self, model, image: np.ndarray, selective=True) -> List[Dict]:
        """
        应用测试时增强
        
        Args:
            model: YOLO模型
            image: 输入图像
            selective: 是否使用选择性TTA
            
        Returns:
            所有增强结果的列表
        """
        h, w = image.shape[:2]
        all_predictions = []
        
        # 基础预测（用于选择性TTA判断）
        base_results = model(image, verbose=False)[0]
        base_confidence = self._get_max_confidence(base_results)
        
        # 决定是否应用TTA
        if selective and base_confidence > self.confidence_threshold:
            # 高置信度，只返回基础预测
            return [self._format_results(base_results, 1.0, False, h, w)]
        
        # 应用完整TTA
        for scale in self.scales:
            # 缩放图像
            if scale != 1.0:
                new_h, new_w = int(h * scale), int(w * scale)
                scaled_image = cv2.resize(image, (new_w, new_h))
            else:
                scaled_image = image.copy()
            
            # 原始图像预测
            results = model(scaled_image, verbose=False)[0]
            formatted_results = self._format_results(results, scale, False, h, w)
            all_predictions.append(formatted_results)
            
            # 水平翻转预测
            if self.flip_horizontal:
                flipped_image = cv2.flip(scaled_image, 1)
                flipped_results = model(flipped_image, verbose=False)[0]
                formatted_results = self._format_results(flipped_results, scale, True, h, w)
                all_predictions.append(formatted_results)
        
        return all_predictions
    
    def _get_max_confidence(self, results) -> float:
        """获取预测结果的最大置信度"""
        if results.boxes is not None and len(results.boxes) > 0:
            return float(results.boxes.conf.max())
        return 0.0
    
    def _format_results(self, results, scale: float, flipped: bool, orig_h: int, orig_w: int) -> Dict:
        """
        格式化预测结果并逆变换到原始图像坐标
        """
        detections = []
        
        if results.boxes is not None:
            boxes = results.boxes.xyxy.cpu().numpy()
            confidences = results.boxes.conf.cpu().numpy()
            class_ids = results.boxes.cls.cpu().numpy().astype(int)
            
            for box, conf, cls_id in zip(boxes, confidences, class_ids):
                x1, y1, x2, y2 = box
                
                # 逆缩放
                if scale != 1.0:
                    x1 /= scale
                    y1 /= scale
                    x2 /= scale
                    y2 /= scale
                
                # 逆翻转
                if flipped:
                    x1_new = orig_w - x2
                    x2_new = orig_w - x1
                    x1, x2 = x1_new, x2_new
                
                # 确保边界在图像内
                x1 = max(0, min(x1, orig_w))
                y1 = max(0, min(y1, orig_h))
                x2 = max(0, min(x2, orig_w))
                y2 = max(0, min(y2, orig_h))
                
                if x2 > x1 and y2 > y1:
                    detections.append({
                        'bbox': [float(x1), float(y1), float(x2), float(y2)],
                        'confidence': float(conf),
                        'class_id': int(cls_id),
                        'scale': scale,
                        'flipped': flipped
                    })
        
        return {
            'detections': detections,
            'scale': scale,
            'flipped': flipped
        }

class SoftNMS:
    """
    Soft-NMS实现
    使用高斯衰减替代硬阈值，减少误删除
    """
    
    def __init__(self, sigma=0.5, iou_threshold=0.5, score_threshold=0.001):
        self.sigma = sigma
        self.iou_threshold = iou_threshold
        self.score_threshold = score_threshold
        
        print(f"🔄 Soft-NMS初始化完成")
        print(f"   σ: {sigma}")
        print(f"   IoU阈值: {iou_threshold}")
        print(f"   分数阈值: {score_threshold}")
    
    def apply_soft_nms(self, detections: List[Dict]) -> List[Dict]:
        """
        应用Soft-NMS
        
        Args:
            detections: 检测结果列表
            
        Returns:
            Soft-NMS处理后的检测结果
        """
        if len(detections) == 0:
            return detections
        
        # 按类别分组处理
        class_groups = {}
        for det in detections:
            class_id = det['class_id']
            if class_id not in class_groups:
                class_groups[class_id] = []
            class_groups[class_id].append(det)
        
        final_detections = []
        
        for class_id, class_detections in class_groups.items():
            if len(class_detections) <= 1:
                final_detections.extend(class_detections)
                continue
            
            # 对每个类别应用Soft-NMS
            soft_nms_results = self._soft_nms_single_class(class_detections)
            final_detections.extend(soft_nms_results)
        
        return final_detections
    
    def _soft_nms_single_class(self, detections: List[Dict]) -> List[Dict]:
        """
        对单个类别应用Soft-NMS
        """
        # 转换为numpy数组便于处理
        boxes = np.array([det['bbox'] for det in detections])
        scores = np.array([det['confidence'] for det in detections])
        
        # Soft-NMS算法
        N = len(detections)
        for i in range(N):
            # 找到当前最高分数的框
            max_idx = np.argmax(scores[i:]) + i
            
            # 交换到当前位置
            if max_idx != i:
                boxes[[i, max_idx]] = boxes[[max_idx, i]]
                scores[[i, max_idx]] = scores[[max_idx, i]]
                detections[i], detections[max_idx] = detections[max_idx], detections[i]
            
            # 如果当前分数太低，停止处理
            if scores[i] < self.score_threshold:
                break
            
            # 计算与后续框的IoU并应用高斯衰减
            for j in range(i + 1, N):
                if scores[j] < self.score_threshold:
                    continue
                
                iou = self._calculate_iou(boxes[i], boxes[j])
                
                # 高斯衰减
                if iou > self.iou_threshold:
                    scores[j] *= np.exp(-(iou ** 2) / self.sigma)
                    detections[j]['confidence'] = float(scores[j])
        
        # 过滤低分数检测
        filtered_detections = []
        for i, det in enumerate(detections):
            if scores[i] >= self.score_threshold:
                filtered_detections.append(det)
        
        return filtered_detections
    
    def _calculate_iou(self, box1: np.ndarray, box2: np.ndarray) -> float:
        """计算两个框的IoU"""
        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2
        
        # 交集
        inter_x1 = max(x1_1, x1_2)
        inter_y1 = max(y1_1, y1_2)
        inter_x2 = min(x2_1, x2_2)
        inter_y2 = min(y2_1, y2_2)
        
        if inter_x2 <= inter_x1 or inter_y2 <= inter_y1:
            return 0.0
        
        inter_area = (inter_x2 - inter_x1) * (inter_y2 - inter_y1)
        
        # 并集
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union_area = area1 + area2 - inter_area
        
        return inter_area / union_area if union_area > 0 else 0.0

class WeightedBoxesFusion:
    """
    加权框融合 (WBF) 实现
    基于置信度的智能框融合，避免NMS的硬删除
    """
    
    def __init__(self, iou_threshold=0.55, skip_box_threshold=0.0001, conf_type='avg'):
        self.iou_threshold = iou_threshold
        self.skip_box_threshold = skip_box_threshold
        self.conf_type = conf_type
        
        print(f"🔄 WBF初始化完成")
        print(f"   IoU阈值: {iou_threshold}")
        print(f"   跳过框阈值: {skip_box_threshold}")
        print(f"   置信度类型: {conf_type}")
    
    def apply_wbf(self, all_predictions: List[Dict], image_shape: Tuple[int, int]) -> List[Dict]:
        """
        应用加权框融合
        
        Args:
            all_predictions: 所有TTA预测结果
            image_shape: 图像形状 (height, width)
            
        Returns:
            WBF融合后的检测结果
        """
        if len(all_predictions) == 0:
            return []
        
        h, w = image_shape
        
        # 准备WBF输入数据
        boxes_list = []
        scores_list = []
        labels_list = []
        weights = []
        
        for pred in all_predictions:
            detections = pred['detections']
            if len(detections) == 0:
                continue
            
            # 转换为归一化坐标
            boxes = []
            scores = []
            labels = []
            
            for det in detections:
                x1, y1, x2, y2 = det['bbox']
                # 归一化到[0,1]
                boxes.append([x1/w, y1/h, x2/w, y2/h])
                scores.append(det['confidence'])
                labels.append(det['class_id'])
            
            boxes_list.append(boxes)
            scores_list.append(scores)
            labels_list.append(labels)
            
            # 根据TTA类型设置权重
            if pred.get('flipped', False):
                weights.append(0.8)  # 翻转预测权重稍低
            elif pred.get('scale', 1.0) != 1.0:
                weights.append(0.9)  # 缩放预测权重稍低
            else:
                weights.append(1.0)  # 原始预测权重最高
        
        if len(boxes_list) == 0:
            return []
        
        # 应用WBF
        try:
            fused_boxes, fused_scores, fused_labels = weighted_boxes_fusion(
                boxes_list, scores_list, labels_list, weights,
                iou_thr=self.iou_threshold,
                skip_box_thr=self.skip_box_threshold,
                conf_type=self.conf_type
            )
            
            # 转换回像素坐标
            final_detections = []
            for box, score, label in zip(fused_boxes, fused_scores, fused_labels):
                x1, y1, x2, y2 = box
                final_detections.append({
                    'bbox': [float(x1*w), float(y1*h), float(x2*w), float(y2*h)],
                    'confidence': float(score),
                    'class_id': int(label)
                })
            
            return final_detections
            
        except Exception as e:
            print(f"⚠️  WBF失败，使用第一个预测结果: {e}")
            return all_predictions[0]['detections'] if all_predictions else []

class EnhancedInferenceSystem:
    """
    增强版推理系统
    集成TTA + Soft-NMS + WBF的完整推理流程
    """
    
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.model = None
        self.load_time = 0
        
        # 初始化组件
        self.tta = TestTimeAugmentation()
        self.soft_nms = SoftNMS()
        self.wbf = WeightedBoxesFusion()
        
        # 类别映射
        self.category_mapping = {
            0: 'CA001_衣架', 1: 'CA002_牙刷', 2: 'CB001_果冻', 3: 'CB002_长方形状饼干',
            4: 'CC001_罐装饮料', 5: 'CC002_瓶装饮料', 6: 'CD001_香蕉', 7: 'CD002_橙子', 8: 'Wxxx_未知物品'
        }
        
        # 加载模型
        self.load_model()
        
        print(f"🚀 增强版推理系统初始化完成")
        print(f"   模型: {model_path}")
        print(f"   加载时间: {self.load_time:.3f}s")
    
    def load_model(self):
        """加载模型"""
        start_time = time.time()
        
        try:
            self.model = YOLO(self.model_path)
            self.load_time = time.time() - start_time
            
            # 预热模型
            dummy_image = np.zeros((640, 640, 3), dtype=np.uint8)
            for _ in range(3):
                _ = self.model(dummy_image, verbose=False)
            
            print(f"✅ 模型加载成功")
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            self.model = None
    
    def predict_enhanced(self, image_path: str, 
                        use_tta: bool = True,
                        use_soft_nms: bool = True, 
                        use_wbf: bool = True,
                        save_result: bool = False) -> Dict:
        """
        增强版预测
        
        Args:
            image_path: 图像路径
            use_tta: 是否使用TTA
            use_soft_nms: 是否使用Soft-NMS
            use_wbf: 是否使用WBF
            save_result: 是否保存结果图像
            
        Returns:
            预测结果字典
        """
        if not self.model:
            raise RuntimeError("模型未加载")
        
        start_time = time.time()
        
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        h, w = image.shape[:2]
        
        if use_tta:
            # 应用TTA
            all_predictions = self.tta.apply_tta(self.model, image)
            
            if use_wbf and len(all_predictions) > 1:
                # 应用WBF融合
                detections = self.wbf.apply_wbf(all_predictions, (h, w))
            else:
                # 使用第一个预测结果
                detections = all_predictions[0]['detections'] if all_predictions else []
        else:
            # 标准预测
            results = self.model(image, verbose=False)[0]
            detections = self._format_standard_results(results)
        
        if use_soft_nms and len(detections) > 1:
            # 应用Soft-NMS
            detections = self.soft_nms.apply_soft_nms(detections)
        
        inference_time = time.time() - start_time
        
        # 格式化最终结果
        final_result = {
            "image_path": image_path,
            "inference_time": inference_time,
            "detections": detections,
            "detection_count": len(detections),
            "model_used": self.model_path,
            "enhancements_used": {
                "tta": use_tta,
                "soft_nms": use_soft_nms,
                "wbf": use_wbf
            }
        }
        
        # 保存结果图像
        if save_result:
            self._save_result_image(image, detections, image_path)
        
        return final_result
    
    def _format_standard_results(self, results) -> List[Dict]:
        """格式化标准预测结果"""
        detections = []
        
        if results.boxes is not None:
            boxes = results.boxes.xyxy.cpu().numpy()
            confidences = results.boxes.conf.cpu().numpy()
            class_ids = results.boxes.cls.cpu().numpy().astype(int)
            
            for box, conf, cls_id in zip(boxes, confidences, class_ids):
                x1, y1, x2, y2 = box
                detections.append({
                    'bbox': [float(x1), float(y1), float(x2), float(y2)],
                    'confidence': float(conf),
                    'class_id': int(cls_id)
                })
        
        return detections
    
    def _save_result_image(self, image: np.ndarray, detections: List[Dict], original_path: str):
        """保存结果图像"""
        result_image = image.copy()
        
        for detection in detections:
            x1, y1, x2, y2 = detection['bbox']
            class_id = detection['class_id']
            confidence = detection['confidence']
            
            category = self.category_mapping.get(class_id, f'class_{class_id}')
            
            # 绘制边界框
            color = self._get_category_color(category)
            cv2.rectangle(result_image, (int(x1), int(y1)), (int(x2), int(y2)), color, 2)
            
            # 绘制标签
            label = f"{category}: {confidence:.3f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
            cv2.rectangle(result_image, (int(x1), int(y1) - label_size[1] - 10),
                         (int(x1) + label_size[0], int(y1)), color, -1)
            cv2.putText(result_image, label, (int(x1), int(y1) - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
        
        # 保存图像
        output_path = Path(original_path).parent / f"enhanced_result_{Path(original_path).name}"
        cv2.imwrite(str(output_path), result_image)
        print(f"💾 增强结果图像已保存: {output_path}")
    
    def _get_category_color(self, category: str) -> Tuple[int, int, int]:
        """获取类别颜色"""
        colors = {
            'CA001_衣架': (255, 0, 0), 'CA002_牙刷': (0, 255, 0), 'CB001_果冻': (0, 0, 255),
            'CB002_长方形状饼干': (255, 255, 0), 'CC001_罐装饮料': (255, 0, 255), 'CC002_瓶装饮料': (0, 255, 255),
            'CD001_香蕉': (128, 0, 128), 'CD002_橙子': (255, 165, 0), 'Wxxx_未知物品': (128, 128, 128)
        }
        return colors.get(category, (255, 255, 255))

def test_enhanced_inference():
    """测试增强版推理系统"""
    print("🧪 测试增强版推理系统")
    
    # 使用当前最佳模型进行测试
    model_path = "yolo11n.pt"  # 替换为实际模型路径
    
    try:
        # 初始化增强推理系统
        inference_system = EnhancedInferenceSystem(model_path)
        
        # 创建测试图像
        test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        test_path = "test_enhanced_inference.jpg"
        cv2.imwrite(test_path, test_image)
        
        # 测试不同配置
        configs = [
            {"use_tta": False, "use_soft_nms": False, "use_wbf": False, "name": "基础"},
            {"use_tta": True, "use_soft_nms": False, "use_wbf": False, "name": "TTA"},
            {"use_tta": False, "use_soft_nms": True, "use_wbf": False, "name": "Soft-NMS"},
            {"use_tta": True, "use_soft_nms": True, "use_wbf": True, "name": "完整增强"}
        ]
        
        for config in configs:
            result = inference_system.predict_enhanced(test_path, **{k: v for k, v in config.items() if k != 'name'})
            print(f"✅ {config['name']}: {result['detection_count']} 个检测, 用时 {result['inference_time']:.3f}s")
        
        # 清理测试文件
        Path(test_path).unlink(missing_ok=True)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_enhanced_inference()