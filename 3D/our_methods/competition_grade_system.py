"""
竞赛级完整优化系统
整合所有优化策略的一站式解决方案

集成优化策略：
1. SIoU损失函数 (预期+2-3% mAP)
2. Copy-Paste增强 (预期+7-9% 小目标AP)  
3. 渐进式训练 (训练效率+20-40%)
4. TTA推理优化 (预期+2-3% mAP)
5. Soft-NMS后处理 (预期+0.5-1% mAP)
6. WBF集成融合 (预期+2-4% mAP)

总体预期性能提升：mAP50-95从68%提升到75-76%
"""
import os
import sys
import json
import time
import argparse
from pathlib import Path
from typing import Dict, List, Optional

# 添加路径
sys.path.append(str(Path(__file__).parent))

from training_strategies.enhanced_competition_training import EnhancedCompetitionTraining
from inference_methods.enhanced_inference_system import EnhancedInferenceSystem
from evaluation_tools.comprehensive_evaluator import ComprehensiveEvaluator
from current_model_config import CurrentModelConfig

class CompetitionGradeSystem:
    """
    竞赛级完整优化系统
    
    提供从训练到推理的完整优化流程：
    1. 基于Stage2模型的增强训练
    2. 多策略集成的推理优化
    3. 全面的性能评估和对比
    """
    
    def __init__(self, project_root: str = None):
        self.project_root = Path(project_root) if project_root else Path(__file__).parent.parent
        self.results_dir = self.project_root / "competition_grade_results"
        self.results_dir.mkdir(exist_ok=True)
        
        # 初始化组件
        self.config = CurrentModelConfig()
        self.trainer = EnhancedCompetitionTraining(str(self.project_root))
        self.evaluator = ComprehensiveEvaluator(str(self.project_root))
        
        # 系统状态
        self.current_best_model = None
        self.baseline_performance = None
        self.enhanced_performance = None
        
        print(f"🏆 竞赛级优化系统初始化完成")
        print(f"   项目根目录: {self.project_root}")
        print(f"   结果目录: {self.results_dir}")
    
    def run_complete_optimization(self, 
                                 skip_training: bool = False,
                                 skip_evaluation: bool = False,
                                 use_existing_model: str = None) -> Dict:
        """
        运行完整的竞赛级优化流程
        
        Args:
            skip_training: 跳过训练，直接使用现有模型
            skip_evaluation: 跳过评估
            use_existing_model: 使用指定的现有模型路径
            
        Returns:
            完整的优化结果报告
        """
        print("🚀 启动竞赛级完整优化流程")
        print("=" * 80)
        
        total_start_time = time.time()
        optimization_report = {
            "start_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "baseline_model": None,
            "enhanced_model": None,
            "training_results": None,
            "inference_optimization": None,
            "performance_comparison": None,
            "final_recommendations": None
        }
        
        # 阶段1: 获取基线模型性能
        print("\n📊 阶段1: 基线模型性能评估")
        baseline_model_path = use_existing_model or self._get_baseline_model()
        
        if baseline_model_path and Path(baseline_model_path).exists():
            self.baseline_performance = self._evaluate_baseline_model(baseline_model_path)
            optimization_report["baseline_model"] = baseline_model_path
            optimization_report["baseline_performance"] = self.baseline_performance
            
            print(f"✅ 基线模型: {Path(baseline_model_path).name}")
            if self.baseline_performance:
                print(f"   mAP50: {self.baseline_performance.get('mAP50', 0):.2f}%")
                print(f"   mAP50-95: {self.baseline_performance.get('mAP50_95', 0):.2f}%")
        else:
            print("❌ 未找到有效的基线模型")
            return optimization_report
        
        # 阶段2: 增强训练（可选）
        if not skip_training:
            print("\n🎯 阶段2: 竞赛级增强训练")
            enhanced_model_path = self._run_enhanced_training()
            
            if enhanced_model_path:
                self.current_best_model = enhanced_model_path
                optimization_report["enhanced_model"] = enhanced_model_path
                print(f"✅ 增强模型训练完成: {Path(enhanced_model_path).name}")
            else:
                print("⚠️  增强训练失败，使用基线模型")
                self.current_best_model = baseline_model_path
        else:
            print("\n⏭️  跳过训练阶段，使用现有模型")
            self.current_best_model = baseline_model_path
        
        # 阶段3: 推理优化测试
        print("\n🔧 阶段3: 推理优化测试")
        inference_results = self._test_inference_optimizations()
        optimization_report["inference_optimization"] = inference_results
        
        # 阶段4: 性能评估（可选）
        if not skip_evaluation:
            print("\n📈 阶段4: 全面性能评估")
            evaluation_results = self._run_comprehensive_evaluation()
            optimization_report["performance_comparison"] = evaluation_results
        
        # 阶段5: 生成最终报告
        print("\n📋 阶段5: 生成优化报告")
        final_report = self._generate_final_report(optimization_report)
        optimization_report["final_recommendations"] = final_report
        
        total_time = time.time() - total_start_time
        optimization_report["total_time_hours"] = total_time / 3600
        optimization_report["end_time"] = time.strftime("%Y-%m-%d %H:%M:%S")
        
        # 保存完整报告
        report_file = self.results_dir / "competition_grade_optimization_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(optimization_report, f, indent=2, ensure_ascii=False)
        
        print("\n" + "🎉" * 30)
        print(f"🏆 竞赛级优化完成!")
        print(f"⏱️  总用时: {total_time/3600:.2f}小时")
        print(f"📊 完整报告: {report_file}")
        print(f"🎯 最佳模型: {self.current_best_model}")
        print("🎉" * 30)
        
        return optimization_report
    
    def _get_baseline_model(self) -> Optional[str]:
        """获取基线模型路径"""
        # 优先使用Stage2模型作为基线
        stage2_model = self.project_root / "refined_models" / "refined_yolo11n_stage2_focused" / "weights" / "best.pt"
        if stage2_model.exists():
            return str(stage2_model)
        
        # 回退到其他可用模型
        model_name, model_path = self.config.get_best_available_model()
        return model_path if Path(model_path).exists() else None
    
    def _evaluate_baseline_model(self, model_path: str) -> Optional[Dict]:
        """评估基线模型性能"""
        try:
            dataset_yaml = str(self.project_root / "competition_2025_dataset" / "competition_dataset.yaml")
            if not Path(dataset_yaml).exists():
                print(f"⚠️  数据集配置文件不存在: {dataset_yaml}")
                return None
            
            evaluation = self.trainer.evaluate_enhanced_model(model_path, dataset_yaml)
            return evaluation['validation_metrics'] if evaluation else None
            
        except Exception as e:
            print(f"❌ 基线模型评估失败: {e}")
            return None
    
    def _run_enhanced_training(self) -> Optional[str]:
        """运行增强训练"""
        try:
            # 设置Copy-Paste增强
            dataset_path = self.project_root / "competition_2025_dataset"
            if dataset_path.exists():
                self.trainer.setup_copy_paste_augmentation(str(dataset_path))
            
            # 运行渐进式训练
            enhanced_model = self.trainer.run_progressive_training()
            return enhanced_model
            
        except Exception as e:
            print(f"❌ 增强训练失败: {e}")
            return None
    
    def _test_inference_optimizations(self) -> Dict:
        """测试推理优化策略"""
        if not self.current_best_model or not Path(self.current_best_model).exists():
            return {"error": "没有可用的模型进行推理测试"}
        
        try:
            # 初始化增强推理系统
            inference_system = EnhancedInferenceSystem(self.current_best_model)
            
            # 创建测试图像（实际使用中应该用真实测试图像）
            import cv2
            import numpy as np
            
            test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
            test_path = self.results_dir / "inference_test.jpg"
            cv2.imwrite(str(test_path), test_image)
            
            # 测试不同推理配置
            inference_configs = [
                {"use_tta": False, "use_soft_nms": False, "use_wbf": False, "name": "基础推理"},
                {"use_tta": True, "use_soft_nms": False, "use_wbf": False, "name": "TTA优化"},
                {"use_tta": False, "use_soft_nms": True, "use_wbf": False, "name": "Soft-NMS优化"},
                {"use_tta": True, "use_soft_nms": True, "use_wbf": True, "name": "完整优化"}
            ]
            
            inference_results = {}
            
            for config in inference_configs:
                config_params = {k: v for k, v in config.items() if k != 'name'}
                result = inference_system.predict_enhanced(str(test_path), **config_params)
                
                inference_results[config['name']] = {
                    "inference_time": result['inference_time'],
                    "detection_count": result['detection_count'],
                    "enhancements_used": result['enhancements_used']
                }
                
                print(f"   {config['name']}: {result['detection_count']} 检测, {result['inference_time']:.3f}s")
            
            # 清理测试文件
            test_path.unlink(missing_ok=True)
            
            return inference_results
            
        except Exception as e:
            print(f"❌ 推理优化测试失败: {e}")
            return {"error": str(e)}
    
    def _run_comprehensive_evaluation(self) -> Optional[Dict]:
        """运行全面性能评估"""
        try:
            dataset_yaml = str(self.project_root / "competition_2025_dataset" / "competition_dataset.yaml")
            if not Path(dataset_yaml).exists():
                return {"error": "数据集配置文件不存在"}
            
            # 运行综合评估
            evaluation_results = self.evaluator.evaluate_all_models(dataset_yaml)
            return evaluation_results
            
        except Exception as e:
            print(f"❌ 综合评估失败: {e}")
            return {"error": str(e)}
    
    def _generate_final_report(self, optimization_report: Dict) -> Dict:
        """生成最终优化报告"""
        final_report = {
            "optimization_summary": "竞赛级优化策略实施完成",
            "implemented_strategies": [
                "SIoU损失函数 - 方向感知的角度代价计算",
                "Copy-Paste增强 - 专门针对困难类别优化",
                "渐进式训练 - 384→512→640逐步提升",
                "TTA推理优化 - 多尺度+翻转增强",
                "Soft-NMS后处理 - 高斯衰减替代硬阈值",
                "WBF集成融合 - 加权框融合避免误删除"
            ],
            "expected_improvements": {
                "mAP50_95_improvement": "5-8%",
                "small_object_ap_improvement": "7-10%",
                "difficult_categories_improvement": "10-15%",
                "training_efficiency_improvement": "20-40%"
            },
            "deployment_recommendations": [],
            "next_steps": []
        }
        
        # 基于结果生成具体建议
        if self.baseline_performance and optimization_report.get("enhanced_model"):
            final_report["deployment_recommendations"].extend([
                f"推荐使用增强模型: {Path(optimization_report['enhanced_model']).name}",
                "启用完整推理优化（TTA + Soft-NMS + WBF）以获得最佳精度",
                "对于实时应用，可选择性启用TTA以平衡速度和精度"
            ])
        
        # 推理优化建议
        inference_results = optimization_report.get("inference_optimization", {})
        if "完整优化" in inference_results:
            full_opt = inference_results["完整优化"]
            if full_opt.get("inference_time", 0) < 0.1:  # 100ms以内
                final_report["deployment_recommendations"].append(
                    "推理速度优秀，可以启用所有优化策略"
                )
            else:
                final_report["deployment_recommendations"].append(
                    "推理时间较长，建议根据应用场景选择性启用优化"
                )
        
        # 后续改进建议
        final_report["next_steps"].extend([
            "在真实测试集上验证优化效果",
            "针对特定困难样本进行进一步优化",
            "考虑模型蒸馏以减少模型大小",
            "实施量化优化以提升推理速度"
        ])
        
        return final_report
    
    def quick_inference_test(self, image_path: str, save_comparison: bool = True) -> Dict:
        """
        快速推理测试
        对比不同优化策略的效果
        """
        if not self.current_best_model:
            model_name, model_path = self.config.get_best_available_model()
            self.current_best_model = model_path
        
        if not Path(self.current_best_model).exists():
            return {"error": "没有可用的模型"}
        
        try:
            inference_system = EnhancedInferenceSystem(self.current_best_model)
            
            # 测试不同配置
            test_results = {}
            
            configs = [
                {"use_tta": False, "use_soft_nms": False, "use_wbf": False, "name": "基础"},
                {"use_tta": True, "use_soft_nms": True, "use_wbf": True, "name": "完整优化"}
            ]
            
            for config in configs:
                config_params = {k: v for k, v in config.items() if k != 'name'}
                result = inference_system.predict_enhanced(
                    image_path, 
                    save_result=save_comparison,
                    **config_params
                )
                test_results[config['name']] = result
            
            return test_results
            
        except Exception as e:
            return {"error": str(e)}

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="竞赛级优化系统")
    parser.add_argument("--mode", choices=["full", "training", "inference", "evaluation"], 
                       default="full", help="运行模式")
    parser.add_argument("--skip-training", action="store_true", help="跳过训练")
    parser.add_argument("--skip-evaluation", action="store_true", help="跳过评估")
    parser.add_argument("--model", type=str, help="使用指定模型路径")
    parser.add_argument("--test-image", type=str, help="测试图像路径")
    
    args = parser.parse_args()
    
    # 初始化系统
    system = CompetitionGradeSystem()
    
    if args.mode == "full":
        # 运行完整优化流程
        results = system.run_complete_optimization(
            skip_training=args.skip_training,
            skip_evaluation=args.skip_evaluation,
            use_existing_model=args.model
        )
        
        print(f"\n🎯 优化完成! 查看详细报告:")
        print(f"   {system.results_dir / 'competition_grade_optimization_report.json'}")
        
    elif args.mode == "inference" and args.test_image:
        # 快速推理测试
        results = system.quick_inference_test(args.test_image)
        
        print(f"\n🔍 推理测试结果:")
        for config_name, result in results.items():
            if "error" not in result:
                print(f"   {config_name}: {result['detection_count']} 检测, {result['inference_time']:.3f}s")
            else:
                print(f"   {config_name}: 错误 - {result['error']}")
    
    else:
        print("请指定有效的运行模式和参数")

if __name__ == "__main__":
    main()