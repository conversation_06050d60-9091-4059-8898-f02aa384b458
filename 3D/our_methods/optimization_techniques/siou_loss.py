"""
SIoU (SCYLLA-IoU) Loss Implementation
基于论文: https://arxiv.org/abs/2205.12740v1

核心创新：引入方向感知的角度代价计算，解决预测框与真实框方向不匹配问题
预期性能提升：mAP@0.5:0.95 +2.4%, mAP@0.5 +3.6%
"""
import torch
import torch.nn as nn
import math
import numpy as np

class SIoULoss(nn.Module):
    """
    SIoU (SCYLLA-IoU) Loss Implementation
    
    SIoU = 1 - IoU + Distance_Cost + Shape_Cost + Angle_Cost
    
    核心优势：
    1. 方向感知：通过角度代价解决方向不匹配问题
    2. 快速收敛：495轮达到真实值 vs CIoU的1000+轮
    3. 更好的定位：特别适合困难类别（牙刷、果冻）
    """
    
    def __init__(self, theta=4.0):
        super(SIoULoss, self).__init__()
        self.theta = theta  # 角度代价的权重参数
        
    def forward(self, pred_boxes, target_boxes, eps=1e-7):
        """
        计算SIoU损失
        
        Args:
            pred_boxes: 预测框 [N, 4] (x1, y1, x2, y2)
            target_boxes: 真实框 [N, 4] (x1, y1, x2, y2)
            eps: 数值稳定性参数
            
        Returns:
            SIoU损失值
        """
        # 确保输入为tensor
        if not isinstance(pred_boxes, torch.Tensor):
            pred_boxes = torch.tensor(pred_boxes, dtype=torch.float32)
        if not isinstance(target_boxes, torch.Tensor):
            target_boxes = torch.tensor(target_boxes, dtype=torch.float32)
            
        # 计算交集区域
        inter_x1 = torch.max(pred_boxes[:, 0], target_boxes[:, 0])
        inter_y1 = torch.max(pred_boxes[:, 1], target_boxes[:, 1])
        inter_x2 = torch.min(pred_boxes[:, 2], target_boxes[:, 2])
        inter_y2 = torch.min(pred_boxes[:, 3], target_boxes[:, 3])
        
        # 交集面积
        inter_area = torch.clamp(inter_x2 - inter_x1, min=0) * \
                    torch.clamp(inter_y2 - inter_y1, min=0)
        
        # 预测框和真实框面积
        pred_area = (pred_boxes[:, 2] - pred_boxes[:, 0]) * \
                   (pred_boxes[:, 3] - pred_boxes[:, 1])
        target_area = (target_boxes[:, 2] - target_boxes[:, 0]) * \
                     (target_boxes[:, 3] - target_boxes[:, 1])
        
        # 并集面积
        union_area = pred_area + target_area - inter_area + eps
        
        # IoU
        iou = inter_area / union_area
        
        # 计算中心点
        pred_cx = (pred_boxes[:, 0] + pred_boxes[:, 2]) / 2
        pred_cy = (pred_boxes[:, 1] + pred_boxes[:, 3]) / 2
        target_cx = (target_boxes[:, 0] + target_boxes[:, 2]) / 2
        target_cy = (target_boxes[:, 1] + target_boxes[:, 3]) / 2
        
        # 计算宽高
        pred_w = pred_boxes[:, 2] - pred_boxes[:, 0]
        pred_h = pred_boxes[:, 3] - pred_boxes[:, 1]
        target_w = target_boxes[:, 2] - target_boxes[:, 0]
        target_h = target_boxes[:, 3] - target_boxes[:, 1]
        
        # 1. 距离代价 (Distance Cost)
        # 计算最小外接矩形
        enclose_x1 = torch.min(pred_boxes[:, 0], target_boxes[:, 0])
        enclose_y1 = torch.min(pred_boxes[:, 1], target_boxes[:, 1])
        enclose_x2 = torch.max(pred_boxes[:, 2], target_boxes[:, 2])
        enclose_y2 = torch.max(pred_boxes[:, 3], target_boxes[:, 3])
        
        enclose_w = enclose_x2 - enclose_x1 + eps
        enclose_h = enclose_y2 - enclose_y1 + eps
        
        # 中心点距离
        center_distance = (pred_cx - target_cx) ** 2 + (pred_cy - target_cy) ** 2
        # 对角线距离
        diagonal_distance = enclose_w ** 2 + enclose_h ** 2 + eps
        
        distance_cost = center_distance / diagonal_distance
        
        # 2. 形状代价 (Shape Cost)
        # 宽高差异
        wh_loss = torch.pow(pred_w - target_w, 2) / (4 * (enclose_w ** 2 + eps)) + \
                 torch.pow(pred_h - target_h, 2) / (4 * (enclose_h ** 2 + eps))
        
        shape_cost = wh_loss
        
        # 3. 角度代价 (Angle Cost) - SIoU的核心创新
        # 计算角度差异
        sigma = torch.pow(center_distance, 0.5)
        sin_alpha_1 = torch.abs(pred_cx - target_cx) / (sigma + eps)
        sin_alpha_2 = torch.abs(pred_cy - target_cy) / (sigma + eps)
        
        # 角度阈值
        threshold = torch.pow(2, 0.5) / 2
        
        # 根据角度大小选择不同的角度代价计算方式
        angle_cost = torch.where(
            sin_alpha_1 > threshold,
            distance_cost,  # 角度较大时，使用距离代价
            torch.where(
                sin_alpha_2 > threshold,
                distance_cost,  # 角度较大时，使用距离代价
                # 角度较小时，计算精确的角度代价
                2 * torch.sin(torch.arcsin(sin_alpha_1) - math.pi/4) ** 2
            )
        )
        
        # 4. 计算最终的SIoU损失
        # Omega权重，用于平衡不同代价项
        omega = torch.exp(-(distance_cost + shape_cost)) * self.theta
        
        # SIoU = IoU - Distance_Cost - Shape_Cost - Angle_Cost
        siou = iou - distance_cost - omega * angle_cost
        
        # 返回损失 (1 - SIoU)
        loss = 1 - siou
        
        return loss.mean()
    
    def compute_angle_cost_v2(self, pred_boxes, target_boxes, eps=1e-7):
        """
        改进版角度代价计算
        更精确地处理方向不匹配问题
        """
        # 计算中心点
        pred_cx = (pred_boxes[:, 0] + pred_boxes[:, 2]) / 2
        pred_cy = (pred_boxes[:, 1] + pred_boxes[:, 3]) / 2
        target_cx = (target_boxes[:, 0] + target_boxes[:, 2]) / 2
        target_cy = (target_boxes[:, 1] + target_boxes[:, 3]) / 2
        
        # 计算宽高
        pred_w = pred_boxes[:, 2] - pred_boxes[:, 0]
        pred_h = pred_boxes[:, 3] - pred_boxes[:, 1]
        target_w = target_boxes[:, 2] - target_boxes[:, 0]
        target_h = target_boxes[:, 3] - target_boxes[:, 1]
        
        # 计算方向向量
        pred_aspect = pred_w / (pred_h + eps)
        target_aspect = target_w / (target_h + eps)
        
        # 角度差异（基于宽高比）
        aspect_diff = torch.abs(torch.log(pred_aspect + eps) - torch.log(target_aspect + eps))
        
        # 位置角度差异
        dx = pred_cx - target_cx
        dy = pred_cy - target_cy
        distance = torch.sqrt(dx**2 + dy**2 + eps)
        
        # 归一化的角度代价
        angle_cost = aspect_diff * torch.exp(-distance / (pred_w + pred_h + eps))
        
        return angle_cost

class FocalSIoULoss(nn.Module):
    """
    Focal-SIoU组合损失
    结合Focal Loss的分类优势和SIoU的定位优势
    """
    
    def __init__(self, alpha=0.25, gamma=2.0, siou_theta=4.0):
        super(FocalSIoULoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.siou_loss = SIoULoss(theta=siou_theta)
        
    def focal_loss(self, pred_cls, target_cls, eps=1e-7):
        """
        Focal Loss for classification
        """
        # 计算交叉熵
        ce_loss = nn.functional.cross_entropy(pred_cls, target_cls, reduction='none')
        
        # 计算pt
        pt = torch.exp(-ce_loss)
        
        # 计算alpha权重
        alpha_t = self.alpha * target_cls + (1 - self.alpha) * (1 - target_cls)
        
        # Focal Loss
        focal_loss = alpha_t * (1 - pt) ** self.gamma * ce_loss
        
        return focal_loss.mean()
    
    def forward(self, pred_boxes, target_boxes, pred_cls=None, target_cls=None):
        """
        组合损失计算
        """
        # SIoU定位损失
        siou_loss = self.siou_loss(pred_boxes, target_boxes)
        
        # 如果提供了分类信息，计算Focal分类损失
        if pred_cls is not None and target_cls is not None:
            focal_cls_loss = self.focal_loss(pred_cls, target_cls)
            return siou_loss + focal_cls_loss
        
        return siou_loss

def test_siou_loss():
    """
    测试SIoU损失函数
    """
    print("🧪 测试SIoU损失函数")
    
    # 创建测试数据
    pred_boxes = torch.tensor([
        [10, 10, 50, 50],  # 预测框1
        [20, 20, 60, 60],  # 预测框2
        [5, 5, 25, 45],    # 预测框3 (细长，类似牙刷)
    ], dtype=torch.float32)
    
    target_boxes = torch.tensor([
        [15, 15, 55, 55],  # 真实框1
        [25, 25, 65, 65],  # 真实框2
        [8, 8, 28, 48],    # 真实框3 (细长，类似牙刷)
    ], dtype=torch.float32)
    
    # 测试SIoU损失
    siou_loss_fn = SIoULoss()
    loss = siou_loss_fn(pred_boxes, target_boxes)
    
    print(f"✅ SIoU损失值: {loss.item():.4f}")
    
    # 对比标准IoU损失
    def compute_iou_loss(pred, target):
        # 简单的IoU损失计算
        inter_x1 = torch.max(pred[:, 0], target[:, 0])
        inter_y1 = torch.max(pred[:, 1], target[:, 1])
        inter_x2 = torch.min(pred[:, 2], target[:, 2])
        inter_y2 = torch.min(pred[:, 3], target[:, 3])
        
        inter_area = torch.clamp(inter_x2 - inter_x1, min=0) * \
                    torch.clamp(inter_y2 - inter_y1, min=0)
        
        pred_area = (pred[:, 2] - pred[:, 0]) * (pred[:, 3] - pred[:, 1])
        target_area = (target[:, 2] - target[:, 0]) * (target[:, 3] - target[:, 1])
        union_area = pred_area + target_area - inter_area + 1e-7
        
        iou = inter_area / union_area
        return (1 - iou).mean()
    
    iou_loss = compute_iou_loss(pred_boxes, target_boxes)
    print(f"📊 标准IoU损失: {iou_loss.item():.4f}")
    print(f"🎯 SIoU相对改进: {((iou_loss - loss) / iou_loss * 100).item():.2f}%")
    
    # 测试Focal-SIoU组合损失
    focal_siou = FocalSIoULoss()
    combined_loss = focal_siou(pred_boxes, target_boxes)
    print(f"🔥 Focal-SIoU损失: {combined_loss.item():.4f}")

if __name__ == "__main__":
    test_siou_loss()