"""
Copy-Paste增强优化 - 专门针对困难类别（牙刷、果冻）
基于论文: https://arxiv.org/abs/1902.07296

核心创新：
1. 小目标过采样：优先选择包含困难类别的图像
2. 策略性复制：在训练图像中复制小目标多次
3. 智能放置：避免重叠，保持真实放置模式

预期性能提升：目标检测+7.1%，小目标AP+9.7%
"""
import cv2
import numpy as np
import random
import json
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import albumentations as A
from albumentations.core.bbox_utils import denormalize_bbox, normalize_bbox

class CopyPasteAugmentation:
    """
    Copy-Paste增强系统
    专门针对困难类别（CA002_牙刷、CB001_果冻）优化
    """
    
    def __init__(self, 
                 difficult_classes=['CA002', 'CB001'],  # 牙刷、果冻
                 small_object_threshold=32*32,  # 小目标阈值（像素面积）
                 paste_probability=0.5,  # 粘贴概率
                 max_paste_objects=3,  # 每张图像最大粘贴目标数
                 min_paste_objects=1,  # 每张图像最小粘贴目标数
                 overlap_threshold=0.1):  # 重叠阈值
        
        self.difficult_classes = difficult_classes
        self.small_object_threshold = small_object_threshold
        self.paste_probability = paste_probability
        self.max_paste_objects = max_paste_objects
        self.min_paste_objects = min_paste_objects
        self.overlap_threshold = overlap_threshold
        
        # 类别映射
        self.class_mapping = {
            'CA001': 0, 'CA002': 1, 'CB001': 2, 'CB002': 3,
            'CC001': 4, 'CC002': 5, 'CD001': 6, 'CD002': 7, 'Wxxx': 8
        }
        
        # 困难类别ID
        self.difficult_class_ids = [self.class_mapping[cls] for cls in difficult_classes]
        
        # 小目标数据库
        self.small_object_database = []
        
        print(f"🎯 Copy-Paste增强初始化完成")
        print(f"   困难类别: {difficult_classes}")
        print(f"   小目标阈值: {small_object_threshold} 像素")
        print(f"   粘贴概率: {paste_probability}")
    
    def build_small_object_database(self, dataset_path: str):
        """
        构建小目标数据库
        扫描数据集，收集所有困难类别的小目标
        """
        dataset_path = Path(dataset_path)
        images_dir = dataset_path / "images" / "train"
        labels_dir = dataset_path / "labels" / "train"
        
        print(f"🔍 构建小目标数据库...")
        print(f"   图像目录: {images_dir}")
        print(f"   标签目录: {labels_dir}")
        
        small_objects_count = 0
        processed_images = 0
        
        for label_file in labels_dir.glob("*.txt"):
            image_file = images_dir / f"{label_file.stem}.jpg"
            if not image_file.exists():
                image_file = images_dir / f"{label_file.stem}.png"
            
            if not image_file.exists():
                continue
            
            # 读取图像
            image = cv2.imread(str(image_file))
            if image is None:
                continue
                
            h, w = image.shape[:2]
            
            # 读取标签
            with open(label_file, 'r') as f:
                lines = f.readlines()
            
            for line in lines:
                parts = line.strip().split()
                if len(parts) < 5:
                    continue
                
                class_id = int(parts[0])
                x_center, y_center, width, height = map(float, parts[1:5])
                
                # 转换为像素坐标
                x_center *= w
                y_center *= h
                width *= w
                height *= h
                
                # 计算面积
                area = width * height
                
                # 检查是否为困难类别的小目标
                if class_id in self.difficult_class_ids and area <= self.small_object_threshold:
                    # 提取目标区域
                    x1 = int(x_center - width / 2)
                    y1 = int(y_center - height / 2)
                    x2 = int(x_center + width / 2)
                    y2 = int(y_center + height / 2)
                    
                    # 确保边界在图像内
                    x1 = max(0, x1)
                    y1 = max(0, y1)
                    x2 = min(w, x2)
                    y2 = min(h, y2)
                    
                    if x2 > x1 and y2 > y1:
                        # 提取目标图像
                        object_image = image[y1:y2, x1:x2].copy()
                        
                        # 创建掩码（简单的矩形掩码）
                        mask = np.ones((y2-y1, x2-x1), dtype=np.uint8) * 255
                        
                        # 存储到数据库
                        self.small_object_database.append({
                            'image': object_image,
                            'mask': mask,
                            'class_id': class_id,
                            'width': x2 - x1,
                            'height': y2 - y1,
                            'area': area,
                            'source_file': str(image_file)
                        })
                        
                        small_objects_count += 1
            
            processed_images += 1
            if processed_images % 100 == 0:
                print(f"   已处理 {processed_images} 张图像，收集 {small_objects_count} 个小目标")
        
        print(f"✅ 小目标数据库构建完成")
        print(f"   总计收集: {small_objects_count} 个小目标")
        print(f"   困难类别分布:")
        
        # 统计类别分布
        class_counts = {}
        for obj in self.small_object_database:
            class_id = obj['class_id']
            class_counts[class_id] = class_counts.get(class_id, 0) + 1
        
        for class_id, count in class_counts.items():
            class_name = [k for k, v in self.class_mapping.items() if v == class_id][0]
            print(f"     {class_name}: {count} 个")
    
    def apply_copy_paste(self, image: np.ndarray, bboxes: List, class_labels: List) -> Tuple[np.ndarray, List, List]:
        """
        应用Copy-Paste增强
        
        Args:
            image: 输入图像 [H, W, C]
            bboxes: 边界框列表 [[x1, y1, x2, y2], ...]
            class_labels: 类别标签列表 [class_id, ...]
            
        Returns:
            增强后的图像、边界框和类别标签
        """
        if len(self.small_object_database) == 0:
            return image, bboxes, class_labels
        
        # 随机决定是否应用Copy-Paste
        if random.random() > self.paste_probability:
            return image, bboxes, class_labels
        
        augmented_image = image.copy()
        augmented_bboxes = bboxes.copy()
        augmented_labels = class_labels.copy()
        
        h, w = image.shape[:2]
        
        # 随机选择要粘贴的目标数量
        num_paste = random.randint(self.min_paste_objects, self.max_paste_objects)
        
        for _ in range(num_paste):
            # 随机选择一个小目标
            obj = random.choice(self.small_object_database)
            
            obj_image = obj['image']
            obj_mask = obj['mask']
            obj_class = obj['class_id']
            obj_w, obj_h = obj['width'], obj['height']
            
            # 寻找合适的粘贴位置
            paste_position = self._find_paste_position(
                augmented_image, augmented_bboxes, obj_w, obj_h
            )
            
            if paste_position is None:
                continue
            
            paste_x, paste_y = paste_position
            
            # 应用随机变换
            transformed_obj, transformed_mask = self._apply_object_transforms(
                obj_image, obj_mask
            )
            
            # 粘贴目标
            self._paste_object(
                augmented_image, transformed_obj, transformed_mask, 
                paste_x, paste_y
            )
            
            # 添加新的边界框和标签
            new_bbox = [paste_x, paste_y, paste_x + obj_w, paste_y + obj_h]
            augmented_bboxes.append(new_bbox)
            augmented_labels.append(obj_class)
        
        return augmented_image, augmented_bboxes, augmented_labels
    
    def _find_paste_position(self, image: np.ndarray, existing_bboxes: List, 
                           obj_w: int, obj_h: int, max_attempts: int = 50) -> Optional[Tuple[int, int]]:
        """
        寻找合适的粘贴位置，避免与现有目标重叠
        """
        h, w = image.shape[:2]
        
        for _ in range(max_attempts):
            # 随机生成位置
            paste_x = random.randint(0, max(1, w - obj_w))
            paste_y = random.randint(0, max(1, h - obj_h))
            
            # 检查与现有边界框的重叠
            new_bbox = [paste_x, paste_y, paste_x + obj_w, paste_y + obj_h]
            
            overlap_found = False
            for existing_bbox in existing_bboxes:
                if self._calculate_overlap(new_bbox, existing_bbox) > self.overlap_threshold:
                    overlap_found = True
                    break
            
            if not overlap_found:
                return paste_x, paste_y
        
        return None
    
    def _calculate_overlap(self, bbox1: List, bbox2: List) -> float:
        """
        计算两个边界框的重叠比例
        """
        x1_1, y1_1, x2_1, y2_1 = bbox1
        x1_2, y1_2, x2_2, y2_2 = bbox2
        
        # 计算交集
        inter_x1 = max(x1_1, x1_2)
        inter_y1 = max(y1_1, y1_2)
        inter_x2 = min(x2_1, x2_2)
        inter_y2 = min(y2_1, y2_2)
        
        if inter_x2 <= inter_x1 or inter_y2 <= inter_y1:
            return 0.0
        
        inter_area = (inter_x2 - inter_x1) * (inter_y2 - inter_y1)
        
        # 计算并集
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union_area = area1 + area2 - inter_area
        
        return inter_area / union_area if union_area > 0 else 0.0
    
    def _apply_object_transforms(self, obj_image: np.ndarray, obj_mask: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        对要粘贴的目标应用随机变换
        """
        # 定义变换
        transform = A.Compose([
            A.HorizontalFlip(p=0.5),
            A.Rotate(limit=15, p=0.3),
            A.RandomBrightnessContrast(brightness_limit=0.1, contrast_limit=0.1, p=0.3),
            A.HueSaturationValue(hue_shift_limit=10, sat_shift_limit=10, val_shift_limit=10, p=0.3),
            A.GaussNoise(var_limit=(10, 30), p=0.2),
        ], p=0.7)
        
        # 应用变换
        try:
            transformed = transform(image=obj_image, mask=obj_mask)
            return transformed['image'], transformed['mask']
        except:
            return obj_image, obj_mask
    
    def _paste_object(self, target_image: np.ndarray, obj_image: np.ndarray, 
                     obj_mask: np.ndarray, paste_x: int, paste_y: int):
        """
        将目标粘贴到目标图像上
        """
        obj_h, obj_w = obj_image.shape[:2]
        target_h, target_w = target_image.shape[:2]
        
        # 确保粘贴区域在图像范围内
        end_x = min(paste_x + obj_w, target_w)
        end_y = min(paste_y + obj_h, target_h)
        
        actual_w = end_x - paste_x
        actual_h = end_y - paste_y
        
        if actual_w <= 0 or actual_h <= 0:
            return
        
        # 调整目标图像和掩码大小
        obj_image_resized = obj_image[:actual_h, :actual_w]
        obj_mask_resized = obj_mask[:actual_h, :actual_w]
        
        # 归一化掩码
        mask_norm = obj_mask_resized.astype(np.float32) / 255.0
        mask_norm = np.expand_dims(mask_norm, axis=2)
        
        # 混合图像
        target_region = target_image[paste_y:end_y, paste_x:end_x]
        blended = obj_image_resized * mask_norm + target_region * (1 - mask_norm)
        
        # 更新目标图像
        target_image[paste_y:end_y, paste_x:end_x] = blended.astype(np.uint8)

class CopyPasteDataset:
    """
    集成Copy-Paste增强的数据集包装器
    """
    
    def __init__(self, dataset_path: str, copy_paste_aug: CopyPasteAugmentation):
        self.dataset_path = Path(dataset_path)
        self.copy_paste_aug = copy_paste_aug
        
        # 构建小目标数据库
        self.copy_paste_aug.build_small_object_database(dataset_path)
    
    def get_augmented_sample(self, image_path: str, label_path: str):
        """
        获取增强后的样本
        """
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            return None, None, None
        
        h, w = image.shape[:2]
        
        # 读取标签
        bboxes = []
        class_labels = []
        
        with open(label_path, 'r') as f:
            lines = f.readlines()
        
        for line in lines:
            parts = line.strip().split()
            if len(parts) < 5:
                continue
            
            class_id = int(parts[0])
            x_center, y_center, width, height = map(float, parts[1:5])
            
            # 转换为像素坐标
            x1 = int((x_center - width / 2) * w)
            y1 = int((y_center - height / 2) * h)
            x2 = int((x_center + width / 2) * w)
            y2 = int((y_center + height / 2) * h)
            
            bboxes.append([x1, y1, x2, y2])
            class_labels.append(class_id)
        
        # 应用Copy-Paste增强
        augmented_image, augmented_bboxes, augmented_labels = \
            self.copy_paste_aug.apply_copy_paste(image, bboxes, class_labels)
        
        return augmented_image, augmented_bboxes, augmented_labels

def test_copy_paste_augmentation():
    """
    测试Copy-Paste增强
    """
    print("🧪 测试Copy-Paste增强")
    
    # 创建Copy-Paste增强器
    copy_paste_aug = CopyPasteAugmentation(
        difficult_classes=['CA002', 'CB001'],  # 牙刷、果冻
        paste_probability=0.8,  # 高概率用于测试
        max_paste_objects=2
    )
    
    # 模拟小目标数据库
    # 在实际使用中，这会从真实数据集构建
    dummy_object = {
        'image': np.random.randint(0, 255, (20, 60, 3), dtype=np.uint8),  # 细长目标（牙刷）
        'mask': np.ones((20, 60), dtype=np.uint8) * 255,
        'class_id': 1,  # CA002_牙刷
        'width': 60,
        'height': 20,
        'area': 1200,
        'source_file': 'test.jpg'
    }
    
    copy_paste_aug.small_object_database = [dummy_object] * 10
    
    # 创建测试图像
    test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
    test_bboxes = [[100, 100, 200, 200], [300, 300, 400, 400]]
    test_labels = [0, 4]
    
    # 应用增强
    aug_image, aug_bboxes, aug_labels = copy_paste_aug.apply_copy_paste(
        test_image, test_bboxes, test_labels
    )
    
    print(f"✅ 原始目标数: {len(test_bboxes)}")
    print(f"✅ 增强后目标数: {len(aug_bboxes)}")
    print(f"✅ 新增目标数: {len(aug_bboxes) - len(test_bboxes)}")
    print(f"✅ 增强后类别: {aug_labels}")

if __name__ == "__main__":
    test_copy_paste_augmentation()