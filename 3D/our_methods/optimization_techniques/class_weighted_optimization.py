"""
类别权重优化技术 - 针对困难类别的优化方法
"""
import numpy as np
import torch
import torch.nn as nn
from typing import Dict, List, Tuple
import json
from pathlib import Path

class ClassWeightedOptimization:
    """类别权重优化器"""
    
    def __init__(self):
        # 基于数据分析的类别权重
        self.class_weights = {
            0: 1.0,   # CA001_衣架 - 相对容易
            1: 2.5,   # CA002_牙刷 - 困难类别，增加权重
            2: 2.5,   # CB001_果冻 - 困难类别，增加权重
            3: 1.0,   # CB002_长方形状饼干 - 相对容易
            4: 1.0,   # CC001_罐装饮料 - 相对容易
            5: 1.0,   # CC002_瓶装饮料 - 相对容易
            6: 1.0,   # CD001_香蕉 - 相对容易
            7: 1.0,   # CD002_橙子 - 相对容易
            8: 1.2    # Wxxx_未知物品 - 稍微增加权重
        }
        
        # 困难类别特殊处理
        self.difficult_categories = {
            1: {  # CA002_牙刷
                'name': 'CA002_牙刷',
                'challenges': ['细长形状', '小目标', '背景干扰'],
                'optimization_strategies': [
                    'aspect_ratio_aware_loss',
                    'small_object_enhancement',
                    'hard_negative_mining'
                ]
            },
            2: {  # CB001_果冻
                'name': 'CB001_果冻',
                'challenges': ['透明材质', '小目标', '形状变化'],
                'optimization_strategies': [
                    'transparency_aware_loss',
                    'multi_scale_training',
                    'data_augmentation_enhancement'
                ]
            }
        }
        
        # 损失函数权重配置
        self.loss_weights = {
            'box_loss': 1.0,
            'cls_loss': 1.0,
            'obj_loss': 1.0,
            'difficult_category_bonus': 0.5  # 困难类别额外奖励
        }
    
    def get_class_weights_tensor(self, device='cpu') -> torch.Tensor:
        """获取类别权重张量"""
        weights = [self.class_weights[i] for i in range(len(self.class_weights))]
        return torch.tensor(weights, dtype=torch.float32, device=device)
    
    def apply_focal_loss_weights(self, alpha: float = 0.25, gamma: float = 2.0) -> Dict:
        """应用Focal Loss权重"""
        focal_weights = {}
        
        for class_id, base_weight in self.class_weights.items():
            # 困难类别使用更高的alpha值
            if class_id in self.difficult_categories:
                focal_weights[class_id] = {
                    'alpha': alpha * 1.5,  # 增加困难类别的alpha
                    'gamma': gamma,
                    'base_weight': base_weight
                }
            else:
                focal_weights[class_id] = {
                    'alpha': alpha,
                    'gamma': gamma,
                    'base_weight': base_weight
                }
        
        return focal_weights
    
    def calculate_adaptive_weights(self, class_distribution: Dict[int, int]) -> Dict[int, float]:
        """根据类别分布计算自适应权重"""
        total_samples = sum(class_distribution.values())
        adaptive_weights = {}
        
        for class_id, count in class_distribution.items():
            # 基础逆频率权重
            inverse_freq_weight = total_samples / (len(class_distribution) * count)
            
            # 结合预设权重
            base_weight = self.class_weights.get(class_id, 1.0)
            
            # 困难类别额外加权
            if class_id in self.difficult_categories:
                difficulty_multiplier = 1.5
            else:
                difficulty_multiplier = 1.0
            
            # 最终权重
            adaptive_weights[class_id] = inverse_freq_weight * base_weight * difficulty_multiplier
        
        return adaptive_weights
    
    def get_training_config_with_weights(self, base_config: Dict) -> Dict:
        """获取包含权重优化的训练配置"""
        optimized_config = base_config.copy()
        
        # 添加类别权重相关配置
        optimized_config.update({
            # 损失函数配置
            'cls_pw': 0.5,  # 分类损失权重
            'obj_pw': 1.0,  # 目标性损失权重
            'box_pw': 0.05, # 边界框损失权重
            
            # 困难类别优化
            'mosaic': 1.0,  # 保持mosaic增强
            'mixup': 0.1,   # 适度mixup
            'copy_paste': 0.1,  # 适度copy-paste
            
            # 学习率策略
            'lr0': 0.001,   # 初始学习率
            'lrf': 0.01,    # 最终学习率比例
            'warmup_epochs': 10,  # 预热轮数
            
            # 数据增强（针对困难类别优化）
            'degrees': 10.0,      # 旋转角度
            'translate': 0.1,     # 平移比例
            'scale': 0.5,         # 缩放比例
            'shear': 2.0,         # 剪切角度
            'perspective': 0.0001, # 透视变换
            'flipud': 0.0,        # 上下翻转
            'fliplr': 0.5,        # 左右翻转
            
            # HSV增强（对透明物体有帮助）
            'hsv_h': 0.015,  # 色调
            'hsv_s': 0.7,    # 饱和度
            'hsv_v': 0.4,    # 明度
        })
        
        return optimized_config
    
    def create_custom_loss_function(self):
        """创建自定义损失函数"""
        class WeightedYOLOLoss(nn.Module):
            def __init__(self, class_weights, difficult_categories):
                super().__init__()
                self.class_weights = class_weights
                self.difficult_categories = difficult_categories
                
            def forward(self, predictions, targets):
                # 这里是简化的损失函数示例
                # 实际实现需要根据YOLO的具体损失函数结构
                
                # 分类损失
                cls_loss = self.compute_classification_loss(predictions, targets)
                
                # 边界框损失
                box_loss = self.compute_box_loss(predictions, targets)
                
                # 目标性损失
                obj_loss = self.compute_objectness_loss(predictions, targets)
                
                # 困难类别奖励
                difficult_bonus = self.compute_difficult_category_bonus(predictions, targets)
                
                total_loss = cls_loss + box_loss + obj_loss + difficult_bonus
                
                return total_loss
            
            def compute_classification_loss(self, predictions, targets):
                # 应用类别权重的分类损失
                # 实际实现需要根据具体的YOLO版本
                pass
            
            def compute_box_loss(self, predictions, targets):
                # 边界框回归损失
                pass
            
            def compute_objectness_loss(self, predictions, targets):
                # 目标性损失
                pass
            
            def compute_difficult_category_bonus(self, predictions, targets):
                # 困难类别额外奖励
                pass
        
        return WeightedYOLOLoss(self.class_weights, self.difficult_categories)
    
    def generate_optimization_report(self, output_path: str = None):
        """生成优化策略报告"""
        report = {
            "optimization_strategy": "Class Weighted Optimization",
            "class_weights": self.class_weights,
            "difficult_categories": self.difficult_categories,
            "loss_weights": self.loss_weights,
            "optimization_techniques": {
                "focal_loss": "针对类别不平衡问题",
                "adaptive_weighting": "根据数据分布自适应调整",
                "difficult_category_enhancement": "困难类别特殊处理",
                "multi_scale_training": "多尺度训练提升小目标检测",
                "data_augmentation": "针对性数据增强"
            },
            "expected_improvements": {
                "CA002_牙刷": "预期提升30-40%",
                "CB001_果冻": "预期提升25-35%",
                "overall_mAP": "预期提升2-5%"
            }
        }
        
        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            print(f"📊 优化报告已保存: {output_path}")
        
        return report
    
    def apply_test_time_augmentation(self, model, image, tta_config: Dict = None):
        """测试时增强（TTA）"""
        if tta_config is None:
            tta_config = {
                'scales': [0.8, 1.0, 1.2],
                'flips': [False, True],
                'rotations': [0, 90, 180, 270]
            }
        
        predictions = []
        
        # 多尺度测试
        for scale in tta_config['scales']:
            # 多翻转测试
            for flip in tta_config['flips']:
                # 多旋转测试（可选）
                for rotation in tta_config.get('rotations', [0]):
                    # 应用变换
                    transformed_image = self._apply_tta_transform(
                        image, scale, flip, rotation
                    )
                    
                    # 推理
                    pred = model(transformed_image)
                    
                    # 逆变换预测结果
                    pred = self._inverse_tta_transform(
                        pred, scale, flip, rotation, image.shape
                    )
                    
                    predictions.append(pred)
        
        # 集成预测结果
        final_prediction = self._ensemble_predictions(predictions)
        
        return final_prediction
    
    def _apply_tta_transform(self, image, scale, flip, rotation):
        """应用TTA变换"""
        # 实现图像变换
        # 这里是简化版本，实际需要完整实现
        transformed = image.copy()
        
        # 缩放
        if scale != 1.0:
            h, w = image.shape[:2]
            new_h, new_w = int(h * scale), int(w * scale)
            transformed = cv2.resize(transformed, (new_w, new_h))
        
        # 翻转
        if flip:
            transformed = cv2.flip(transformed, 1)
        
        # 旋转
        if rotation != 0:
            h, w = transformed.shape[:2]
            center = (w // 2, h // 2)
            matrix = cv2.getRotationMatrix2D(center, rotation, 1.0)
            transformed = cv2.warpAffine(transformed, matrix, (w, h))
        
        return transformed
    
    def _inverse_tta_transform(self, prediction, scale, flip, rotation, original_shape):
        """逆变换预测结果"""
        # 实现预测结果的逆变换
        # 这里是简化版本
        return prediction
    
    def _ensemble_predictions(self, predictions):
        """集成多个预测结果"""
        # 实现预测结果集成
        # 可以使用NMS、投票等方法
        return predictions[0]  # 简化版本

def main():
    """测试函数"""
    optimizer = ClassWeightedOptimization()
    
    # 生成优化报告
    report = optimizer.generate_optimization_report("class_weighted_optimization_report.json")
    
    print("🎯 类别权重优化配置:")
    for class_id, weight in optimizer.class_weights.items():
        print(f"  类别 {class_id}: 权重 {weight}")
    
    print("\n🔥 困难类别优化策略:")
    for class_id, info in optimizer.difficult_categories.items():
        print(f"  {info['name']}:")
        print(f"    挑战: {', '.join(info['challenges'])}")
        print(f"    策略: {', '.join(info['optimization_strategies'])}")

if __name__ == "__main__":
    main()