"""
综合评估工具 - 我们的模型评估方法
"""
import json
import time
import numpy as np
from pathlib import Path
from ultralytics import YOL<PERSON>
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt
import seaborn as sns

class ComprehensiveEvaluator:
    """综合模型评估器"""
    
    def __init__(self, project_root: str = None):
        self.project_root = Path(project_root) if project_root else Path(__file__).parent.parent.parent
        self.results_dir = self.project_root / "evaluation_results"
        self.results_dir.mkdir(exist_ok=True)
        
        # 类别映射
        self.category_mapping = {
            0: 'CA001_衣架',
            1: 'CA002_牙刷',
            2: 'CB001_果冻',
            3: 'CB002_长方形状饼干',
            4: 'CC001_罐装饮料',
            5: 'CC002_瓶装饮料',
            6: 'CD001_香蕉',
            7: 'CD002_橙子',
            8: 'Wxxx_未知物品'
        }
        
        # 困难类别
        self.difficult_categories = ['CA002_牙刷', 'CB001_果冻']
        
        # 评估指标
        self.evaluation_metrics = [
            'mAP50', 'mAP50_95', 'precision', 'recall',
            'loading_time', 'inference_time'
        ]
        
        # 模型配置
        self.model_configs = {
            "基线_YOLOv11n": {
                "path": "competition_2025_models/competition_2025_yolo11n/weights/best.pt",
                "description": "基线模型"
            },
            "优化_YOLOv11n": {
                "path": "optimized_models/class_weighted_yolo11n_thin_small/weights/best.pt",
                "description": "类别权重优化模型"
            },
            "精细化_stage1": {
                "path": "refined_models/refined_yolo11n_stage1_balanced/weights/best.pt",
                "description": "精细化训练阶段1"
            },
            "精细化_stage2": {
                "path": "refined_models/refined_yolo11n_stage2_focused/weights/best.pt",
                "description": "精细化训练阶段2"
            },
            "优化_YOLOv11s": {
                "path": "optimized_models_s/class_weighted_yolo11s_thin_small/weights/best.pt",
                "description": "大模型优化版本"
            }
        }
    
    def evaluate_single_model(self, model_name: str, model_path: str, dataset_yaml: str) -> Dict:
        """评估单个模型"""
        print(f"🔍 评估模型: {model_name}")
        
        # 检查模型文件是否存在
        full_model_path = self.project_root / model_path
        if not full_model_path.exists():
            print(f"❌ 模型文件不存在: {full_model_path}")
            return None
        
        # 加载模型并测量加载时间
        start_time = time.time()
        try:
            model = YOLO(str(full_model_path))
            loading_time = time.time() - start_time
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            return None
        
        # 运行验证
        start_time = time.time()
        try:
            # 验证集评估
            val_results = model.val(
                data=dataset_yaml,
                split='val',
                save_json=False,
                verbose=False
            )
            
            # 测试集评估
            test_results = model.val(
                data=dataset_yaml,
                split='test',
                save_json=False,
                verbose=False
            )
            
            evaluation_time = time.time() - start_time
            
        except Exception as e:
            print(f"❌ 模型评估失败: {e}")
            return None
        
        # 提取指标
        val_metrics = self._extract_metrics(val_results)
        test_metrics = self._extract_metrics(test_results)
        
        # 获取类别特定指标
        val_class_metrics = self._extract_class_metrics(val_results)
        test_class_metrics = self._extract_class_metrics(test_results)
        
        # 构建结果
        result = {
            "model_name": model_name,
            "model_path": model_path,
            "loading_time": loading_time,
            "evaluation_time": evaluation_time,
            "validation_metrics": val_metrics,
            "test_metrics": test_metrics,
            "class_metrics": val_class_metrics,
            "test_class_metrics": test_class_metrics,
            "class_precision": self._get_class_precision(val_results),
            "class_recall": self._get_class_recall(val_results)
        }
        
        print(f"✅ {model_name} 评估完成")
        print(f"   验证集 mAP50: {val_metrics['mAP50']:.2f}%")
        print(f"   测试集 mAP50: {test_metrics['mAP50']:.2f}%")
        print(f"   加载时间: {loading_time:.3f}s")
        
        return result
    
    def _extract_metrics(self, results) -> Dict:
        """提取评估指标"""
        metrics = {}
        
        if hasattr(results, 'results_dict'):
            results_dict = results.results_dict
            metrics = {
                'mAP50': float(results_dict.get('metrics/mAP50(B)', 0)) * 100,
                'mAP50_95': float(results_dict.get('metrics/mAP50-95(B)', 0)) * 100,
                'precision': float(results_dict.get('metrics/precision(B)', 0)),
                'recall': float(results_dict.get('metrics/recall(B)', 0))
            }
        
        return metrics
    
    def _extract_class_metrics(self, results) -> Dict:
        """提取类别特定指标"""
        class_metrics = {}
        
        if hasattr(results, 'ap_class_index') and hasattr(results, 'ap'):
            ap_class_index = results.ap_class_index
            ap = results.ap
            
            # mAP50 for each class
            if len(ap.shape) > 1:
                ap50 = ap[:, 0]  # AP at IoU=0.5
                for i, class_idx in enumerate(ap_class_index):
                    class_name = self.category_mapping.get(int(class_idx), f'class_{class_idx}')
                    class_metrics[class_name] = float(ap50[i])
        
        return class_metrics
    
    def _get_class_precision(self, results) -> Dict:
        """获取类别精确率"""
        class_precision = {}
        
        if hasattr(results, 'p') and hasattr(results, 'ap_class_index'):
            precision = results.p
            ap_class_index = results.ap_class_index
            
            for i, class_idx in enumerate(ap_class_index):
                class_name = self.category_mapping.get(int(class_idx), f'class_{class_idx}')
                if i < len(precision):
                    class_precision[class_name] = float(precision[i])
        
        return class_precision
    
    def _get_class_recall(self, results) -> Dict:
        """获取类别召回率"""
        class_recall = {}
        
        if hasattr(results, 'r') and hasattr(results, 'ap_class_index'):
            recall = results.r
            ap_class_index = results.ap_class_index
            
            for i, class_idx in enumerate(ap_class_index):
                class_name = self.category_mapping.get(int(class_idx), f'class_{class_idx}')
                if i < len(recall):
                    class_recall[class_name] = float(recall[i])
        
        return class_recall
    
    def evaluate_all_models(self, dataset_yaml: str = None) -> Dict:
        """评估所有模型"""
        if dataset_yaml is None:
            dataset_yaml = str(self.project_root / "competition_2025_dataset" / "competition_dataset.yaml")
        
        print("🏆 开始综合模型评估")
        print("=" * 60)
        
        all_results = {}
        evaluation_summary = {
            "total_models_evaluated": 0,
            "evaluation_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "dataset_path": dataset_yaml
        }
        
        for model_name, config in self.model_configs.items():
            result = self.evaluate_single_model(model_name, config["path"], dataset_yaml)
            if result:
                all_results[model_name] = result
                evaluation_summary["total_models_evaluated"] += 1
            
            print("-" * 40)
        
        # 生成排名
        rankings = self._generate_rankings(all_results)
        
        # 生成对比分析
        comparison = self._generate_comparison_analysis(all_results)
        
        # 困难类别分析
        difficult_analysis = self._analyze_difficult_categories(all_results)
        
        # 完整结果
        comprehensive_results = {
            "evaluation_summary": evaluation_summary,
            "model_rankings": rankings,
            "detailed_results": all_results,
            "comparison_analysis": comparison,
            "difficult_categories_analysis": difficult_analysis
        }
        
        # 保存结果
        results_file = self.results_dir / "comprehensive_evaluation_results.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(comprehensive_results, f, indent=2, ensure_ascii=False)
        
        print(f"📊 综合评估结果已保存: {results_file}")
        
        # 生成可视化报告
        self._generate_visualization_report(comprehensive_results)
        
        return comprehensive_results
    
    def _generate_rankings(self, results: Dict) -> List[Dict]:
        """生成模型排名"""
        rankings = []
        
        for model_name, result in results.items():
            if result and 'validation_metrics' in result:
                val_metrics = result['validation_metrics']
                test_metrics = result['test_metrics']
                
                ranking_entry = {
                    "模型": model_name,
                    "验证集_mAP50": val_metrics.get('mAP50', 0) / 100,
                    "验证集_mAP50-95": val_metrics.get('mAP50_95', 0) / 100,
                    "验证集_精确率": val_metrics.get('precision', 0),
                    "验证集_召回率": val_metrics.get('recall', 0),
                    "测试集_mAP50": test_metrics.get('mAP50', 0) / 100,
                    "测试集_mAP50-95": test_metrics.get('mAP50_95', 0) / 100,
                    "测试集_精确率": test_metrics.get('precision', 0),
                    "测试集_召回率": test_metrics.get('recall', 0),
                    "加载时间(s)": result.get('loading_time', 0),
                    "评估时间(s)": result.get('evaluation_time', 0)
                }
                
                rankings.append(ranking_entry)
        
        # 按验证集mAP50排序
        rankings.sort(key=lambda x: x["验证集_mAP50"], reverse=True)
        
        return rankings
    
    def _generate_comparison_analysis(self, results: Dict) -> Dict:
        """生成对比分析"""
        if not results:
            return {}
        
        # 找到基线模型
        baseline_name = "基线_YOLOv11n"
        baseline_result = results.get(baseline_name)
        
        if not baseline_result:
            return {"error": "未找到基线模型"}
        
        baseline_map50 = baseline_result['validation_metrics']['mAP50']
        
        comparison = {
            "baseline_model": baseline_name,
            "baseline_mAP50": baseline_map50,
            "improvements": {}
        }
        
        for model_name, result in results.items():
            if model_name != baseline_name and result:
                current_map50 = result['validation_metrics']['mAP50']
                improvement = current_map50 - baseline_map50
                improvement_percent = (improvement / baseline_map50) * 100
                
                comparison["improvements"][model_name] = {
                    "mAP50": current_map50,
                    "absolute_improvement": improvement,
                    "relative_improvement_percent": improvement_percent
                }
        
        return comparison
    
    def _analyze_difficult_categories(self, results: Dict) -> Dict:
        """分析困难类别性能"""
        difficult_analysis = {
            "difficult_categories": self.difficult_categories,
            "model_performance": {}
        }
        
        for model_name, result in results.items():
            if result and 'class_metrics' in result:
                class_metrics = result['class_metrics']
                
                difficult_performance = {}
                for category in self.difficult_categories:
                    if category in class_metrics:
                        difficult_performance[category] = class_metrics[category]
                
                # 计算困难类别平均性能
                if difficult_performance:
                    avg_difficult = np.mean(list(difficult_performance.values()))
                    difficult_performance['average'] = avg_difficult
                
                difficult_analysis["model_performance"][model_name] = difficult_performance
        
        return difficult_analysis
    
    def _generate_visualization_report(self, results: Dict):
        """生成可视化报告"""
        try:
            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
            
            # 创建图表
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle('模型性能综合评估报告', fontsize=16, fontweight='bold')
            
            # 1. mAP50对比
            rankings = results['model_rankings']
            model_names = [r['模型'] for r in rankings]
            map50_scores = [r['验证集_mAP50'] * 100 for r in rankings]
            
            axes[0, 0].bar(model_names, map50_scores, color='skyblue')
            axes[0, 0].set_title('验证集 mAP50 对比')
            axes[0, 0].set_ylabel('mAP50 (%)')
            axes[0, 0].tick_params(axis='x', rotation=45)
            
            # 2. 加载时间对比
            loading_times = [r['加载时间(s)'] for r in rankings]
            axes[0, 1].bar(model_names, loading_times, color='lightcoral')
            axes[0, 1].set_title('模型加载时间对比')
            axes[0, 1].set_ylabel('加载时间 (秒)')
            axes[0, 1].tick_params(axis='x', rotation=45)
            
            # 3. 困难类别性能热力图
            difficult_data = results['difficult_categories_analysis']['model_performance']
            if difficult_data:
                heatmap_data = []
                heatmap_labels = []
                
                for model_name, performance in difficult_data.items():
                    row = []
                    for category in self.difficult_categories:
                        row.append(performance.get(category, 0))
                    heatmap_data.append(row)
                    heatmap_labels.append(model_name)
                
                sns.heatmap(heatmap_data, 
                           xticklabels=self.difficult_categories,
                           yticklabels=heatmap_labels,
                           annot=True, 
                           fmt='.2f',
                           cmap='YlOrRd',
                           ax=axes[1, 0])
                axes[1, 0].set_title('困难类别性能热力图')
            
            # 4. 精确率vs召回率散点图
            precisions = [r['验证集_精确率'] for r in rankings]
            recalls = [r['验证集_召回率'] for r in rankings]
            
            axes[1, 1].scatter(recalls, precisions, s=100, alpha=0.7)
            for i, name in enumerate(model_names):
                axes[1, 1].annotate(name, (recalls[i], precisions[i]), 
                                   xytext=(5, 5), textcoords='offset points')
            axes[1, 1].set_xlabel('召回率')
            axes[1, 1].set_ylabel('精确率')
            axes[1, 1].set_title('精确率 vs 召回率')
            axes[1, 1].grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            # 保存图表
            chart_file = self.results_dir / "evaluation_visualization.png"
            plt.savefig(chart_file, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"📈 可视化报告已保存: {chart_file}")
            
        except Exception as e:
            print(f"⚠️  可视化生成失败: {e}")

def main():
    """主函数"""
    evaluator = ComprehensiveEvaluator()
    
    # 运行综合评估
    results = evaluator.evaluate_all_models()
    
    # 打印总结
    if results and 'model_rankings' in results:
        print("\n🏆 模型排名 (按验证集mAP50):")
        for i, ranking in enumerate(results['model_rankings'], 1):
            print(f"{i}. {ranking['模型']}: {ranking['验证集_mAP50']*100:.2f}%")

if __name__ == "__main__":
    main()