#!/usr/bin/env python3
"""
Configuration Management Module
Unified management of all configuration parameters
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional

class CompetitionConfig:
    """比赛配置类"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        
        # 比赛基本信息
        self.COMPETITION_INFO = {
            "year": 2025,
            "platform": "香橙派 AI pro 8T",
            "memory_limit": "16GB",
            "camera": "奥比中光 Astra Pro Plus RGBD",
            "max_loading_time": 30,  # 秒
            "target_inference_time": 5,  # 秒
            "categories": 9  # 8个基本类别 + 未知物品
        }
        
        # 类别映射
        self.CATEGORY_MAPPING = {
            0: 'CA001_衣架',
            1: 'CA002_牙刷',
            2: 'CB001_果冻',
            3: 'CB002_长方形状饼干',
            4: 'CC001_罐装饮料',
            5: 'CC002_瓶装饮料',
            6: 'CD001_香蕉',
            7: 'CD002_橙子',
            8: 'Wxxx_未知物品'
        }
        
        # 类别简化映射（用于输出）
        self.CATEGORY_SHORT_MAPPING = {
            0: 'CA001',
            1: 'CA002',
            2: 'CB001',
            3: 'CB002',
            4: 'CC001',
            5: 'CC002',
            6: 'CD001',
            7: 'CD002',
            8: 'Wxxx'
        }
        
        # 困难类别
        self.DIFFICULT_CATEGORIES = ['CA002_牙刷', 'CB001_果冻']
        
        # 路径配置
        self.PATHS = {
            "project_root": self.project_root,
            "datasets": {
                "original_1": self.project_root / "dataset",
                "original_2": self.project_root / "dataset-20250723-coco",
                "competition": self.project_root / "competition_2025_dataset"
            },
            "models": {
                "baseline": self.project_root / "competition_2025_models",
                "optimized": self.project_root / "optimized_models",
                "refined": self.project_root / "refined_models",
                "optimized_s": self.project_root / "optimized_models_s"
            },
            "results": {
                "evaluation": self.project_root / "results" / "evaluation_reports",
                "training": self.project_root / "results" / "training_logs",
                "inference": self.project_root / "results" / "inference_results"
            }
        }
        
        # 训练配置
        self.TRAINING_CONFIG = {
            "base": {
                "epochs": 150,
                "batch": 16,
                "imgsz": 640,
                "optimizer": "AdamW",
                "lr0": 0.001,
                "lrf": 0.01,
                "patience": 30
            },
            "optimized": {
                "epochs": 250,
                "batch": 16,
                "imgsz": [640, 672, 704, 736, 768, 800],
                "class_weights": [1.0, 3.0, 3.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.5],
                "copy_paste": 0.3,
                "mosaic": 1.0,
                "mixup": 0.15
            },
            "refined": {
                "stage1": {
                    "epochs": 200,
                    "batch": 16,
                    "imgsz": [640, 672, 704],
                    "copy_paste": 0.1,
                    "patience": 40
                },
                "stage2": {
                    "epochs": 100,
                    "batch": 16,
                    "imgsz": 640,
                    "lr0": 0.0005,
                    "class_weights": [1.0, 1.5, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.2],
                    "patience": 20
                }
            }
        }
        
        # 推理配置
        self.INFERENCE_CONFIG = {
            "global_confidence": 0.5,
            "category_specific_confidence": {
                'CA001': 0.4,
                'CA002': 0.25,  # 牙刷 - 更低阈值
                'CB001': 0.3,   # 果冻 - 更低阈值
                'CB002': 0.4,
                'CC001': 0.5,
                'CC002': 0.5,
                'CD001': 0.5,
                'CD002': 0.5,
                'Wxxx': 0.35
            },
            "nms_thresholds": {
                'CA001': 0.5,
                'CA002': 0.3,   # 牙刷 - 更严格NMS
                'CB001': 0.4,   # 果冻 - 适中NMS
                'CB002': 0.5,
                'CC001': 0.5,
                'CC002': 0.5,
                'CD001': 0.5,
                'CD002': 0.5,
                'Wxxx': 0.4
            },
            "area_constraints": {
                'CA001': {'min_area': 500},
                'CA002': {'min_area': 200, 'aspect_ratio_range': (3, 15)},
                'CB001': {'min_area': 100, 'max_area': 2000},
                'CB002': {'min_area': 300},
                'CC001': {'min_area': 800},
                'CC002': {'min_area': 600},
                'CD001': {'min_area': 400},
                'CD002': {'min_area': 400},
                'Wxxx': {'min_area': 200}
            }
        }
        
        # 网络配置
        self.NETWORK_CONFIG = {
            "server_host": "*************",
            "server_port": 8080,
            "timeout": 10
        }
        
        # 模型推荐配置
        self.MODEL_RECOMMENDATIONS = {
            "highest_accuracy": {
                "name": "精细化_stage1",
                "path": "refined_models/refined_yolo11n_stage1_balanced/weights/best.pt",
                "description": "最高整体精度模型",
                "performance": {"mAP50": 95.54, "mAP50_95": 68.18}
            },
            "difficult_categories": {
                "name": "优化_YOLOv11s",
                "path": "optimized_models_s/class_weighted_yolo11s_thin_small/weights/best.pt",
                "description": "困难类别专用模型",
                "performance": {"difficult_avg": 40.56, "CA002": 43.47, "CB001": 37.64}
            },
            "balanced_performance": {
                "name": "优化_YOLOv11n",
                "path": "optimized_models/class_weighted_yolo11n_thin_small/weights/best.pt",
                "description": "平衡性能模型",
                "performance": {"mAP50": 94.47, "difficult_avg": 37.76}
            }
        }
        
    def get_model_path(self, model_type: str) -> Optional[Path]:
        """获取模型路径"""
        if model_type in self.MODEL_RECOMMENDATIONS:
            return self.project_root / self.MODEL_RECOMMENDATIONS[model_type]["path"]
        return None
        
    def get_dataset_config_path(self) -> Path:
        """获取数据集配置文件路径"""
        return self.PATHS["datasets"]["competition"] / "competition_dataset.yaml"
        
    def get_category_name(self, category_id: int) -> str:
        """根据ID获取类别名称"""
        return self.CATEGORY_MAPPING.get(category_id, "Unknown")
        
    def get_category_short_name(self, category_id: int) -> str:
        """根据ID获取类别简称"""
        return self.CATEGORY_SHORT_MAPPING.get(category_id, "Unknown")
        
    def is_difficult_category(self, category_name: str) -> bool:
        """判断是否为困难类别"""
        return category_name in self.DIFFICULT_CATEGORIES
        
    def get_confidence_threshold(self, category_short_name: str) -> float:
        """获取类别特定的置信度阈值"""
        return self.INFERENCE_CONFIG["category_specific_confidence"].get(
            category_short_name, 
            self.INFERENCE_CONFIG["global_confidence"]
        )
        
    def get_nms_threshold(self, category_short_name: str) -> float:
        """获取类别特定的NMS阈值"""
        return self.INFERENCE_CONFIG["nms_thresholds"].get(category_short_name, 0.5)
        
    def get_area_constraints(self, category_short_name: str) -> Dict[str, Any]:
        """获取类别特定的面积约束"""
        return self.INFERENCE_CONFIG["area_constraints"].get(category_short_name, {})
        
    def create_directories(self):
        """创建必要的目录"""
        for path_group in self.PATHS.values():
            if isinstance(path_group, dict):
                for path in path_group.values():
                    if isinstance(path, Path):
                        path.mkdir(parents=True, exist_ok=True)
            elif isinstance(path_group, Path):
                path_group.mkdir(parents=True, exist_ok=True)
                
    def save_config(self, filepath: Optional[Path] = None):
        """保存配置到文件"""
        import json
        
        if filepath is None:
            filepath = self.project_root / "config.json"
            
        config_dict = {
            "competition_info": self.COMPETITION_INFO,
            "category_mapping": self.CATEGORY_MAPPING,
            "training_config": self.TRAINING_CONFIG,
            "inference_config": self.INFERENCE_CONFIG,
            "model_recommendations": self.MODEL_RECOMMENDATIONS
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
            
    @classmethod
    def load_config(cls, filepath: Path):
        """从文件加载配置"""
        import json
        
        with open(filepath, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
            
        instance = cls()
        instance.COMPETITION_INFO.update(config_dict.get("competition_info", {}))
        instance.CATEGORY_MAPPING.update(config_dict.get("category_mapping", {}))
        instance.TRAINING_CONFIG.update(config_dict.get("training_config", {}))
        instance.INFERENCE_CONFIG.update(config_dict.get("inference_config", {}))
        instance.MODEL_RECOMMENDATIONS.update(config_dict.get("model_recommendations", {}))
        
        return instance

# 全局配置实例
config = CompetitionConfig()

# 便捷函数
def get_config() -> CompetitionConfig:
    """获取全局配置实例"""
    return config

def get_model_path(model_type: str) -> Optional[Path]:
    """获取模型路径的便捷函数"""
    return config.get_model_path(model_type)

def get_category_name(category_id: int) -> str:
    """获取类别名称的便捷函数"""
    return config.get_category_name(category_id)

def get_confidence_threshold(category_short_name: str) -> float:
    """获取置信度阈值的便捷函数"""
    return config.get_confidence_threshold(category_short_name)
