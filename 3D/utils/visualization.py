#!/usr/bin/env python3
"""
Visualization Tools Module
Provides detection result visualization, training curve plotting and other functions
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import seaborn as sns

# Set font for visualization
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class DetectionVisualizer:
    """Detection Result Visualizer"""
    
    def __init__(self):
        # Category color mapping
        self.colors = plt.cm.Set3(np.linspace(0, 1, 9))
        self.category_colors = {
            'CA001': self.colors[0],
            'CA002': self.colors[1],
            'CB001': self.colors[2],
            'CB002': self.colors[3],
            'CC001': self.colors[4],
            'CC002': self.colors[5],
            'CD001': self.colors[6],
            'CD002': self.colors[7],
            'Wxxx': self.colors[8]
        }

        # Category English names
        self.category_names_en = {
            'CA001': 'Hanger',
            'CA002': 'Toothbrush',
            'CB001': 'Jelly',
            'CB002': 'Biscuit',
            'CC001': 'Canned_Drink',
            'CC002': 'Bottled_Drink',
            'CD001': 'Banana',
            'CD002': 'Orange',
            'Wxxx': 'Unknown_Item'
        }
        
    def visualize_detections(self,
                           image: np.ndarray,
                           detections: List[Dict[str, Any]],
                           title: str = "Detection Results",
                           save_path: Optional[Path] = None,
                           show_confidence: bool = True,
                           show_category_id: bool = True) -> np.ndarray:
        """
        Visualize detection results

        Args:
            image: Input image (RGB format)
            detections: List of detection results
            title: Image title
            save_path: Save path
            show_confidence: Whether to show confidence
            show_category_id: Whether to show category ID

        Returns:
            Visualized image
        """
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        ax.imshow(image)
        
        for detection in detections:
            bbox = detection['bbox']
            x1, y1, x2, y2 = bbox
            width = x2 - x1
            height = y2 - y1
            
            # Get category information
            category_id = detection.get('category_id', 'Unknown')
            category_name = detection.get('category_name', 'Unknown')
            confidence = detection.get('confidence', 0.0)

            # Get color
            if isinstance(category_id, int):
                category_short = f"CA00{category_id+1}" if category_id < 2 else f"CB00{category_id-1}" if category_id < 4 else f"CC00{category_id-3}" if category_id < 6 else f"CD00{category_id-5}" if category_id < 8 else "Wxxx"
            else:
                category_short = str(category_id)

            color = self.category_colors.get(category_short, self.colors[0])

            # Draw bounding box
            rect = patches.Rectangle(
                (x1, y1), width, height,
                linewidth=2, edgecolor=color, facecolor='none'
            )
            ax.add_patch(rect)

            # Build label text
            label_parts = []
            if show_category_id:
                label_parts.append(category_short)

            en_name = self.category_names_en.get(category_short, category_short)
            label_parts.append(en_name)

            if show_confidence:
                label_parts.append(f"{confidence:.2f}")

            label = ": ".join(label_parts)

            # Add label
            ax.text(x1, y1-5, label,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor=color, alpha=0.7),
                   fontsize=10, color='black', weight='bold')
        
        ax.set_title(title, fontsize=14, weight='bold')
        ax.axis('off')
        
        # Add legend
        legend_elements = []
        for cat_short, color in self.category_colors.items():
            en_name = self.category_names_en.get(cat_short, cat_short)
            legend_elements.append(
                patches.Patch(color=color, label=f"{cat_short}: {en_name}")
            )

        ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1.15, 1))

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Visualization result saved: {save_path}")

        # Convert to numpy array
        fig.canvas.draw()
        vis_image = np.frombuffer(fig.canvas.tostring_rgb(), dtype=np.uint8)
        vis_image = vis_image.reshape(fig.canvas.get_width_height()[::-1] + (3,))
        
        plt.close(fig)
        return vis_image
        
    def compare_detections(self,
                          image: np.ndarray,
                          detections_list: List[Tuple[str, List[Dict[str, Any]]]],
                          save_path: Optional[Path] = None) -> np.ndarray:
        """
        Compare detection results from multiple models

        Args:
            image: Input image
            detections_list: [(model_name, detection_results), ...]
            save_path: Save path

        Returns:
            Comparison visualization image
        """
        num_models = len(detections_list)
        fig, axes = plt.subplots(1, num_models, figsize=(6*num_models, 6))
        
        if num_models == 1:
            axes = [axes]
            
        for i, (model_name, detections) in enumerate(detections_list):
            ax = axes[i]
            ax.imshow(image)
            
            # Count detections
            total_count = len(detections)
            difficult_count = sum(1 for d in detections
                                if d.get('category_name', '').startswith(('CA002', 'CB001')))

            ax.set_title(f"{model_name}\nTotal: {total_count}, Difficult: {difficult_count}")

            # Draw detection boxes
            for detection in detections:
                bbox = detection['bbox']
                x1, y1, x2, y2 = bbox
                width = x2 - x1
                height = y2 - y1
                
                category_id = detection.get('category_id', 0)
                confidence = detection.get('confidence', 0.0)
                
                color = self.colors[category_id % len(self.colors)]
                
                rect = patches.Rectangle(
                    (x1, y1), width, height,
                    linewidth=2, edgecolor=color, facecolor='none'
                )
                ax.add_patch(rect)
                
                # Simplified label
                label = f"{confidence:.2f}"
                ax.text(x1, y1-5, label,
                       bbox=dict(boxstyle="round,pad=0.2", facecolor=color, alpha=0.7),
                       fontsize=8, color='black')

            ax.axis('off')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Comparison visualization saved: {save_path}")

        # Convert to numpy array
        fig.canvas.draw()
        vis_image = np.frombuffer(fig.canvas.tostring_rgb(), dtype=np.uint8)
        vis_image = vis_image.reshape(fig.canvas.get_width_height()[::-1] + (3,))
        
        plt.close(fig)
        return vis_image

class TrainingVisualizer:
    """Training Process Visualizer"""
    
    def __init__(self):
        self.colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
        
    def plot_training_curves(self,
                           training_logs: Dict[str, List[float]],
                           title: str = "训练曲线",
                           save_path: Optional[Path] = None):
        """
        绘制训练曲线
        
        Args:
            training_logs: 训练日志 {'loss': [...], 'mAP50': [...], ...}
            title: 图表标题
            save_path: 保存路径
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        axes = axes.flatten()
        
        metrics = ['loss', 'mAP50', 'precision', 'recall']
        
        for i, metric in enumerate(metrics):
            if metric in training_logs:
                ax = axes[i]
                epochs = range(1, len(training_logs[metric]) + 1)
                ax.plot(epochs, training_logs[metric], 
                       color=self.colors[i], linewidth=2, label=metric)
                
                ax.set_xlabel('Epoch')
                ax.set_ylabel(metric)
                ax.set_title(f'{metric} 变化曲线')
                ax.grid(True, alpha=0.3)
                ax.legend()
        
        plt.suptitle(title, fontsize=16, weight='bold')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"训练曲线已保存: {save_path}")
        
        plt.show()
        
    def plot_model_comparison(self,
                            model_results: Dict[str, Dict[str, float]],
                            save_path: Optional[Path] = None):
        """
        绘制模型性能对比图
        
        Args:
            model_results: {'模型名': {'mAP50': 0.95, 'mAP50_95': 0.68, ...}, ...}
            save_path: 保存路径
        """
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        models = list(model_results.keys())
        
        # mAP50对比
        map50_scores = [model_results[m].get('mAP50', 0) for m in models]
        bars1 = ax1.bar(models, map50_scores, color=self.colors[:len(models)])
        ax1.set_title('mAP50 对比')
        ax1.set_ylabel('mAP50')
        ax1.tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for bar, score in zip(bars1, map50_scores):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{score:.3f}', ha='center', va='bottom')
        
        # mAP50-95对比
        map50_95_scores = [model_results[m].get('mAP50_95', 0) for m in models]
        bars2 = ax2.bar(models, map50_95_scores, color=self.colors[:len(models)])
        ax2.set_title('mAP50-95 对比')
        ax2.set_ylabel('mAP50-95')
        ax2.tick_params(axis='x', rotation=45)
        
        for bar, score in zip(bars2, map50_95_scores):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{score:.3f}', ha='center', va='bottom')
        
        # 精确率和召回率对比
        precision_scores = [model_results[m].get('precision', 0) for m in models]
        recall_scores = [model_results[m].get('recall', 0) for m in models]
        
        x = np.arange(len(models))
        width = 0.35
        
        bars3 = ax3.bar(x - width/2, precision_scores, width, label='精确率', alpha=0.8)
        bars4 = ax3.bar(x + width/2, recall_scores, width, label='召回率', alpha=0.8)
        
        ax3.set_title('精确率 vs 召回率')
        ax3.set_ylabel('分数')
        ax3.set_xticks(x)
        ax3.set_xticklabels(models, rotation=45)
        ax3.legend()
        
        # 推理时间对比（如果有）
        if any('inference_time' in model_results[m] for m in models):
            inference_times = [model_results[m].get('inference_time', 0) for m in models]
            bars5 = ax4.bar(models, inference_times, color='orange', alpha=0.8)
            ax4.set_title('推理时间对比')
            ax4.set_ylabel('时间 (秒)')
            ax4.tick_params(axis='x', rotation=45)
            
            for bar, time in zip(bars5, inference_times):
                ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                        f'{time:.3f}s', ha='center', va='bottom')
        else:
            ax4.axis('off')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"模型对比图已保存: {save_path}")
        
        plt.show()

class ConfusionMatrixVisualizer:
    """混淆矩阵可视化器"""
    
    def __init__(self, category_names: List[str]):
        self.category_names = category_names
        
    def plot_confusion_matrix(self,
                            confusion_matrix: np.ndarray,
                            title: str = "混淆矩阵",
                            save_path: Optional[Path] = None):
        """
        绘制混淆矩阵
        
        Args:
            confusion_matrix: 混淆矩阵
            title: 图表标题
            save_path: 保存路径
        """
        plt.figure(figsize=(10, 8))
        
        # 归一化混淆矩阵
        cm_normalized = confusion_matrix.astype('float') / confusion_matrix.sum(axis=1)[:, np.newaxis]
        
        # 绘制热力图
        sns.heatmap(cm_normalized, 
                   annot=True, 
                   fmt='.2f',
                   cmap='Blues',
                   xticklabels=self.category_names,
                   yticklabels=self.category_names)
        
        plt.title(title)
        plt.xlabel('预测类别')
        plt.ylabel('真实类别')
        plt.xticks(rotation=45)
        plt.yticks(rotation=0)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"混淆矩阵已保存: {save_path}")
        
        plt.show()

# 便捷函数
def visualize_detection_result(image_path: str, 
                             detections: List[Dict[str, Any]],
                             save_path: Optional[str] = None):
    """便捷的检测结果可视化函数"""
    image = cv2.imread(image_path)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    visualizer = DetectionVisualizer()
    return visualizer.visualize_detections(
        image, detections, 
        title=f"检测结果 - {Path(image_path).name}",
        save_path=Path(save_path) if save_path else None
    )
