#!/usr/bin/env python3
"""
2025年比赛RGB-D YOLO检测系统安装脚本
"""

from setuptools import setup, find_packages
import os
import sys

# 读取README文件
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# 读取requirements文件
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

# 版本信息
VERSION = "1.2.0"

setup(
    name="competition-2025-rgbd-yolo",
    version=VERSION,
    author="Competition Team",
    author_email="<EMAIL>",
    description="2025年比赛RGB-D YOLO目标检测系统",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/competition2025/rgbd-yolo",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Scientific/Engineering :: Image Recognition",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.5.0",
        ],
        "jupyter": [
            "jupyter>=1.0.0",
            "ipywidgets>=8.0.0",
        ],
        "profiling": [
            "line-profiler>=4.0.0",
            "memory-profiler>=0.61.0",
            "gpustat>=1.1.0",
        ],
        "tracking": [
            "wandb>=0.15.0",
            "tensorboard>=2.13.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "competition2025-train=training.competition_2025_training:main",
            "competition2025-eval=evaluation.comprehensive_model_evaluation:main",
            "competition2025-infer=inference.ultimate_competition_system:main",
            "competition2025-pipeline=run_complete_pipeline:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.yaml", "*.yml", "*.json", "*.txt", "*.md"],
    },
    zip_safe=False,
    keywords=[
        "computer vision",
        "object detection", 
        "YOLO",
        "RGB-D",
        "deep learning",
        "competition",
        "pytorch",
        "ultralytics"
    ],
    project_urls={
        "Bug Reports": "https://github.com/competition2025/rgbd-yolo/issues",
        "Source": "https://github.com/competition2025/rgbd-yolo",
        "Documentation": "https://competition2025.readthedocs.io/",
    },
)

# 安装后的设置
def post_install():
    """安装后的配置"""
    print("🎉 2025年比赛RGB-D YOLO检测系统安装完成!")
    print("\n📋 下一步:")
    print("1. 准备数据集: python data_preparation/competition_2025_dataset_preparation.py")
    print("2. 开始训练: python training/refined_training_system.py")
    print("3. 评估模型: python evaluation/comprehensive_model_evaluation.py")
    print("4. 运行推理: python inference/ultimate_competition_system.py")
    print("\n🚀 或者使用一键脚本: python run_complete_pipeline.py --mode full")
    print("\n📖 详细说明请查看 README.md")

if __name__ == "__main__":
    post_install()
