# 旋转增强比赛数据集配置
path: /home/<USER>/homes/ch/claude/SpatialVLA/3D/enhanced_competition_2025_dataset
train: images/train
val: images/val
test: images/test

# 扩展类别配置（包含W001-W008）
nc: 17
names: ['CA001_衣架', 'CA002_牙刷', 'CB001_果冻', 'CB002_长方形状饼干',
        'CC001_罐装饮料', 'CC002_瓶装饮料', 'CD001_香蕉', 'CD002_橙子',
        'W001_数学类书籍', 'W002_物理类书籍', 'W003_药盒类', 'W004_电子类',
        'W005_英语类书籍', 'W006_文学类书籍', 'W007_速食类', 'W008_文具类', 'Wxxx_未知物品']

# 旋转增强特定权重
class_weights: [1.0, 3.0, 3.0, 1.0, 1.0, 1.0, 1.0, 1.0,
                1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 2.0]

# 深度图像路径
depth_train: depth/train
depth_val: depth/val
depth_test: depth/test

# 旋转增强配置
rotation_augmentation:
  enabled: true
  degrees: 360.0
  probability: 0.8
  test_angles: [0, 45, 90, 135, 180, 225, 270, 315]

# 比赛特定配置
competition_format: true
rotation_scenario: true
output_format: "ID;Num"
