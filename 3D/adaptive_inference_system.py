#!/usr/bin/env python3
"""
自适应推理系统
解决置信度阈值和后处理优化问题，实现最佳检测性能
"""

import os
import time
import json
import cv2
import numpy as np
from pathlib import Path
from ultralytics import YOL<PERSON>
from typing import Dict, List, Tuple, Optional
import torch

class AdaptiveInferenceSystem:
    def __init__(self):
        self.models = {}
        self.performance_stats = {}
        
        # 比赛类别映射
        self.category_mapping = {
            0: 'CA001_衣架',
            1: 'CA002_牙刷', 
            2: 'CB001_果冻',
            3: 'CB002_长方形状饼干',
            4: 'CC001_罐装饮料',
            5: 'CC002_瓶装饮料',
            6: 'CD001_香蕉',
            7: 'CD002_橙子',
            8: 'Wxxx_未知物品'
        }
        
        # 类别特定配置
        self.category_config = {
            'CA001_衣架': {'confidence_threshold': 0.4, 'nms_threshold': 0.5, 'min_area': 500},
            'CA002_牙刷': {'confidence_threshold': 0.25, 'nms_threshold': 0.3, 'min_area': 200, 'aspect_ratio_range': (3, 15)},
            'CB001_果冻': {'confidence_threshold': 0.3, 'nms_threshold': 0.4, 'min_area': 100, 'max_area': 2000},
            'CB002_长方形状饼干': {'confidence_threshold': 0.4, 'nms_threshold': 0.5, 'min_area': 300},
            'CC001_罐装饮料': {'confidence_threshold': 0.5, 'nms_threshold': 0.5, 'min_area': 800},
            'CC002_瓶装饮料': {'confidence_threshold': 0.5, 'nms_threshold': 0.5, 'min_area': 600},
            'CD001_香蕉': {'confidence_threshold': 0.5, 'nms_threshold': 0.5, 'min_area': 400},
            'CD002_橙子': {'confidence_threshold': 0.5, 'nms_threshold': 0.5, 'min_area': 400},
            'Wxxx_未知物品': {'confidence_threshold': 0.35, 'nms_threshold': 0.4, 'min_area': 200}
        }
        
        # 困难类别
        self.difficult_categories = ['CA002_牙刷', 'CB001_果冻']
        
    def load_models(self):
        """加载所有可用模型"""
        model_paths = {
            "基线_YOLOv11n": "competition_2025_models/competition_2025_yolo11n/weights/best.pt",
            "优化_YOLOv11n": "optimized_models/class_weighted_yolo11n_thin_small/weights/best.pt",
            "精细化_YOLOv11n_stage1": "refined_models/refined_yolo11n_stage1_balanced/weights/best.pt",
            "精细化_YOLOv11n_stage2": "refined_models/refined_yolo11n_stage2_focused/weights/best.pt"
        }
        
        for model_name, model_path in model_paths.items():
            if os.path.exists(model_path):
                print(f"加载模型: {model_name}")
                start_time = time.time()
                self.models[model_name] = YOLO(model_path)
                loading_time = time.time() - start_time
                print(f"  加载时间: {loading_time:.3f}s")
                
                # 预热模型
                dummy_image = np.zeros((640, 640, 3), dtype=np.uint8)
                _ = self.models[model_name](dummy_image, verbose=False)
            else:
                print(f"模型不存在: {model_path}")
                
        print(f"\n成功加载 {len(self.models)} 个模型")
        
    def adaptive_inference(self, image, model_name="auto", use_ensemble=False):
        """自适应推理"""
        if model_name == "auto":
            # 自动选择最佳模型
            model_name = self.select_best_model()
            
        if model_name not in self.models:
            raise ValueError(f"模型不存在: {model_name}")
            
        if use_ensemble and len(self.models) > 1:
            return self.ensemble_inference(image)
        else:
            return self.single_model_inference(image, model_name)
            
    def single_model_inference(self, image, model_name):
        """单模型自适应推理"""
        model = self.models[model_name]
        
        # 使用较低的全局置信度阈值进行初始检测
        start_time = time.time()
        results = model(image, conf=0.1, verbose=False)  # 使用很低的阈值
        inference_time = time.time() - start_time
        
        # 解析原始检测结果
        raw_detections = []
        if results[0].boxes is not None:
            boxes = results[0].boxes.xyxy.cpu().numpy()
            confidences = results[0].boxes.conf.cpu().numpy()
            classes = results[0].boxes.cls.cpu().numpy().astype(int)
            
            for box, conf, cls in zip(boxes, confidences, classes):
                category_name = self.category_mapping.get(cls, 'Unknown')
                
                detection = {
                    'category_id': cls,
                    'category_name': category_name,
                    'confidence': float(conf),
                    'bbox': box.tolist(),
                    'area': (box[2] - box[0]) * (box[3] - box[1]),
                    'aspect_ratio': (box[2] - box[0]) / max(box[3] - box[1], 1e-6)
                }
                raw_detections.append(detection)
        
        # 应用类别特定的过滤和后处理
        filtered_detections = self.apply_category_specific_filtering(raw_detections)
        
        # 应用自定义NMS
        final_detections = self.apply_adaptive_nms(filtered_detections)
        
        return {
            'detections': final_detections,
            'inference_time': inference_time,
            'model_used': model_name,
            'raw_count': len(raw_detections),
            'filtered_count': len(filtered_detections),
            'final_count': len(final_detections)
        }
        
    def apply_category_specific_filtering(self, detections):
        """应用类别特定的过滤规则"""
        filtered = []
        
        for detection in detections:
            category_name = detection['category_name']
            if category_name not in self.category_config:
                continue
                
            config = self.category_config[category_name]
            
            # 置信度过滤
            if detection['confidence'] < config['confidence_threshold']:
                continue
                
            # 面积过滤
            area = detection['area']
            if 'min_area' in config and area < config['min_area']:
                continue
            if 'max_area' in config and area > config['max_area']:
                continue
                
            # 长宽比过滤（主要针对牙刷）
            if 'aspect_ratio_range' in config:
                aspect_ratio = detection['aspect_ratio']
                min_ratio, max_ratio = config['aspect_ratio_range']
                if not (min_ratio <= aspect_ratio <= max_ratio):
                    continue
                    
            filtered.append(detection)
            
        return filtered
        
    def apply_adaptive_nms(self, detections):
        """应用自适应NMS"""
        if not detections:
            return []
            
        # 按类别分组
        category_groups = {}
        for detection in detections:
            category = detection['category_name']
            if category not in category_groups:
                category_groups[category] = []
            category_groups[category].append(detection)
        
        final_detections = []
        
        # 对每个类别单独应用NMS
        for category, group_detections in category_groups.items():
            if category not in self.category_config:
                final_detections.extend(group_detections)
                continue
                
            nms_threshold = self.category_config[category]['nms_threshold']
            
            # 转换为torch tensor进行NMS
            if len(group_detections) > 1:
                boxes = torch.tensor([d['bbox'] for d in group_detections])
                scores = torch.tensor([d['confidence'] for d in group_detections])
                
                # 应用NMS
                keep_indices = torch.ops.torchvision.nms(boxes, scores, nms_threshold)
                
                # 保留NMS后的检测结果
                for idx in keep_indices:
                    final_detections.append(group_detections[idx])
            else:
                final_detections.extend(group_detections)
                
        return final_detections
        
    def ensemble_inference(self, image):
        """集成推理"""
        all_detections = []
        inference_times = []
        
        # 收集所有模型的检测结果
        for model_name, model in self.models.items():
            result = self.single_model_inference(image, model_name)
            
            # 为每个检测添加模型来源信息
            for detection in result['detections']:
                detection['source_model'] = model_name
                detection['model_confidence'] = self.get_model_confidence_weight(model_name)
                
            all_detections.extend(result['detections'])
            inference_times.append(result['inference_time'])
        
        # 集成后处理
        ensemble_detections = self.ensemble_postprocessing(all_detections)
        
        return {
            'detections': ensemble_detections,
            'inference_time': max(inference_times),  # 并行推理时间
            'model_used': 'ensemble',
            'individual_counts': {model: len([d for d in all_detections if d.get('source_model') == model]) 
                                for model in self.models.keys()},
            'final_count': len(ensemble_detections)
        }
        
    def ensemble_postprocessing(self, all_detections):
        """集成后处理"""
        if not all_detections:
            return []
            
        # 按类别分组
        category_groups = {}
        for detection in all_detections:
            category = detection['category_name']
            if category not in category_groups:
                category_groups[category] = []
            category_groups[category].append(detection)
        
        final_detections = []
        
        for category, detections in category_groups.items():
            # 对于困难类别，使用投票机制
            if category in self.difficult_categories:
                category_detections = self.voting_based_fusion(detections)
            else:
                # 对于容易类别，使用置信度加权
                category_detections = self.confidence_weighted_fusion(detections)
                
            final_detections.extend(category_detections)
            
        return final_detections
        
    def voting_based_fusion(self, detections):
        """基于投票的融合（用于困难类别）"""
        if not detections:
            return []
            
        # 简化版本：选择置信度最高的检测结果
        # 实际应用中可以实现更复杂的投票机制
        detections.sort(key=lambda x: x['confidence'], reverse=True)
        
        # 应用NMS去除重复
        final_detections = []
        for detection in detections:
            is_duplicate = False
            for existing in final_detections:
                if self.calculate_iou(detection['bbox'], existing['bbox']) > 0.5:
                    is_duplicate = True
                    break
            if not is_duplicate:
                final_detections.append(detection)
                
        return final_detections
        
    def confidence_weighted_fusion(self, detections):
        """基于置信度的加权融合"""
        return self.voting_based_fusion(detections)  # 简化实现
        
    def calculate_iou(self, box1, box2):
        """计算IoU"""
        x1, y1, x2, y2 = box1
        x1_2, y1_2, x2_2, y2_2 = box2
        
        # 计算交集
        xi1 = max(x1, x1_2)
        yi1 = max(y1, y1_2)
        xi2 = min(x2, x2_2)
        yi2 = min(y2, y2_2)
        
        if xi2 <= xi1 or yi2 <= yi1:
            return 0
            
        inter_area = (xi2 - xi1) * (yi2 - yi1)
        
        # 计算并集
        box1_area = (x2 - x1) * (y2 - y1)
        box2_area = (x2_2 - x1_2) * (y2_2 - y1_2)
        union_area = box1_area + box2_area - inter_area
        
        return inter_area / union_area if union_area > 0 else 0
        
    def select_best_model(self):
        """选择最佳模型"""
        # 优先级顺序
        priority_order = [
            "精细化_YOLOv11n_stage2",
            "精细化_YOLOv11n_stage1", 
            "优化_YOLOv11n",
            "基线_YOLOv11n"
        ]
        
        for model_name in priority_order:
            if model_name in self.models:
                return model_name
                
        # 如果都不存在，返回第一个可用模型
        return list(self.models.keys())[0] if self.models else None
        
    def get_model_confidence_weight(self, model_name):
        """获取模型置信度权重"""
        weights = {
            "精细化_YOLOv11n_stage2": 1.0,
            "精细化_YOLOv11n_stage1": 0.9,
            "优化_YOLOv11n": 0.8,
            "基线_YOLOv11n": 0.7
        }
        return weights.get(model_name, 0.5)
        
    def count_objects(self, detections):
        """统计各类别物品数量"""
        counts = {}
        for detection in detections:
            category = detection['category_name']
            counts[category] = counts.get(category, 0) + 1
        return counts
        
    def format_competition_output(self, counts):
        """格式化比赛输出格式"""
        category_order = ['CA001', 'CA002', 'CB001', 'CB002', 'CC001', 'CC002', 'CD001', 'CD002', 'Wxxx']
        output_lines = []
        
        for category in category_order:
            # 匹配完整的类别名称
            full_category_name = None
            for full_name in counts.keys():
                if full_name.startswith(category):
                    full_category_name = full_name
                    break
                    
            count = counts.get(full_category_name, 0) if full_category_name else 0
            output_lines.append(f"{category};{count}")
            
        return '\n'.join(output_lines)

def main():
    """主函数 - 演示自适应推理系统"""
    print("=== 自适应推理系统测试 ===")
    
    # 创建推理系统
    inference_system = AdaptiveInferenceSystem()
    
    # 加载模型
    inference_system.load_models()
    
    if not inference_system.models:
        print("没有可用的模型")
        return
    
    # 测试图像
    test_image_dir = Path("competition_2025_dataset/images/test")
    if test_image_dir.exists():
        test_images = list(test_image_dir.glob("*.jpg"))[:5]
        
        print(f"\n开始测试 {len(test_images)} 张图像...")
        
        for i, img_path in enumerate(test_images, 1):
            print(f"\n--- 图像 {i}: {img_path.name} ---")
            
            # 加载图像
            image = cv2.imread(str(img_path))
            if image is None:
                continue
                
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # 测试不同推理模式
            modes = [
                ("自动选择", "auto", False),
                ("集成推理", "auto", True)
            ]
            
            for mode_name, model_name, use_ensemble in modes:
                try:
                    result = inference_system.adaptive_inference(
                        image_rgb, model_name=model_name, use_ensemble=use_ensemble
                    )
                    
                    counts = inference_system.count_objects(result['detections'])
                    
                    print(f"  {mode_name}:")
                    print(f"    模型: {result['model_used']}")
                    print(f"    推理时间: {result['inference_time']:.3f}s")
                    print(f"    检测数量: {result['final_count']}")
                    
                    # 显示困难类别检测情况
                    for difficult_cat in inference_system.difficult_categories:
                        count = sum(1 for d in result['detections'] 
                                  if d['category_name'] == difficult_cat)
                        if count > 0:
                            print(f"    {difficult_cat}: {count}个")
                            
                except Exception as e:
                    print(f"    {mode_name} 失败: {e}")
    
    print("\n=== 测试完成 ===")
    print("自适应推理系统已就绪，可用于比赛环境")

if __name__ == "__main__":
    main()
