#!/usr/bin/env python3
"""
将COCO格式的标注转换为YOLO格式
为YOLO11x训练准备数据
"""

import json
import os
from pathlib import Path
from PIL import Image
import yaml

def coco_to_yolo_bbox(coco_bbox, img_width, img_height):
    """将COCO格式的边界框转换为YOLO格式"""
    x, y, width, height = coco_bbox
    
    # 转换为中心点坐标
    x_center = x + width / 2
    y_center = y + height / 2
    
    # 归一化到0-1
    x_center /= img_width
    y_center /= img_height
    width /= img_width
    height /= img_height
    
    return [x_center, y_center, width, height]

def convert_coco_to_yolo(coco_file, images_dir, output_dir):
    """转换COCO标注文件到YOLO格式"""
    print(f"转换 {coco_file} 到YOLO格式...")
    
    # 读取COCO标注文件
    with open(coco_file, 'r', encoding='utf-8') as f:
        coco_data = json.load(f)
    
    # 创建输出目录
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建图像ID到文件名的映射
    image_info = {}
    for img in coco_data['images']:
        image_info[img['id']] = {
            'file_name': img['file_name'],
            'width': img['width'],
            'height': img['height']
        }
    
    # 按图像ID分组标注
    annotations_by_image = {}
    for ann in coco_data['annotations']:
        image_id = ann['image_id']
        if image_id not in annotations_by_image:
            annotations_by_image[image_id] = []
        annotations_by_image[image_id].append(ann)
    
    converted_count = 0
    
    # 为每个图像创建YOLO标注文件
    for image_id, img_info in image_info.items():
        file_name = img_info['file_name']
        img_width = img_info['width']
        img_height = img_info['height']
        
        # 检查图像文件是否存在
        img_path = images_dir / file_name
        if not img_path.exists():
            print(f"警告: 图像文件不存在 {img_path}")
            continue
        
        # 创建YOLO标注文件
        yolo_file = output_dir / f"{Path(file_name).stem}.txt"
        
        yolo_lines = []
        if image_id in annotations_by_image:
            for ann in annotations_by_image[image_id]:
                class_id = ann['category_id']
                coco_bbox = ann['bbox']
                
                # 转换边界框格式
                yolo_bbox = coco_to_yolo_bbox(coco_bbox, img_width, img_height)
                
                # 创建YOLO格式的行
                yolo_line = f"{class_id} {' '.join(map(str, yolo_bbox))}"
                yolo_lines.append(yolo_line)
        
        # 写入YOLO标注文件
        with open(yolo_file, 'w') as f:
            f.write('\n'.join(yolo_lines))
        
        converted_count += 1
    
    print(f"转换完成: {converted_count} 个标注文件")
    return converted_count

def create_yolo_dataset_structure():
    """创建YOLO格式的数据集结构"""
    print("=== 创建YOLO格式数据集 ===")
    
    # 路径定义
    merged_dataset = Path("/home/<USER>/claude/SpatialVLA/3D/merged_rgbd_dataset")
    yolo_dataset = Path("/home/<USER>/claude/SpatialVLA/3D/yolo11x/dataset")
    
    # 创建YOLO数据集目录结构
    for split in ['train', 'val', 'test']:
        (yolo_dataset / 'images' / split).mkdir(parents=True, exist_ok=True)
        (yolo_dataset / 'labels' / split).mkdir(parents=True, exist_ok=True)
    
    print(f"创建YOLO数据集目录: {yolo_dataset}")
    
    # 复制图像文件并转换标注
    for split in ['train', 'val', 'test']:
        print(f"\n处理 {split} 数据...")
        
        # 源路径
        src_images = merged_dataset / 'images' / split
        src_labels = merged_dataset / 'labels' / 'train' / 'annotations.json'  # 所有标注都在train中
        
        # 目标路径
        dst_images = yolo_dataset / 'images' / split
        dst_labels = yolo_dataset / 'labels' / split
        
        if not src_images.exists():
            print(f"跳过 {split}: 源目录不存在")
            continue
        
        # 复制图像文件
        image_count = 0
        for img_file in src_images.glob('*.jpg'):
            dst_img = dst_images / img_file.name
            if not dst_img.exists():
                import shutil
                shutil.copy2(img_file, dst_img)
                image_count += 1
        
        print(f"复制了 {image_count} 张图像")
        
        # 转换标注文件（只对train数据转换，val和test暂时为空）
        if split == 'train' and src_labels.exists():
            convert_coco_to_yolo(src_labels, dst_images, dst_labels)
        elif split in ['val', 'test']:
            # 为val和test创建空的标注文件
            for img_file in dst_images.glob('*.jpg'):
                label_file = dst_labels / f"{img_file.stem}.txt"
                if not label_file.exists():
                    label_file.touch()
    
    return yolo_dataset

def create_yolo_config(yolo_dataset):
    """创建YOLO训练配置文件"""
    print("\n=== 创建YOLO配置文件 ===")
    
    config = {
        'path': str(yolo_dataset),
        'train': 'images/train',
        'val': 'images/val',
        'test': 'images/test',
        'nc': 9,
        'names': [
            'person',
            'bicycle', 
            'car',
            'motorcycle',
            'airplane',
            'bus',
            'train',
            'truck',
            'boat'
        ]
    }
    
    config_file = yolo_dataset / 'dataset.yaml'
    with open(config_file, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    print(f"YOLO配置文件创建: {config_file}")
    return config_file

def main():
    """主函数"""
    print("开始转换数据集为YOLO格式...")
    
    # 1. 创建YOLO数据集结构
    yolo_dataset = create_yolo_dataset_structure()
    
    # 2. 创建YOLO配置文件
    config_file = create_yolo_config(yolo_dataset)
    
    # 3. 验证转换结果
    print("\n=== 验证转换结果 ===")
    for split in ['train', 'val', 'test']:
        images_dir = yolo_dataset / 'images' / split
        labels_dir = yolo_dataset / 'labels' / split
        
        img_count = len(list(images_dir.glob('*.jpg'))) if images_dir.exists() else 0
        label_count = len(list(labels_dir.glob('*.txt'))) if labels_dir.exists() else 0
        
        print(f"{split}: {img_count} 图像, {label_count} 标注文件")
    
    print(f"\n✅ YOLO格式数据集准备完成!")
    print(f"数据集路径: {yolo_dataset}")
    print(f"配置文件: {config_file}")

if __name__ == "__main__":
    main()
