#!/usr/bin/env python3
"""
安装YOLOv12s训练环境所需的依赖包
"""

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        print(f"安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("YOLOv12s 环境依赖安装脚本")
    print("=" * 60)
    
    # 必需的包列表
    required_packages = [
        "torch",
        "torchvision", 
        "ultralytics",
        "opencv-python",
        "pyyaml",
        "matplotlib",
        "pillow",
        "numpy",
        "scipy"
    ]
    
    print("将安装以下包:")
    for pkg in required_packages:
        print(f"  - {pkg}")
    
    response = input("\n继续安装? (y/n): ")
    if response.lower() != 'y':
        print("安装取消")
        return
    
    # 安装包
    success_count = 0
    for package in required_packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n安装完成: {success_count}/{len(required_packages)} 个包安装成功")
    
    if success_count == len(required_packages):
        print("✓ 所有依赖安装成功!")
        print("\n下一步:")
        print("1. 运行数据转换: python scripts/convert_coco_to_yolo.py")
        print("2. 分割训练验证集: python scripts/split_train_val.py") 
        print("3. 开始训练: python scripts/train.py")
    else:
        print("⚠ 部分依赖安装失败，请手动安装缺失的包")

if __name__ == "__main__":
    main()