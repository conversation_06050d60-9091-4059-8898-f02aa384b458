#!/bin/bash

# YOLOv12s RGB训练环境设置脚本
# 确保在efficientdet conda环境中运行

echo "YOLOv12s RGB训练环境设置"
echo "=========================="

# 检查conda环境
echo "检查conda环境..."
if [[ "$CONDA_DEFAULT_ENV" != "efficientdet" ]]; then
    echo "警告: 当前不在efficientdet环境中"
    echo "请先激活环境: source /home/<USER>/jing_audio/anaconda3/bin/activate efficientdet"
    echo "然后重新运行此脚本"
    exit 1
fi

echo "✓ 当前conda环境: $CONDA_DEFAULT_ENV"

# 检查Python版本
echo "检查Python版本..."
python_version=$(python --version 2>&1)
echo "✓ $python_version"

# 检查CUDA
echo "检查CUDA..."
if command -v nvidia-smi &> /dev/null; then
    echo "✓ NVIDIA GPU可用:"
    nvidia-smi --query-gpu=name,memory.total --format=csv,noheader,nounits
else
    echo "⚠ 未检测到NVIDIA GPU，将使用CPU训练"
fi

# 安装/更新必要的包
echo "安装/更新必要的包..."

# 更新pip
echo "更新pip..."
pip install --upgrade pip

# 安装ultralytics (支持YOLOv12)
echo "安装ultralytics..."
pip install ultralytics

# 安装其他必要的包
echo "安装其他依赖包..."
pip install opencv-python
pip install Pillow
pip install matplotlib
pip install seaborn
pip install pandas
pip install tqdm

# 检查PyTorch
echo "检查PyTorch..."
python -c "import torch; print(f'PyTorch版本: {torch.__version__}'); print(f'CUDA可用: {torch.cuda.is_available()}')"

# 检查ultralytics
echo "检查ultralytics..."
python -c "import ultralytics; print(f'Ultralytics版本: {ultralytics.__version__}')"

# 创建必要的目录
echo "创建工作目录..."
mkdir -p /home/<USER>/claude/SpatialVLA/3D/yolo12s/rgb_dataset
mkdir -p /home/<USER>/claude/SpatialVLA/3D/yolo12s/runs
mkdir -p /home/<USER>/claude/SpatialVLA/3D/yolo12s/logs

echo "✓ 目录创建完成"

# 检查源数据集
echo "检查源数据集..."
source_dataset="/home/<USER>/claude/SpatialVLA/3D/merged_rgbd_dataset"
if [ -d "$source_dataset" ]; then
    echo "✓ 源数据集存在: $source_dataset"
    
    # 统计数据集信息
    train_images=$(find "$source_dataset/images/train" -name "*.jpg" | wc -l)
    val_images=$(find "$source_dataset/images/val" -name "*.jpg" | wc -l)
    test_images=$(find "$source_dataset/images/test" -name "*.jpg" | wc -l)
    
    echo "  训练图像: $train_images"
    echo "  验证图像: $val_images"
    echo "  测试图像: $test_images"
else
    echo "✗ 源数据集不存在: $source_dataset"
    exit 1
fi

echo ""
echo "=========================="
echo "环境设置完成！"
echo ""
echo "下一步操作:"
echo "1. 运行数据转换和训练: python run_training.py"
echo "2. 或者分步执行:"
echo "   - 转换数据集: python coco_to_yolo_converter.py"
echo "   - 训练模型: python train_yolo12s.py"
echo ""
echo "训练完成后，结果将保存在:"
echo "- 数据集: /home/<USER>/claude/SpatialVLA/3D/yolo12s/rgb_dataset"
echo "- 模型: /home/<USER>/claude/SpatialVLA/3D/yolo12s/runs"
echo "=========================="
