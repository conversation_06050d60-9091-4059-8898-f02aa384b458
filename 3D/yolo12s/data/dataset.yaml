# YOLOv12s 数据集配置文件
# 基于merged_rgbd_dataset的RGB图像训练

# 数据集路径
path: /home/<USER>/claude/SpatialVLA/3D/yolo12s/data
train: images/train
val: images/val
test: images/test

# 类别数量
nc: 9

# 类别名称 (与COCO标注中的category_id对应)
names:
  0: 'CA001_衣架'
  1: 'CA002_牙刷'
  2: 'CB001_果冻'
  3: 'CB002_长方形状饼干'
  4: 'CC001_罐装饮料'
  5: 'CC002_瓶装饮料'
  6: 'CD001_香蕉'
  7: 'CD002_橙子'
  8: 'Wxxx_未知物品'

# 数据集信息
dataset_info:
  name: "Competition 2025 RGB Dataset"
  description: "3D识别比赛数据集 - 仅使用RGB图像"
  version: "1.0"
  total_images: 1711
  total_classes: 9
  
  # 数据分布
  splits:
    train: 1419
    val: 194
    test: 98
  
  # 比赛物品类别说明
  categories:
    CA: "日用品"
    CB: "副食品"
    CC: "饮料"
    CD: "水果"
    W: "未知物品"

# 训练建议配置
training_config:
  image_size: 640
  batch_size: 16
  epochs: 100
  learning_rate: 0.01
  
  # 数据增强
  augmentation:
    hsv_h: 0.015
    hsv_s: 0.7
    hsv_v: 0.4
    degrees: 0.0
    translate: 0.1
    scale: 0.5
    shear: 0.0
    perspective: 0.0
    flipud: 0.0
    fliplr: 0.5
    mosaic: 1.0
    mixup: 0.0