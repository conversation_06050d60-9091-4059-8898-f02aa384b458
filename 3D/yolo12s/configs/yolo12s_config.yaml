# YOLOv12s 训练配置文件
# 针对3D识别比赛优化的配置

# 模型配置
model: 'yolo11s.pt'  # 使用YOLOv11s作为基础模型 (YOLOv12s暂未发布)

# 数据配置
data: '/home/<USER>/claude/SpatialVLA/3D/yolo12s/data/dataset.yaml'

# 训练超参数
epochs: 100
imgsz: 640
batch: 16
lr0: 0.01          # 初始学习率
lrf: 0.01          # 最终学习率 (lr0 * lrf)
momentum: 0.937    # SGD动量/Adam beta1
weight_decay: 0.0005  # 优化器权重衰减
warmup_epochs: 3.0    # 预热轮数
warmup_momentum: 0.8  # 预热动量
warmup_bias_lr: 0.1   # 预热偏置学习率

# 数据增强配置
hsv_h: 0.015       # 色调增强
hsv_s: 0.7         # 饱和度增强
hsv_v: 0.4         # 明度增强
degrees: 0.0       # 旋转角度 (±deg)
translate: 0.1     # 平移 (±fraction)
scale: 0.5         # 缩放增益 (±gain)
shear: 0.0         # 剪切角度 (±deg)
perspective: 0.0   # 透视变换 (±fraction)
flipud: 0.0        # 垂直翻转概率
fliplr: 0.5        # 水平翻转概率
mosaic: 1.0        # Mosaic增强概率
mixup: 0.0         # MixUp增强概率
copy_paste: 0.0    # Copy-paste增强概率

# 损失函数权重
box: 7.5           # 边界框损失权重
cls: 0.5           # 分类损失权重
dfl: 1.5           # DFL损失权重

# 训练设置
device: 'auto'     # 设备选择 ('cpu', '0', '1', 'auto')
workers: 8         # 数据加载器工作进程数
patience: 50       # 早停耐心值
save_period: 10    # 模型保存间隔 (epochs)
val: true          # 训练期间验证
plots: true        # 保存训练图表
verbose: true      # 详细输出

# 优化器设置
optimizer: 'auto'  # 优化器 ('SGD', 'Adam', 'AdamW', 'auto')
close_mosaic: 10   # 最后N个epoch关闭mosaic增强

# NMS设置
iou: 0.7          # NMS IoU阈值
conf: 0.001       # 目标置信度阈值

# 验证设置
val_iou: 0.6      # 验证时NMS IoU阈值
val_conf: 0.001   # 验证时置信度阈值

# 比赛特定优化
competition_optimizations:
  # 模型大小优化
  model_pruning: false      # 模型剪枝
  quantization: false       # 量化
  
  # 推理速度优化
  half_precision: true      # 半精度推理
  optimize_for_mobile: false # 移动端优化
  
  # 精度优化
  test_time_augmentation: false  # 测试时增强
  multi_scale_training: true     # 多尺度训练
  
  # 类别平衡
  class_weights: null       # 类别权重 (自动计算)
  focal_loss: false         # 使用Focal Loss

# 回调函数配置
callbacks:
  tensorboard: true         # TensorBoard日志
  wandb: false             # Weights & Biases
  clearml: false           # ClearML

# 项目设置
project: '/home/<USER>/claude/SpatialVLA/3D/yolo12s/runs'
name: 'yolo12s_competition'

# 高级设置
amp: true                 # 自动混合精度训练
fraction: 1.0             # 数据集使用比例
profile: false            # 性能分析
freeze: null              # 冻结层数 (0-backbone冻结)

# 数据集特定配置
dataset_specific:
  # 针对比赛数据的特殊处理
  small_objects: true      # 小目标优化
  dense_scenes: true       # 密集场景优化
  occlusion_handling: true # 遮挡处理
  
  # 类别特定配置
  class_specific_augmentation:
    'CA001_衣架': {'rotate': 15}      # 衣架可以旋转更多
    'CB001_果冻': {'scale': 0.3}      # 果冻尺度变化较大
    'CD001_香蕉': {'hsv_h': 0.02}     # 香蕉颜色变化
    'CD002_橙子': {'hsv_s': 0.8}      # 橙子饱和度变化