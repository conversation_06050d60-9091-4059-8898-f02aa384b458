#!/usr/bin/env python3
"""
YOLOv12s RGB训练脚本
使用转换后的RGB数据集训练YOLOv12s模型
"""

import os
import sys
import torch
from pathlib import Path
import yaml
from datetime import datetime

def check_environment():
    """检查训练环境"""
    print("=== 环境检查 ===")
    print(f"Python版本: {sys.version}")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
    print()

def install_ultralytics():
    """安装或更新ultralytics"""
    print("=== 安装/更新 Ultralytics ===")
    try:
        import ultralytics
        print(f"Ultralytics已安装，版本: {ultralytics.__version__}")
    except ImportError:
        print("正在安装ultralytics...")
        os.system("pip install ultralytics")
        
    # 尝试更新到最新版本以支持YOLOv12
    print("更新ultralytics到最新版本...")
    os.system("pip install --upgrade ultralytics")
    
    try:
        import ultralytics
        print(f"Ultralytics版本: {ultralytics.__version__}")
    except ImportError:
        print("错误: 无法导入ultralytics")
        return False
    
    return True

def check_dataset(dataset_path):
    """检查数据集"""
    print("=== 数据集检查 ===")
    dataset_path = Path(dataset_path)
    
    if not dataset_path.exists():
        print(f"错误: 数据集路径不存在: {dataset_path}")
        return False
        
    # 检查配置文件
    yaml_file = dataset_path / 'dataset.yaml'
    if not yaml_file.exists():
        print(f"错误: 数据集配置文件不存在: {yaml_file}")
        return False
        
    # 读取配置
    with open(yaml_file, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
        
    print(f"数据集路径: {dataset_path}")
    print(f"类别数量: {config.get('nc', 'Unknown')}")
    print(f"类别名称: {config.get('names', 'Unknown')}")
    
    # 检查图像和标签目录
    for split in ['train', 'val', 'test']:
        img_dir = dataset_path / 'images' / split
        label_dir = dataset_path / 'labels' / split
        
        if img_dir.exists() and label_dir.exists():
            img_count = len(list(img_dir.glob('*.jpg')))
            label_count = len(list(label_dir.glob('*.txt')))
            print(f"{split}: {img_count} 图像, {label_count} 标签")
        else:
            print(f"警告: {split} 目录不完整")
            
    print()
    return True

def train_yolo12s(dataset_path, output_dir, **kwargs):
    """训练YOLOv12s模型"""
    print("=== 开始训练 YOLOv12s ===")
    
    try:
        from ultralytics import YOLO
    except ImportError:
        print("错误: 无法导入YOLO，请确保ultralytics已正确安装")
        return False
    
    # 创建输出目录
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 训练参数
    default_params = {
        'epochs': 100,
        'batch': 16,
        'imgsz': 640,
        'device': 0 if torch.cuda.is_available() else 'cpu',
        'patience': 50,
        'save_period': 10,
        'workers': 4,
        'project': str(output_dir),
        'name': f'yolo12s_rgb_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
        'exist_ok': True,
        'pretrained': True,
        'optimizer': 'AdamW',
        'lr0': 0.01,
        'lrf': 0.01,
        'momentum': 0.937,
        'weight_decay': 0.0005,
        'warmup_epochs': 3,
        'warmup_momentum': 0.8,
        'warmup_bias_lr': 0.1,
        'box': 7.5,
        'cls': 0.5,
        'dfl': 1.5,
        'pose': 12.0,
        'kobj': 1.0,
        'label_smoothing': 0.0,
        'nbs': 64,
        'hsv_h': 0.015,
        'hsv_s': 0.7,
        'hsv_v': 0.4,
        'degrees': 0.0,
        'translate': 0.1,
        'scale': 0.5,
        'shear': 0.0,
        'perspective': 0.0,
        'flipud': 0.0,
        'fliplr': 0.5,
        'mosaic': 1.0,
        'mixup': 0.0,
        'copy_paste': 0.0
    }
    
    # 更新参数
    default_params.update(kwargs)
    
    print("训练参数:")
    for key, value in default_params.items():
        print(f"  {key}: {value}")
    print()
    
    try:
        # 尝试加载YOLOv12s模型
        model_names = ['yolo12s.pt', 'yolov12s.pt', 'yolov8s.pt']  # 备选模型名称
        model = None
        
        for model_name in model_names:
            try:
                print(f"尝试加载模型: {model_name}")
                model = YOLO(model_name)
                print(f"成功加载模型: {model_name}")
                break
            except Exception as e:
                print(f"无法加载 {model_name}: {e}")
                continue
                
        if model is None:
            print("错误: 无法加载任何YOLO模型")
            return False
            
        # 开始训练
        print(f"开始训练，数据集: {dataset_path}")
        results = model.train(
            data=str(Path(dataset_path) / 'dataset.yaml'),
            **default_params
        )
        
        print("训练完成！")
        print(f"结果保存在: {output_dir}")
        
        return True
        
    except Exception as e:
        print(f"训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("YOLOv12s RGB训练脚本")
    print("=" * 50)
    
    # 配置路径
    dataset_path = "/home/<USER>/claude/SpatialVLA/3D/yolo12s/rgb_dataset"
    output_dir = "/home/<USER>/claude/SpatialVLA/3D/yolo12s/runs"
    
    # 检查环境
    check_environment()
    
    # 安装依赖
    if not install_ultralytics():
        print("错误: 无法安装ultralytics")
        return
    
    # 检查数据集
    if not check_dataset(dataset_path):
        print("错误: 数据集检查失败")
        return
    
    # 训练参数（可以根据需要调整）
    training_params = {
        'epochs': 100,
        'batch': 16,
        'imgsz': 640,
        'patience': 50,
        'save_period': 10,
        'workers': 4
    }
    
    # 开始训练
    success = train_yolo12s(dataset_path, output_dir, **training_params)
    
    if success:
        print("\n训练成功完成！")
        print(f"模型和结果保存在: {output_dir}")
        print("\n可以使用以下命令进行推理测试:")
        print(f"python -c \"from ultralytics import YOLO; model = YOLO('{output_dir}/最新训练结果/weights/best.pt'); results = model('测试图像路径')\"")
    else:
        print("\n训练失败，请检查错误信息")

if __name__ == "__main__":
    main()
