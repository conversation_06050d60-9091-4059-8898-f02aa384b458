#!/usr/bin/env python3
"""
YOLOv12s RGB训练完整流程脚本
1. 转换COCO格式数据集到YOLO格式
2. 训练YOLOv12s模型
"""

import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

def main():
    """主函数 - 执行完整的训练流程"""
    print("YOLOv12s RGB训练完整流程")
    print("=" * 60)
    
    # 步骤1: 数据集转换
    print("\n步骤 1/2: 转换数据集格式 (COCO -> YOLO)")
    print("-" * 40)
    
    try:
        from coco_to_yolo_converter import main as convert_main
        convert_main()
        print("✓ 数据集转换完成")
    except Exception as e:
        print(f"✗ 数据集转换失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 步骤2: 模型训练
    print("\n步骤 2/2: 开始训练YOLOv12s模型")
    print("-" * 40)
    
    try:
        from train_yolo12s import main as train_main
        train_main()
        print("✓ 模型训练完成")
    except Exception as e:
        print(f"✗ 模型训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n" + "=" * 60)
    print("🎉 YOLOv12s RGB训练流程全部完成！")
    print("\n训练结果位置:")
    print("  - 转换后的数据集: /home/<USER>/claude/SpatialVLA/3D/yolo12s/rgb_dataset")
    print("  - 训练结果: /home/<USER>/claude/SpatialVLA/3D/yolo12s/runs")
    print("\n下一步建议:")
    print("  1. 检查训练日志和指标")
    print("  2. 使用最佳模型进行推理测试")
    print("  3. 根据结果调整超参数进行进一步优化")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ 训练流程未能完成，请检查错误信息")
        sys.exit(1)
    else:
        print("\n✅ 训练流程成功完成！")
