#!/usr/bin/env python3
"""
从训练集中分割出验证集
将部分训练数据移动到验证集，确保训练和验证都有标注
"""

import json
import shutil
import random
from pathlib import Path
from collections import defaultdict

def load_coco_annotations(annotation_file):
    """加载COCO标注文件"""
    with open(annotation_file, 'r', encoding='utf-8') as f:
        return json.load(f)

def split_by_images(coco_data, val_ratio=0.15):
    """按图像分割数据集"""
    images = coco_data['images']
    random.shuffle(images)
    
    split_idx = int(len(images) * (1 - val_ratio))
    train_images = images[:split_idx]
    val_images = images[split_idx:]
    
    # 创建图像ID集合
    train_image_ids = {img['id'] for img in train_images}
    val_image_ids = {img['id'] for img in val_images}
    
    # 分割标注
    train_annotations = []
    val_annotations = []
    
    for ann in coco_data['annotations']:
        if ann['image_id'] in train_image_ids:
            train_annotations.append(ann)
        elif ann['image_id'] in val_image_ids:
            val_annotations.append(ann)
    
    # 创建新的数据集
    train_data = {
        'images': train_images,
        'annotations': train_annotations,
        'categories': coco_data['categories']
    }
    
    val_data = {
        'images': val_images,
        'annotations': val_annotations,
        'categories': coco_data['categories']
    }
    
    return train_data, val_data

def move_images_and_labels(image_list, source_img_dir, source_label_dir, 
                          target_img_dir, target_label_dir):
    """移动图像和对应的标注文件"""
    moved_count = 0
    
    for img_info in image_list:
        img_name = img_info['file_name']
        label_name = Path(img_name).stem + '.txt'
        
        # 源文件路径
        src_img = source_img_dir / img_name
        src_label = source_label_dir / label_name
        
        # 目标文件路径
        dst_img = target_img_dir / img_name
        dst_label = target_label_dir / label_name
        
        # 移动图像文件 (软链接)
        if src_img.exists():
            if dst_img.exists():
                dst_img.unlink()  # 删除现有链接
            dst_img.symlink_to(src_img.resolve())
            moved_count += 1
        
        # 移动标注文件
        if src_label.exists():
            shutil.move(str(src_label), str(dst_label))
    
    return moved_count

def main():
    """主函数"""
    print("开始分割训练集和验证集...")
    
    # 路径配置
    data_dir = Path("/home/<USER>/claude/SpatialVLA/3D/yolo12s/data")
    original_annotation = Path("/home/<USER>/claude/SpatialVLA/3D/merged_rgbd_dataset/labels/train/annotations.json")
    
    # 加载原始标注
    print("加载原始标注文件...")
    coco_data = load_coco_annotations(original_annotation)
    print(f"总图像数: {len(coco_data['images'])}")
    print(f"总标注数: {len(coco_data['annotations'])}")
    
    # 分割数据集
    print("分割数据集...")
    train_data, val_data = split_by_images(coco_data, val_ratio=0.15)
    
    print(f"训练集: {len(train_data['images'])} 图像, {len(train_data['annotations'])} 标注")
    print(f"验证集: {len(val_data['images'])} 图像, {len(val_data['annotations'])} 标注")
    
    # 创建新的标注文件
    train_ann_file = data_dir / "train_annotations.json"
    val_ann_file = data_dir / "val_annotations.json"
    
    with open(train_ann_file, 'w', encoding='utf-8') as f:
        json.dump(train_data, f, ensure_ascii=False, indent=2)
    
    with open(val_ann_file, 'w', encoding='utf-8') as f:
        json.dump(val_data, f, ensure_ascii=False, indent=2)
    
    print(f"保存新的标注文件:")
    print(f"  训练集: {train_ann_file}")
    print(f"  验证集: {val_ann_file}")
    
    # 重新组织图像和标注文件
    print("重新组织文件...")
    
    # 目录路径
    train_img_dir = data_dir / "images" / "train"
    val_img_dir = data_dir / "images" / "val"
    train_label_dir = data_dir / "labels" / "train"
    val_label_dir = data_dir / "labels" / "val"
    
    # 清空验证集目录
    if val_img_dir.exists():
        shutil.rmtree(val_img_dir)
    if val_label_dir.exists():
        shutil.rmtree(val_label_dir)
    
    val_img_dir.mkdir(parents=True, exist_ok=True)
    val_label_dir.mkdir(parents=True, exist_ok=True)
    
    # 移动验证集文件
    print("移动验证集文件...")
    val_moved = move_images_and_labels(
        val_data['images'], 
        train_img_dir, train_label_dir,
        val_img_dir, val_label_dir
    )
    
    print(f"移动到验证集: {val_moved} 个文件")
    
    # 验证结果
    print("\n验证分割结果:")
    train_img_count = len(list(train_img_dir.glob('*.jpg')))
    val_img_count = len(list(val_img_dir.glob('*.jpg')))
    train_label_count = len(list(train_label_dir.glob('*.txt')))
    val_label_count = len(list(val_label_dir.glob('*.txt')))
    
    print(f"训练集: {train_img_count} 图像, {train_label_count} 标注")
    print(f"验证集: {val_img_count} 图像, {val_label_count} 标注")
    
    print("\n分割完成!")

if __name__ == "__main__":
    # 设置随机种子以确保可重现性
    random.seed(42)
    main()