#!/usr/bin/env python3
"""
YOLOv12s 模型评估脚本
用于评估训练好的模型在验证集和测试集上的性能
"""

import os
import sys
import argparse
import json
import yaml
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

try:
    from ultralytics import YOLO
    print("✓ ultralytics 库已导入")
except ImportError:
    print("❌ 未找到 ultralytics 库，请安装:")
    print("pip install ultralytics")
    sys.exit(1)

def load_model(model_path):
    """加载训练好的模型"""
    if not Path(model_path).exists():
        print(f"❌ 模型文件不存在: {model_path}")
        return None
    
    try:
        model = YOLO(model_path)
        print(f"✓ 模型加载成功: {model_path}")
        return model
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return None

def evaluate_model(model, data_config, split='val', save_results=True):
    """评估模型性能"""
    print(f"开始评估模型在 {split} 集上的性能...")
    
    try:
        # 运行验证
        results = model.val(
            data=data_config,
            split=split,
            save_json=save_results,
            save_hybrid=save_results,
            plots=save_results,
            verbose=True
        )
        
        print("✓ 评估完成!")
        return results
        
    except Exception as e:
        print(f"❌ 评估失败: {e}")
        return None

def extract_metrics(results):
    """提取关键评估指标"""
    if results is None:
        return None
    
    metrics = {}
    
    # 提取mAP指标
    if hasattr(results, 'box'):
        box_metrics = results.box
        metrics.update({
            'mAP50': float(box_metrics.map50) if box_metrics.map50 is not None else 0.0,
            'mAP50-95': float(box_metrics.map) if box_metrics.map is not None else 0.0,
            'mAP75': float(box_metrics.map75) if box_metrics.map75 is not None else 0.0,
        })
        
        # 每个类别的AP
        if box_metrics.ap_class_index is not None and box_metrics.ap is not None:
            class_ap = {}
            for i, class_idx in enumerate(box_metrics.ap_class_index):
                if i < len(box_metrics.ap):
                    class_ap[int(class_idx)] = float(box_metrics.ap[i])
            metrics['class_ap'] = class_ap
        
        # 精确率和召回率
        if hasattr(box_metrics, 'mp') and box_metrics.mp is not None:
            metrics['precision'] = float(box_metrics.mp)
        if hasattr(box_metrics, 'mr') and box_metrics.mr is not None:
            metrics['recall'] = float(box_metrics.mr)
    
    # 添加其他可用指标
    for attr in ['fitness', 'ap50', 'ap']:
        if hasattr(results, attr):
            value = getattr(results, attr)
            if value is not None:
                metrics[attr] = float(value) if not isinstance(value, (list, tuple)) else value
    
    return metrics

def generate_evaluation_report(model_path, metrics, split, output_dir):
    """生成评估报告"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = output_dir / f"evaluation_report_{split}_{timestamp}.json"
    
    # 类别名称映射
    class_names = {
        0: 'CA001_衣架',
        1: 'CA002_牙刷', 
        2: 'CB001_果冻',
        3: 'CB002_长方形状饼干',
        4: 'CC001_罐装饮料',
        5: 'CC002_瓶装饮料',
        6: 'CD001_香蕉',
        7: 'CD002_橙子',
        8: 'Wxxx_未知物品'
    }
    
    report = {
        'evaluation_info': {
            'model_path': str(model_path),
            'split': split,
            'timestamp': timestamp,
            'evaluation_date': datetime.now().isoformat()
        },
        'overall_metrics': {
            'mAP50': metrics.get('mAP50', 0.0),
            'mAP50-95': metrics.get('mAP50-95', 0.0),
            'mAP75': metrics.get('mAP75', 0.0),
            'precision': metrics.get('precision', 0.0),
            'recall': metrics.get('recall', 0.0)
        },
        'class_metrics': {},
        'raw_metrics': metrics
    }
    
    # 添加每个类别的详细指标
    if 'class_ap' in metrics:
        for class_id, ap_value in metrics['class_ap'].items():
            class_name = class_names.get(class_id, f"Class_{class_id}")
            report['class_metrics'][class_name] = {
                'class_id': class_id,
                'ap50': ap_value,
                'class_name': class_name
            }
    
    # 保存报告
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"✓ 评估报告已保存: {report_file}")
    return report

def print_evaluation_summary(metrics, split):
    """打印评估结果摘要"""
    print(f"\n{'='*60}")
    print(f"模型在 {split.upper()} 集上的评估结果")
    print(f"{'='*60}")
    
    # 总体指标
    print("总体性能指标:")
    print(f"  mAP@0.5      : {metrics.get('mAP50', 0.0):.4f}")
    print(f"  mAP@0.5:0.95 : {metrics.get('mAP50-95', 0.0):.4f}")
    print(f"  mAP@0.75     : {metrics.get('mAP75', 0.0):.4f}")
    print(f"  Precision    : {metrics.get('precision', 0.0):.4f}")
    print(f"  Recall       : {metrics.get('recall', 0.0):.4f}")
    
    # 每个类别的AP
    if 'class_ap' in metrics:
        print("\n各类别AP@0.5:")
        class_names = {
            0: 'CA001_衣架', 1: 'CA002_牙刷', 2: 'CB001_果冻',
            3: 'CB002_长方形状饼干', 4: 'CC001_罐装饮料', 5: 'CC002_瓶装饮料',
            6: 'CD001_香蕉', 7: 'CD002_橙子', 8: 'Wxxx_未知物品'
        }
        
        for class_id, ap_value in metrics['class_ap'].items():
            class_name = class_names.get(class_id, f"Class_{class_id}")
            print(f"  {class_name:<20}: {ap_value:.4f}")

def compare_with_baseline(metrics, baseline_file=None):
    """与基线模型比较"""
    if baseline_file is None:
        baseline_file = project_root / "models" / "baseline_metrics.json"
    
    if not baseline_file.exists():
        print("\n未找到基线模型指标，跳过比较")
        return
    
    try:
        with open(baseline_file, 'r', encoding='utf-8') as f:
            baseline = json.load(f)
        
        print(f"\n{'='*60}")
        print("与基线模型比较")
        print(f"{'='*60}")
        
        current_map50 = metrics.get('mAP50', 0.0)
        baseline_map50 = baseline.get('overall_metrics', {}).get('mAP50', 0.0)
        
        improvement = current_map50 - baseline_map50
        print(f"mAP@0.5 改进: {improvement:+.4f} ({current_map50:.4f} vs {baseline_map50:.4f})")
        
        if improvement > 0:
            print("✓ 模型性能有所提升!")
        elif improvement < -0.01:
            print("⚠ 模型性能有所下降")
        else:
            print("→ 模型性能基本持平")
            
    except Exception as e:
        print(f"比较基线模型时出错: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='YOLOv12s 模型评估脚本')
    parser.add_argument('--model', type=str, required=True, help='模型文件路径')
    parser.add_argument('--data', type=str, help='数据集配置文件路径')
    parser.add_argument('--split', type=str, default='val', choices=['val', 'test'], help='评估数据集分割')
    parser.add_argument('--save', action='store_true', help='保存评估结果')
    parser.add_argument('--compare', action='store_true', help='与基线模型比较')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("YOLOv12s 模型评估脚本")
    print("3D识别比赛 - RGB图像目标检测")
    print("=" * 60)
    
    # 加载模型
    model = load_model(args.model)
    if model is None:
        sys.exit(1)
    
    # 确定数据配置文件
    data_config = args.data
    if data_config is None:
        data_config = str(project_root / "data" / "dataset.yaml")
    
    if not Path(data_config).exists():
        print(f"❌ 数据配置文件不存在: {data_config}")
        sys.exit(1)
    
    print(f"✓ 数据配置: {data_config}")
    
    # 创建输出目录
    output_dir = project_root / "runs" / "evaluation"
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 评估模型
    results = evaluate_model(model, data_config, args.split, args.save)
    
    if results is None:
        print("❌ 评估失败")
        sys.exit(1)
    
    # 提取指标
    metrics = extract_metrics(results)
    
    if metrics:
        # 打印结果摘要
        print_evaluation_summary(metrics, args.split)
        
        # 生成详细报告
        if args.save:
            report = generate_evaluation_report(
                args.model, metrics, args.split, output_dir
            )
        
        # 与基线比较
        if args.compare:
            compare_with_baseline(metrics)
    
    print(f"\n{'='*60}")
    print("评估完成!")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()