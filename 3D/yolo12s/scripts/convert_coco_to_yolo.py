#!/usr/bin/env python3
"""
COCO格式转YOLO格式转换脚本
将merged_rgbd_dataset中的COCO标注转换为YOLO格式，并创建软链接指向RGB图像
"""

import json
import os
import shutil
from pathlib import Path
from tqdm import tqdm

def convert_bbox_coco_to_yolo(bbox, img_width, img_height):
    """
    将COCO格式的边界框转换为YOLO格式
    COCO格式: [x_min, y_min, width, height] (绝对坐标)
    YOLO格式: [center_x, center_y, width, height] (相对坐标，归一化到0-1)
    """
    x_min, y_min, width, height = bbox
    
    # 计算中心点
    center_x = x_min + width / 2
    center_y = y_min + height / 2
    
    # 归一化
    center_x /= img_width
    center_y /= img_height
    width /= img_width
    height /= img_height
    
    return [center_x, center_y, width, height]

def create_image_links(source_dir, target_dir, split):
    """创建图像软链接"""
    source_path = Path(source_dir) / 'images' / split
    target_path = Path(target_dir) / 'images' / split
    
    if not source_path.exists():
        print(f"警告: 源目录不存在 {source_path}")
        return 0
    
    # 清空目标目录
    if target_path.exists():
        shutil.rmtree(target_path)
    target_path.mkdir(parents=True, exist_ok=True)
    
    # 创建软链接
    count = 0
    for img_file in source_path.glob('*.jpg'):
        target_file = target_path / img_file.name
        try:
            os.symlink(img_file.absolute(), target_file)
            count += 1
        except Exception as e:
            print(f"创建软链接失败 {img_file} -> {target_file}: {e}")
    
    print(f"创建 {split} 图像软链接: {count} 个")
    return count

def convert_annotations(source_dir, target_dir, split):
    """转换标注文件"""
    annotation_file = Path(source_dir) / 'labels' / split / 'annotations.json'
    target_labels_dir = Path(target_dir) / 'labels' / split
    
    if not annotation_file.exists():
        print(f"警告: 标注文件不存在 {annotation_file}")
        return 0
    
    # 清空目标目录
    if target_labels_dir.exists():
        shutil.rmtree(target_labels_dir)
    target_labels_dir.mkdir(parents=True, exist_ok=True)
    
    # 加载COCO标注
    with open(annotation_file, 'r', encoding='utf-8') as f:
        coco_data = json.load(f)
    
    # 创建图像ID到文件名的映射
    image_info = {}
    for img in coco_data['images']:
        image_info[img['id']] = {
            'file_name': img['file_name'],
            'width': img['width'],
            'height': img['height']
        }
    
    # 按图像分组标注
    annotations_by_image = {}
    for ann in coco_data['annotations']:
        image_id = ann['image_id']
        if image_id not in annotations_by_image:
            annotations_by_image[image_id] = []
        annotations_by_image[image_id].append(ann)
    
    # 转换每个图像的标注
    converted_count = 0
    for image_id, annotations in tqdm(annotations_by_image.items(), desc=f"转换{split}标注"):
        if image_id not in image_info:
            continue
            
        img_info = image_info[image_id]
        img_width = img_info['width']
        img_height = img_info['height']
        
        # 创建YOLO标注文件
        txt_filename = Path(img_info['file_name']).stem + '.txt'
        txt_path = target_labels_dir / txt_filename
        
        with open(txt_path, 'w') as f:
            for ann in annotations:
                category_id = ann['category_id']
                bbox = ann['bbox']
                
                # 转换边界框格式
                yolo_bbox = convert_bbox_coco_to_yolo(bbox, img_width, img_height)
                
                # 写入YOLO格式: class_id center_x center_y width height
                f.write(f"{category_id} {' '.join(map(str, yolo_bbox))}\n")
        
        converted_count += 1
    
    print(f"转换 {split} 标注文件: {converted_count} 个")
    return converted_count

def main():
    """主函数"""
    # 路径配置
    source_dir = "/home/<USER>/claude/SpatialVLA/3D/merged_rgbd_dataset"
    target_dir = "/home/<USER>/claude/SpatialVLA/3D/yolo12s/data"
    
    print("开始转换COCO格式到YOLO格式...")
    print(f"源目录: {source_dir}")
    print(f"目标目录: {target_dir}")
    
    # 处理各个数据分割
    splits = ['train', 'val', 'test']
    total_images = 0
    total_labels = 0
    
    for split in splits:
        print(f"\n处理 {split} 数据集...")
        
        # 创建图像软链接
        img_count = create_image_links(source_dir, target_dir, split)
        total_images += img_count
        
        # 转换标注
        label_count = convert_annotations(source_dir, target_dir, split)
        total_labels += label_count
    
    print(f"\n转换完成!")
    print(f"总图像数: {total_images}")
    print(f"总标注文件数: {total_labels}")
    
    # 验证转换结果
    print("\n验证转换结果...")
    for split in splits:
        img_dir = Path(target_dir) / 'images' / split
        label_dir = Path(target_dir) / 'labels' / split
        
        img_count = len(list(img_dir.glob('*.jpg'))) if img_dir.exists() else 0
        label_count = len(list(label_dir.glob('*.txt'))) if label_dir.exists() else 0
        
        print(f"{split}: 图像={img_count}, 标注={label_count}")

if __name__ == "__main__":
    main()