#!/usr/bin/env python3
"""
YOLOv12s 推理脚本
用于对单张图像或图像目录进行目标检测推理
"""

import os
import sys
import argparse
import cv2
import numpy as np
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

try:
    from ultralytics import YOLO
    print("✓ ultralytics 库已导入")
except ImportError:
    print("❌ 未找到 ultralytics 库，请安装:")
    print("pip install ultralytics")
    sys.exit(1)

# 类别名称映射
CLASS_NAMES = {
    0: 'CA001_衣架',
    1: 'CA002_牙刷', 
    2: 'CB001_果冻',
    3: 'CB002_长方形状饼干',
    4: 'CC001_罐装饮料',
    5: 'CC002_瓶装饮料',
    6: 'CD001_香蕉',
    7: 'CD002_橙子',
    8: 'Wxxx_未知物品'
}

def load_model(model_path):
    """加载训练好的模型"""
    if not Path(model_path).exists():
        print(f"❌ 模型文件不存在: {model_path}")
        return None
    
    try:
        model = YOLO(model_path)
        print(f"✓ 模型加载成功: {model_path}")
        return model
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return None

def inference_single_image(model, image_path, conf_threshold=0.25, save_result=True):
    """对单张图像进行推理"""
    if not Path(image_path).exists():
        print(f"❌ 图像文件不存在: {image_path}")
        return None
    
    print(f"推理图像: {image_path}")
    
    # 进行推理
    results = model(image_path, conf=conf_threshold, verbose=False)
    
    # 解析结果
    detections = []
    if results and len(results) > 0:
        result = results[0]
        
        if result.boxes is not None:
            boxes = result.boxes.xyxy.cpu().numpy()  # 边界框坐标
            confidences = result.boxes.conf.cpu().numpy()  # 置信度
            class_ids = result.boxes.cls.cpu().numpy().astype(int)  # 类别ID
            
            for i, (box, conf, cls_id) in enumerate(zip(boxes, confidences, class_ids)):
                x1, y1, x2, y2 = box
                class_name = CLASS_NAMES.get(cls_id, f"Unknown_{cls_id}")
                
                detection = {
                    'class_id': cls_id,
                    'class_name': class_name,
                    'confidence': float(conf),
                    'bbox': [float(x1), float(y1), float(x2), float(y2)],
                    'center': [float((x1 + x2) / 2), float((y1 + y2) / 2)],
                    'size': [float(x2 - x1), float(y2 - y1)]
                }
                detections.append(detection)
    
    print(f"检测到 {len(detections)} 个目标")
    
    # 保存结果图像
    if save_result and results:
        output_dir = project_root / "runs" / "inference"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成输出文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_name = f"{Path(image_path).stem}_{timestamp}.jpg"
        output_path = output_dir / output_name
        
        # 保存带标注的图像
        annotated_img = results[0].plot()
        cv2.imwrite(str(output_path), annotated_img)
        print(f"✓ 结果图像已保存: {output_path}")
    
    return detections

def inference_directory(model, input_dir, conf_threshold=0.25, save_results=True):
    """对目录中的所有图像进行推理"""
    input_path = Path(input_dir)
    if not input_path.exists():
        print(f"❌ 输入目录不存在: {input_dir}")
        return []
    
    # 支持的图像格式
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    image_files = []
    
    for ext in image_extensions:
        image_files.extend(input_path.glob(f"*{ext}"))
        image_files.extend(input_path.glob(f"*{ext.upper()}"))
    
    if not image_files:
        print(f"❌ 在目录中未找到图像文件: {input_dir}")
        return []
    
    print(f"找到 {len(image_files)} 张图像")
    
    all_results = []
    for i, image_file in enumerate(image_files, 1):
        print(f"\n处理 {i}/{len(image_files)}: {image_file.name}")
        
        detections = inference_single_image(
            model, str(image_file), conf_threshold, save_results
        )
        
        if detections:
            result = {
                'image_path': str(image_file),
                'image_name': image_file.name,
                'detections': detections,
                'detection_count': len(detections)
            }
            all_results.append(result)
    
    return all_results

def format_competition_output(detections, image_name):
    """格式化为比赛要求的输出格式"""
    # 比赛要求格式: ID;Num
    # 统计每个类别的数量
    class_counts = {}
    for det in detections:
        class_name = det['class_name']
        if class_name in class_counts:
            class_counts[class_name] += 1
        else:
            class_counts[class_name] = 1
    
    # 生成输出
    output_lines = ["START"]
    for class_name, count in class_counts.items():
        output_lines.append(f"{class_name};{count}")
    output_lines.append("END")
    
    return output_lines

def save_competition_format(all_results, output_file):
    """保存为比赛格式的文本文件"""
    output_path = Path(output_file)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        for result in all_results:
            f.write(f"# 图像: {result['image_name']}\n")
            competition_output = format_competition_output(
                result['detections'], result['image_name']
            )
            for line in competition_output:
                f.write(line + '\n')
            f.write('\n')
    
    print(f"✓ 比赛格式结果已保存: {output_path}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='YOLOv12s 推理脚本')
    parser.add_argument('--model', type=str, required=True, help='模型文件路径')
    parser.add_argument('--source', type=str, required=True, help='输入图像或目录路径')
    parser.add_argument('--conf', type=float, default=0.25, help='置信度阈值')
    parser.add_argument('--save', action='store_true', help='保存结果图像')
    parser.add_argument('--competition', action='store_true', help='输出比赛格式结果')
    parser.add_argument('--output', type=str, help='输出文件路径')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("YOLOv12s 推理脚本")
    print("3D识别比赛 - RGB图像目标检测")
    print("=" * 60)
    
    # 加载模型
    model = load_model(args.model)
    if model is None:
        sys.exit(1)
    
    # 检查输入源
    source_path = Path(args.source)
    if not source_path.exists():
        print(f"❌ 输入路径不存在: {args.source}")
        sys.exit(1)
    
    # 进行推理
    if source_path.is_file():
        # 单张图像推理
        detections = inference_single_image(
            model, str(source_path), args.conf, args.save
        )
        
        if detections:
            print("\n检测结果:")
            for i, det in enumerate(detections, 1):
                print(f"{i}. {det['class_name']} (置信度: {det['confidence']:.3f})")
            
            if args.competition:
                competition_output = format_competition_output(detections, source_path.name)
                print("\n比赛格式输出:")
                for line in competition_output:
                    print(line)
        
    elif source_path.is_dir():
        # 目录推理
        all_results = inference_directory(
            model, str(source_path), args.conf, args.save
        )
        
        if all_results:
            print(f"\n推理完成! 处理了 {len(all_results)} 张图像")
            
            # 统计总体结果
            total_detections = sum(r['detection_count'] for r in all_results)
            print(f"总检测数量: {total_detections}")
            
            if args.competition:
                output_file = args.output or "competition_results.txt"
                save_competition_format(all_results, output_file)
    
    else:
        print(f"❌ 不支持的输入类型: {args.source}")
        sys.exit(1)
    
    print("\n推理完成!")

if __name__ == "__main__":
    main()