#!/usr/bin/env python3
"""
YOLOv12s 训练脚本
用于训练3D识别比赛的目标检测模型
"""

import os
import sys
import argparse
import yaml
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

try:
    from ultralytics import YOL<PERSON>
    print("✓ ultralytics 库已导入")
except ImportError:
    print("❌ 未找到 ultralytics 库，请安装:")
    print("pip install ultralytics")
    sys.exit(1)

def setup_training_environment():
    """设置训练环境"""
    # 创建必要的目录
    runs_dir = project_root / "runs"
    models_dir = project_root / "models"
    
    runs_dir.mkdir(exist_ok=True)
    models_dir.mkdir(exist_ok=True)
    
    return runs_dir, models_dir

def load_config(config_path=None):
    """加载训练配置"""
    if config_path is None:
        config_path = project_root / "configs" / "yolo12s_config.yaml"
    
    if not config_path.exists():
        # 使用默认配置
        return {
            'model': 'yolo11s.pt',  # 使用YOLOv11s作为基础模型
            'data': str(project_root / "data" / "dataset.yaml"),
            'epochs': 100,
            'imgsz': 640,
            'batch': 16,
            'lr0': 0.01,
            'device': 'auto',
            'workers': 8,
            'patience': 50,
            'save_period': 10,
            'val': True,
            'plots': True,
            'verbose': True
        }
    
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def train_model(config, experiment_name=None):
    """训练模型"""
    if experiment_name is None:
        experiment_name = f"yolo12s_train_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    print(f"开始训练实验: {experiment_name}")
    print(f"配置: {config}")
    
    # 初始化模型
    model_name = config.get('model', 'yolo11s.pt')
    print(f"加载模型: {model_name}")
    
    try:
        model = YOLO(model_name)
        print(f"✓ 模型加载成功: {model_name}")
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        print("尝试使用在线下载...")
        model = YOLO('yolo11s.pt')  # 这会自动下载
    
    # 设置训练参数
    train_args = {
        'data': config['data'],
        'epochs': config.get('epochs', 100),
        'imgsz': config.get('imgsz', 640),
        'batch': config.get('batch', 16),
        'lr0': config.get('lr0', 0.01),
        'device': config.get('device', 'auto'),
        'workers': config.get('workers', 8),
        'patience': config.get('patience', 50),
        'save_period': config.get('save_period', 10),
        'val': config.get('val', True),
        'plots': config.get('plots', True),
        'verbose': config.get('verbose', True),
        'name': experiment_name,
        'project': str(project_root / "runs"),
    }
    
    print("训练参数:")
    for key, value in train_args.items():
        print(f"  {key}: {value}")
    
    # 开始训练
    try:
        results = model.train(**train_args)
        print("✓ 训练完成!")
        
        # 保存最佳模型到models目录
        best_model_path = project_root / "runs" / experiment_name / "weights" / "best.pt"
        if best_model_path.exists():
            target_path = project_root / "models" / f"{experiment_name}_best.pt"
            import shutil
            shutil.copy2(best_model_path, target_path)
            print(f"✓ 最佳模型已保存到: {target_path}")
        
        return results
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        return None

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='YOLOv12s 训练脚本')
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--name', type=str, help='实验名称')
    parser.add_argument('--epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--batch', type=int, default=16, help='批次大小')
    parser.add_argument('--imgsz', type=int, default=640, help='图像大小')
    parser.add_argument('--device', type=str, default='auto', help='设备 (cpu, 0, 1, auto)')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("YOLOv12s 训练脚本")
    print("3D识别比赛 - RGB图像目标检测")
    print("=" * 60)
    
    # 设置环境
    runs_dir, models_dir = setup_training_environment()
    print(f"训练结果目录: {runs_dir}")
    print(f"模型保存目录: {models_dir}")
    
    # 加载配置
    config = load_config(args.config)
    
    # 命令行参数覆盖配置文件
    if args.epochs:
        config['epochs'] = args.epochs
    if args.batch:
        config['batch'] = args.batch
    if args.imgsz:
        config['imgsz'] = args.imgsz
    if args.device:
        config['device'] = args.device
    
    # 检查数据集文件
    dataset_path = Path(config['data'])
    if not dataset_path.exists():
        print(f"❌ 数据集配置文件不存在: {dataset_path}")
        print("请先运行数据转换脚本: python scripts/convert_coco_to_yolo.py")
        sys.exit(1)
    
    print(f"✓ 数据集配置: {dataset_path}")
    
    # 开始训练
    results = train_model(config, args.name)
    
    if results:
        print("\n" + "=" * 60)
        print("训练完成!")
        print("=" * 60)
        print("下一步:")
        print("1. 查看训练结果: tensorboard --logdir runs/")
        print("2. 运行推理测试: python scripts/inference.py")
        print("3. 评估模型性能: python scripts/evaluate.py")
    else:
        print("\n❌ 训练失败，请检查错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()