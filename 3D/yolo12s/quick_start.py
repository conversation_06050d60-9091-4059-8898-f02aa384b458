#!/usr/bin/env python3
"""
YOLOv12s 快速启动脚本
用于快速测试训练环境和开始训练
"""

import os
import sys
from pathlib import Path

def check_environment():
    """检查环境依赖"""
    print("检查环境依赖...")
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查必要的库
    required_packages = ['torch', 'torchvision', 'ultralytics', 'opencv-python', 'yaml']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'opencv-python':
                import cv2
                print(f"✓ OpenCV: {cv2.__version__}")
            elif package == 'yaml':
                import yaml
                print(f"✓ PyYAML: {yaml.__version__}")
            elif package == 'torch':
                import torch
                print(f"✓ PyTorch: {torch.__version__}")
                print(f"  CUDA可用: {torch.cuda.is_available()}")
                if torch.cuda.is_available():
                    print(f"  GPU数量: {torch.cuda.device_count()}")
                    print(f"  当前GPU: {torch.cuda.get_device_name()}")
            elif package == 'torchvision':
                import torchvision
                print(f"✓ TorchVision: {torchvision.__version__}")
            elif package == 'ultralytics':
                from ultralytics import YOLO
                print(f"✓ Ultralytics: 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}: 未安装")
    
    if missing_packages:
        print(f"\n需要安装以下包:")
        for pkg in missing_packages:
            print(f"  pip install {pkg}")
        return False
    
    return True

def check_data():
    """检查数据集"""
    print("\n检查数据集...")
    
    data_dir = Path("data")
    dataset_yaml = data_dir / "dataset.yaml"
    
    if not dataset_yaml.exists():
        print(f"❌ 数据集配置文件不存在: {dataset_yaml}")
        return False
    
    print(f"✓ 数据集配置: {dataset_yaml}")
    
    # 检查各个分割的数据
    for split in ['train', 'val', 'test']:
        img_dir = data_dir / "images" / split
        label_dir = data_dir / "labels" / split
        
        img_count = len(list(img_dir.glob('*.jpg'))) if img_dir.exists() else 0
        label_count = len(list(label_dir.glob('*.txt'))) if label_dir.exists() else 0
        
        print(f"  {split}: {img_count} 图像, {label_count} 标注")
        
        if split in ['train', 'val'] and (img_count == 0 or label_count == 0):
            print(f"    ⚠ {split} 集数据不完整")
    
    return True

def quick_train():
    """快速训练测试"""
    print("\n开始快速训练测试 (5个epoch)...")
    
    try:
        from ultralytics import YOLO
        
        # 加载模型
        model = YOLO('yolo11n.pt')  # 使用更小的模型进行快速测试
        print("✓ 模型加载成功")
        
        # 训练参数
        train_args = {
            'data': 'data/dataset.yaml',
            'epochs': 5,
            'imgsz': 640,
            'batch': 8,  # 较小的批次大小
            'device': 'auto',
            'workers': 4,
            'patience': 10,
            'val': True,
            'plots': True,
            'verbose': True,
            'name': 'quick_test',
            'project': 'runs',
        }
        
        print("训练参数:")
        for key, value in train_args.items():
            print(f"  {key}: {value}")
        
        # 开始训练
        results = model.train(**train_args)
        print("✓ 快速训练测试完成!")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("YOLOv12s 快速启动脚本")
    print("3D识别比赛 - RGB图像目标检测")
    print("=" * 60)
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请安装缺失的依赖包")
        return
    
    # 检查数据
    if not check_data():
        print("\n❌ 数据检查失败，请先运行数据转换脚本")
        print("python scripts/convert_coco_to_yolo.py")
        print("python scripts/split_train_val.py")
        return
    
    print("\n✓ 环境和数据检查通过!")
    
    # 询问是否进行快速训练测试
    response = input("\n是否进行快速训练测试 (5个epoch)? (y/n): ")
    if response.lower() == 'y':
        success = quick_train()
        
        if success:
            print("\n" + "=" * 60)
            print("快速测试完成!")
            print("=" * 60)
            print("下一步:")
            print("1. 查看训练结果: ls runs/quick_test/")
            print("2. 开始完整训练: python scripts/train.py")
            print("3. 进行推理测试: python scripts/inference.py --model runs/quick_test/weights/best.pt --source data/images/val/")
        else:
            print("\n❌ 快速测试失败，请检查错误信息")
    else:
        print("\n准备就绪! 可以开始训练:")
        print("python scripts/train.py")

if __name__ == "__main__":
    main()