#!/usr/bin/env python3
"""
COCO格式转YOLO格式转换器
将merged_rgbd_dataset的COCO标注转换为YOLO格式，用于YOLOv12s训练
"""

import json
import os
import shutil
from pathlib import Path
from typing import Dict, List, Tuple

class COCOToYOLOConverter:
    def __init__(self, source_dataset_path: str, target_dataset_path: str):
        self.source_path = Path(source_dataset_path)
        self.target_path = Path(target_dataset_path)
        
        # 类别映射 - 从COCO格式的category_id到YOLO格式的class_id
        self.category_mapping = {
            0: 0,  # CA001_衣架
            1: 1,  # CA002_牙刷
            2: 2,  # CB001_果冻
            3: 3,  # CB002_长方形状饼干
            4: 4,  # CC001_罐装饮料
            5: 5,  # CC002_瓶装饮料
            6: 6,  # CD001_香蕉
            7: 7,  # CD002_橙子
            8: 8   # Wxxx_未知物品
        }
        
        # 类别名称
        self.class_names = [
            'CA001_衣架', 'CA002_牙刷', 'CB001_果冻', 'CB002_长方形状饼干',
            'CC001_罐装饮料', 'CC002_瓶装饮料', 'CD001_香蕉', 'CD002_橙子', 'Wxxx_未知物品'
        ]
        
    def setup_target_structure(self):
        """创建YOLO格式的目录结构"""
        print("创建YOLO数据集目录结构...")
        
        # 创建主要目录
        for split in ['train', 'val', 'test']:
            (self.target_path / 'images' / split).mkdir(parents=True, exist_ok=True)
            (self.target_path / 'labels' / split).mkdir(parents=True, exist_ok=True)
            
        print(f"目录结构已创建: {self.target_path}")
        
    def coco_bbox_to_yolo(self, bbox: List[float], img_width: int, img_height: int) -> Tuple[float, float, float, float]:
        """
        将COCO格式的bbox转换为YOLO格式
        COCO: [x_min, y_min, width, height] (绝对坐标)
        YOLO: [x_center, y_center, width, height] (相对坐标，0-1)
        """
        x_min, y_min, width, height = bbox
        
        # 转换为中心点坐标
        x_center = x_min + width / 2
        y_center = y_min + height / 2
        
        # 归一化到0-1
        x_center_norm = x_center / img_width
        y_center_norm = y_center / img_height
        width_norm = width / img_width
        height_norm = height / img_height
        
        return x_center_norm, y_center_norm, width_norm, height_norm
        
    def convert_split(self, split: str):
        """转换指定的数据集分割"""
        print(f"转换 {split} 数据集...")
        
        # 读取COCO标注文件
        annotation_file = self.source_path / 'labels' / split / 'annotations.json'
        if not annotation_file.exists():
            print(f"警告: 标注文件不存在: {annotation_file}")
            return
            
        with open(annotation_file, 'r', encoding='utf-8') as f:
            coco_data = json.load(f)
            
        # 创建图像ID到文件名的映射
        image_id_to_info = {img['id']: img for img in coco_data['images']}
        
        # 按图像分组标注
        image_annotations = {}
        for ann in coco_data['annotations']:
            image_id = ann['image_id']
            if image_id not in image_annotations:
                image_annotations[image_id] = []
            image_annotations[image_id].append(ann)
            
        # 复制图像并创建YOLO标注
        source_img_dir = self.source_path / 'images' / split
        target_img_dir = self.target_path / 'images' / split
        target_label_dir = self.target_path / 'labels' / split
        
        converted_count = 0
        for image_id, image_info in image_id_to_info.items():
            file_name = image_info['file_name']
            img_width = image_info['width']
            img_height = image_info['height']
            
            # 复制图像文件
            source_img_path = source_img_dir / file_name
            target_img_path = target_img_dir / file_name
            
            if source_img_path.exists():
                shutil.copy2(source_img_path, target_img_path)
                
                # 创建YOLO标注文件
                label_file_name = file_name.replace('.jpg', '.txt')
                label_file_path = target_label_dir / label_file_name
                
                yolo_annotations = []
                if image_id in image_annotations:
                    for ann in image_annotations[image_id]:
                        category_id = ann['category_id']
                        bbox = ann['bbox']
                        
                        # 转换bbox格式
                        x_center, y_center, width, height = self.coco_bbox_to_yolo(
                            bbox, img_width, img_height
                        )
                        
                        # YOLO格式: class_id x_center y_center width height
                        yolo_line = f"{category_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}"
                        yolo_annotations.append(yolo_line)
                
                # 写入标注文件
                with open(label_file_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(yolo_annotations))
                    
                converted_count += 1
                
                if converted_count % 100 == 0:
                    print(f"已转换 {converted_count} 个文件...")
                    
        print(f"{split} 数据集转换完成，共转换 {converted_count} 个文件")
        
    def create_dataset_yaml(self):
        """创建YOLO数据集配置文件"""
        yaml_content = f"""# YOLOv12s RGB训练数据集配置
# 从merged_rgbd_dataset转换而来，仅使用RGB图像

# 数据集路径
path: {self.target_path.absolute()}
train: images/train
val: images/val
test: images/test

# 类别数量和名称
nc: {len(self.class_names)}
names: {self.class_names}

# 数据集信息
dataset_info:
  source: "merged_rgbd_dataset"
  format: "YOLO"
  image_size: [500, 500]
  total_classes: {len(self.class_names)}
  
# 训练配置建议
training_config:
  epochs: 100
  batch_size: 16
  imgsz: 640
  device: 0
  patience: 50
  
# 数据增强配置
augmentation:
  hsv_h: 0.015
  hsv_s: 0.7
  hsv_v: 0.4
  degrees: 0.0
  translate: 0.1
  scale: 0.5
  shear: 0.0
  perspective: 0.0
  flipud: 0.0
  fliplr: 0.5
  mosaic: 1.0
  mixup: 0.0
"""
        
        yaml_file = self.target_path / 'dataset.yaml'
        with open(yaml_file, 'w', encoding='utf-8') as f:
            f.write(yaml_content)
            
        print(f"数据集配置文件已创建: {yaml_file}")
        
    def convert_dataset(self):
        """转换整个数据集"""
        print("开始转换COCO格式数据集到YOLO格式...")
        print(f"源数据集: {self.source_path}")
        print(f"目标数据集: {self.target_path}")
        
        # 创建目录结构
        self.setup_target_structure()
        
        # 转换各个分割
        for split in ['train', 'val', 'test']:
            self.convert_split(split)
            
        # 创建配置文件
        self.create_dataset_yaml()
        
        print("数据集转换完成！")
        print(f"YOLO格式数据集保存在: {self.target_path}")

def main():
    # 配置路径
    source_dataset = "/home/<USER>/claude/SpatialVLA/3D/merged_rgbd_dataset"
    target_dataset = "/home/<USER>/claude/SpatialVLA/3D/yolo12s/rgb_dataset"
    
    # 创建转换器并执行转换
    converter = COCOToYOLOConverter(source_dataset, target_dataset)
    converter.convert_dataset()
    
    print("\n转换完成！现在可以使用YOLOv12s进行训练了。")

if __name__ == "__main__":
    main()
