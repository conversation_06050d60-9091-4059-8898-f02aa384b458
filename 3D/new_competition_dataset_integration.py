#!/usr/bin/env python3
"""
2025年新比赛数据集整合脚本
根据新版赛事说明，整合符合要求的8个类别数据集
"""

import os
import json
import shutil
from pathlib import Path
import cv2
import numpy as np
from typing import Dict, List, Tuple
import random

class NewCompetitionDatasetIntegrator:
    def __init__(self, output_dir: str = "competition_2025_dataset"):
        self.output_dir = Path(output_dir)
        self.setup_output_structure()
        
        # 2025年新比赛的8个类别映射
        self.category_mapping = {
            'CA001': 0,  # 衣架
            'CA002': 1,  # 牙刷
            'CB001': 2,  # 果冻
            'CB002': 3,  # 长方形状饼干
            'CC001': 4,  # 罐装饮料
            'CC002': 5,  # 瓶装饮料
            'CD001': 6,  # 香蕉
            'CD002': 7,  # 橙子
        }
        
        # 类别名称映射
        self.class_names = [
            'CA001_衣架', 'CA002_牙刷', 'CB001_果冻', 'CB002_长方形状饼干',
            'CC001_罐装饮料', 'CC002_瓶装饮料', 'CD001_香蕉', 'CD002_橙子'
        ]
        
    def setup_output_structure(self):
        """创建输出目录结构"""
        dirs = [
            self.output_dir / "images" / "train",
            self.output_dir / "images" / "val", 
            self.output_dir / "images" / "test",
            self.output_dir / "labels" / "train",
            self.output_dir / "labels" / "val",
            self.output_dir / "labels" / "test"
        ]
        
        for dir_path in dirs:
            dir_path.mkdir(parents=True, exist_ok=True)
            
    def load_coco_annotations(self, coco_file: str) -> Dict:
        """加载COCO格式标注文件"""
        with open(coco_file, 'r', encoding='utf-8') as f:
            return json.load(f)
            
    def coco_to_yolo_bbox(self, bbox: List[float], img_width: int, img_height: int) -> List[float]:
        """将COCO格式的bbox转换为YOLO格式"""
        x, y, w, h = bbox
        
        # COCO: [x_min, y_min, width, height]
        # YOLO: [x_center, y_center, width, height] (normalized)
        x_center = (x + w / 2) / img_width
        y_center = (y + h / 2) / img_height
        width = w / img_width
        height = h / img_height
        
        return [x_center, y_center, width, height]
        
    def process_new_coco_dataset(self, dataset_path: str, split_ratio: Tuple[float, float, float] = (0.7, 0.2, 0.1)):
        """处理新的COCO数据集"""
        dataset_path = Path(dataset_path)
        
        # 查找COCO标注文件
        coco_file = dataset_path / "instances_train.json"
        if not coco_file.exists():
            print(f"错误: 在 {dataset_path} 中未找到COCO标注文件")
            return
            
        print(f"处理新比赛数据集: {dataset_path}")
        coco_data = self.load_coco_annotations(coco_file)
        
        # 验证类别
        categories = {cat['name']: cat['id'] for cat in coco_data['categories']}
        print(f"数据集包含的类别: {list(categories.keys())}")
        
        # 检查是否包含所有需要的类别
        required_categories = set(self.category_mapping.keys())
        available_categories = set(categories.keys())
        
        if not required_categories.issubset(available_categories):
            missing = required_categories - available_categories
            print(f"警告: 缺少类别: {missing}")
            
        # 创建图像ID到文件名的映射
        image_id_to_filename = {img['id']: img['file_name'] for img in coco_data['images']}
        image_id_to_size = {img['id']: (img['width'], img['height']) for img in coco_data['images']}
        
        # 按图像组织标注
        annotations_by_image = {}
        for ann in coco_data['annotations']:
            image_id = ann['image_id']
            if image_id not in annotations_by_image:
                annotations_by_image[image_id] = []
            annotations_by_image[image_id].append(ann)
            
        # 获取所有图像文件
        image_files = []
        train_dir = dataset_path / "train"
        
        for img_info in coco_data['images']:
            filename = img_info['file_name']
            img_path = train_dir / filename
            if img_path.exists():
                image_files.append((img_info['id'], img_path))
            else:
                print(f"警告: 图像文件不存在: {img_path}")
                
        print(f"找到 {len(image_files)} 张有效图像")
        
        # 随机打乱数据
        random.shuffle(image_files)
        
        # 数据集分割
        total_images = len(image_files)
        train_count = int(total_images * split_ratio[0])
        val_count = int(total_images * split_ratio[1])
        
        splits = {
            'train': image_files[:train_count],
            'val': image_files[train_count:train_count + val_count],
            'test': image_files[train_count + val_count:]
        }
        
        # 统计信息
        category_stats = {split: {name: 0 for name in self.class_names} for split in splits.keys()}
        
        for split_name, split_files in splits.items():
            print(f"  处理 {split_name} 集: {len(split_files)} 张图像")
            
            for image_id, img_path in split_files:
                filename = image_id_to_filename[image_id]
                base_name = Path(filename).stem
                
                # 复制图像
                new_img_name = f"{base_name}.jpg"
                img_dst = self.output_dir / "images" / split_name / new_img_name
                shutil.copy2(img_path, img_dst)
                
                # 生成YOLO格式标注
                if image_id in annotations_by_image:
                    img_width, img_height = image_id_to_size[image_id]
                    yolo_annotations = []
                    
                    for ann in annotations_by_image[image_id]:
                        # 根据COCO的category_id找到类别名称
                        coco_category_id = ann['category_id']
                        category_name = None
                        for cat in coco_data['categories']:
                            if cat['id'] == coco_category_id:
                                category_name = cat['name']
                                break
                                
                        if category_name and category_name in self.category_mapping:
                            yolo_class_id = self.category_mapping[category_name]
                            bbox = self.coco_to_yolo_bbox(ann['bbox'], img_width, img_height)
                            yolo_annotations.append(f"{yolo_class_id} {' '.join(map(str, bbox))}")
                            
                            # 统计类别数量
                            class_name = self.class_names[yolo_class_id]
                            category_stats[split_name][class_name] += 1
                            
                    # 保存YOLO标注文件
                    if yolo_annotations:
                        label_file = self.output_dir / "labels" / split_name / f"{base_name}.txt"
                        with open(label_file, 'w') as f:
                            f.write('\n'.join(yolo_annotations))
                            
        return category_stats
        
    def create_dataset_yaml(self):
        """创建YOLO数据集配置文件"""
        yaml_content = f"""# 2025年新比赛数据集配置
path: {self.output_dir.absolute()}
train: images/train
val: images/val
test: images/test

# Classes (2025年新比赛8个类别)
nc: 8
names: {self.class_names}

# 比赛信息
competition: "2025中国机器人大赛3D识别"
platform: "香橙派AI pro 8T算力16G内存"
camera: "奥比中光Astra Pro Plus"

# 训练参数建议
epochs: 150
batch_size: 16
imgsz: 640
device: 0

# 数据增强参数 (针对比赛环境优化)
hsv_h: 0.015
hsv_s: 0.7
hsv_v: 0.4
degrees: 10.0
translate: 0.1
scale: 0.5
shear: 2.0
perspective: 0.0
flipud: 0.0
fliplr: 0.5
mosaic: 1.0
mixup: 0.0
"""
        
        yaml_file = self.output_dir / "dataset.yaml"
        with open(yaml_file, 'w', encoding='utf-8') as f:
            f.write(yaml_content)
            
        print(f"数据集配置文件已保存: {yaml_file}")
        
    def generate_statistics(self, category_stats: Dict):
        """生成数据集统计信息"""
        stats = {}
        
        for split in ['train', 'val', 'test']:
            img_dir = self.output_dir / "images" / split
            label_dir = self.output_dir / "labels" / split
            
            img_count = len(list(img_dir.glob("*.jpg")))
            label_count = len(list(label_dir.glob("*.txt")))
            
            stats[split] = {
                'images': img_count,
                'labels': label_count,
                'categories': category_stats[split] if split in category_stats else {}
            }
            
        # 保存统计信息
        stats_file = self.output_dir / "dataset_stats.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)
            
        print("\n=== 2025年新比赛数据集统计 ===")
        for split, counts in stats.items():
            print(f"{split}: 图像={counts['images']}, 标注={counts['labels']}")
            if 'categories' in counts:
                print(f"  类别分布:")
                for class_name, count in counts['categories'].items():
                    print(f"    {class_name}: {count}")
                    
        return stats
        
    def create_competition_requirements_doc(self):
        """创建比赛要求文档"""
        doc_content = f"""# 2025年中国机器人大赛3D识别项目要求

## 比赛变化要点

### 1. 硬件平台
- **计算平台**: 香橙派AI pro 8T算力16G内存
- **相机**: 奥比中光Astra Pro Plus (0.6-8m)

### 2. 评分变化
- **模型加载时间**: 计入总识别分
- **桌台编号**: 国赛将计入分数
- **未知物品**: 需要文本识别功能

### 3. 目标类别 (8个)
{chr(10).join([f"- {name}" for name in self.class_names])}

### 4. 比赛环境
- **第一轮**: 方形目标台，6-12个物品，无特定光源
- **第二轮**: 圆形转动目标台，6-12个物品，有特定光源
- **干扰**: 图片干扰 + 实物干扰
- **复杂情况**: 遮挡、叠放、旋转

### 5. 技术要求
- **实时性**: 模型加载和推理时间都计分
- **轻量化**: 适配香橙派AI pro性能
- **鲁棒性**: 处理复杂光照和运动场景
- **文本识别**: 未知物品的OCR功能

### 6. 软件要求
- 一键启动脚本: "识别.sh"
- 网络通讯: 与裁判盒数据交互
- 结果输出: 指定格式的文本文件
- 界面要求: 提供强制关闭按钮

## 模型优化建议

### 1. 模型选择
- 优先考虑YOLOv11n (最新且轻量)
- 备选YOLOv8n/s (成熟稳定)
- 避免过大模型影响加载时间

### 2. 训练策略
- 增加训练轮数到150-200轮
- 针对转动台场景的数据增强
- 光照变化的鲁棒性训练

### 3. 部署优化
- 模型量化减少加载时间
- 预热推理减少首次延迟
- 内存优化适配16G限制

### 4. 功能扩展
- 集成OCR模块处理未知物品
- 实现桌台编号识别
- 网络通讯协议对接
"""
        
        doc_file = self.output_dir / "competition_requirements_2025.md"
        with open(doc_file, 'w', encoding='utf-8') as f:
            f.write(doc_content)
            
        print(f"比赛要求文档已保存: {doc_file}")

def main():
    """主函数"""
    integrator = NewCompetitionDatasetIntegrator("competition_2025_dataset")
    
    # 处理新的COCO数据集
    new_dataset_path = "data_new/coco-data"
    
    if not Path(new_dataset_path).exists():
        print(f"错误: 新数据集路径不存在: {new_dataset_path}")
        return
        
    print("开始整合2025年新比赛数据集...")
    
    # 处理数据集
    category_stats = integrator.process_new_coco_dataset(new_dataset_path)
    
    # 创建配置文件
    integrator.create_dataset_yaml()
    
    # 生成统计信息
    integrator.generate_statistics(category_stats)
    
    # 创建比赛要求文档
    integrator.create_competition_requirements_doc()
    
    print(f"\n✅ 2025年新比赛数据集整合完成!")
    print(f"📁 输出目录: {integrator.output_dir}")
    print(f"📊 数据集包含8个类别，符合新比赛要求")
    print(f"📋 下一步: 使用新数据集训练轻量化模型")

if __name__ == "__main__":
    main()
