#!/usr/bin/env python3
"""
扩展OCR分类系统测试器
验证集成到competition_2025_inference_system.py中的增强OCR功能
"""

import numpy as np
import cv2
import time
from pathlib import Path
import json

# 导入扩展的推理系统
try:
    from competition_2025_inference_system import Competition2025InferenceSystem
    INFERENCE_SYSTEM_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  推理系统导入失败: {e}")
    INFERENCE_SYSTEM_AVAILABLE = False

# 导入增强OCR分类器
try:
    from enhanced_ocr_classifier import EnhancedOCRClassifier
    ENHANCED_OCR_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  增强OCR分类器导入失败: {e}")
    ENHANCED_OCR_AVAILABLE = False

class ExtendedOCRSystemTester:
    """扩展OCR分类系统测试器"""
    
    def __init__(self):
        self.system = None
        self.test_results = {
            'system_loading': False,
            'enhanced_ocr_loading': False,
            'classification_tests': [],
            'performance_tests': {},
            'overall_success': False
        }
    
    def test_system_loading(self):
        """测试系统加载"""
        print("🔄 测试系统加载...")
        
        if not INFERENCE_SYSTEM_AVAILABLE:
            print("❌ 推理系统不可用")
            return False
        
        try:
            # 创建推理系统实例（不加载YOLO模型，只测试OCR）
            self.system = Competition2025InferenceSystem()
            
            # 尝试加载增强OCR分类器
            self.system.load_enhanced_ocr()
            
            if self.system.enhanced_ocr_classifier is not None:
                print("✅ 增强OCR分类器加载成功")
                self.test_results['enhanced_ocr_loading'] = True
            else:
                print("⚠️  增强OCR分类器未加载，将使用基础功能")
            
            self.test_results['system_loading'] = True
            return True
            
        except Exception as e:
            print(f"❌ 系统加载失败: {e}")
            return False
    
    def test_classification_functionality(self):
        """测试分类功能"""
        print("\n📝 测试分类功能...")
        
        if self.system is None or self.system.enhanced_ocr_classifier is None:
            print("⚠️  系统或增强OCR分类器未加载，跳过分类测试")
            return False
        
        # 模拟未知物品检测结果
        test_detections = [
            {
                'category_id': 8,
                'category_name': 'Wxxx',
                'confidence': 0.9,
                'bbox': [100, 100, 200, 150],
                'center': [150, 125]
            },
            {
                'category_id': 8,
                'category_name': 'Wxxx',
                'confidence': 0.85,
                'bbox': [300, 200, 400, 250],
                'center': [350, 225]
            }
        ]
        
        # 创建模拟图像
        test_image = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # 在图像上添加一些文字区域（模拟）
        cv2.putText(test_image, "Mathematics", (110, 130), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(test_image, "Physics", (310, 230), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        try:
            # 测试文字识别和分类
            start_time = time.time()
            processed_detections = self.system.recognize_text_in_unknown_objects(test_image, test_detections)
            processing_time = time.time() - start_time
            
            # 分析结果
            classification_success = 0
            for detection in processed_detections:
                refined_category = detection.get('refined_category', 'Wxxx')
                ocr_text = detection.get('ocr_text', '')
                confidence = detection.get('classification_confidence', 0.0)
                
                test_result = {
                    'bbox': detection['bbox'],
                    'ocr_text': ocr_text,
                    'refined_category': refined_category,
                    'confidence': confidence,
                    'success': refined_category != 'Wxxx'
                }
                
                self.test_results['classification_tests'].append(test_result)
                
                if refined_category != 'Wxxx':
                    classification_success += 1
                    print(f"  ✅ 检测框 {detection['bbox']}: '{ocr_text}' -> {refined_category} (置信度: {confidence:.3f})")
                else:
                    print(f"  ⚠️  检测框 {detection['bbox']}: '{ocr_text}' -> 未分类")
            
            # 记录性能数据
            self.test_results['performance_tests'] = {
                'processing_time': processing_time,
                'detections_processed': len(test_detections),
                'successful_classifications': classification_success,
                'success_rate': classification_success / len(test_detections) if test_detections else 0
            }
            
            print(f"\n📊 分类测试结果:")
            print(f"  处理时间: {processing_time:.3f}s")
            print(f"  成功分类: {classification_success}/{len(test_detections)}")
            print(f"  成功率: {self.test_results['performance_tests']['success_rate']:.1%}")
            
            return classification_success > 0
            
        except Exception as e:
            print(f"❌ 分类功能测试失败: {e}")
            return False
    
    def test_performance_requirements(self):
        """测试性能要求"""
        print("\n⏱️  测试性能要求...")
        
        if not self.test_results['performance_tests']:
            print("⚠️  没有性能数据，跳过性能测试")
            return False
        
        perf = self.test_results['performance_tests']
        
        # 检查处理时间要求（单次OCR处理时间<1秒）
        time_ok = perf['processing_time'] <= 1.0
        
        # 检查分类准确率要求（≥90%）
        accuracy_ok = perf['success_rate'] >= 0.9
        
        print(f"  OCR处理时间 ≤ 1s: {'✅' if time_ok else '❌'} ({perf['processing_time']:.3f}s)")
        print(f"  分类准确率 ≥ 90%: {'✅' if accuracy_ok else '❌'} ({perf['success_rate']:.1%})")
        
        return time_ok and accuracy_ok
    
    def get_system_performance_summary(self):
        """获取系统性能摘要"""
        if self.system and self.system.enhanced_ocr_classifier:
            return self.system.enhanced_ocr_classifier.get_performance_summary()
        return {}
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🚀 开始扩展OCR分类系统综合测试")
        print("="*60)
        
        # 1. 测试系统加载
        loading_ok = self.test_system_loading()
        
        # 2. 测试分类功能
        classification_ok = self.test_classification_functionality()
        
        # 3. 测试性能要求
        performance_ok = self.test_performance_requirements()
        
        # 4. 获取详细性能统计
        if self.system and self.system.enhanced_ocr_classifier:
            print("\n📊 详细性能统计:")
            self.system.enhanced_ocr_classifier.print_performance_summary()
        
        # 综合评估
        overall_success = loading_ok and classification_ok and performance_ok
        self.test_results['overall_success'] = overall_success
        
        print("\n" + "="*60)
        print("📈 综合测试结果")
        print("="*60)
        print(f"系统加载: {'✅' if loading_ok else '❌'}")
        print(f"分类功能: {'✅' if classification_ok else '❌'}")
        print(f"性能要求: {'✅' if performance_ok else '❌'}")
        print(f"综合评估: {'✅ 测试通过' if overall_success else '❌ 测试未通过'}")
        print("="*60)
        
        return overall_success
    
    def save_test_results(self, output_path: str = "extended_ocr_test_results.json"):
        """保存测试结果"""
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        print(f"📄 测试结果已保存到: {output_path}")

def main():
    """主函数"""
    tester = ExtendedOCRSystemTester()
    
    try:
        success = tester.run_comprehensive_test()
        tester.save_test_results()
        return success
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
