#!/usr/bin/env python3
"""
测试时增强(TTA)实现
专门用于提升mAP50-95性能
"""

import torch
import cv2
import numpy as np
from ultralytics import YOLO
import torch.nn.functional as F
from typing import List, Dict, Any, Tuple
import copy

class TestTimeAugmentation:
    """
    测试时增强类
    实现多尺度、翻转、旋转等增强策略
    """
    
    def __init__(self, model_path: str):
        self.model = YOLO(model_path)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # TTA配置
        self.scales = [0.8, 0.9, 1.0, 1.1, 1.2]  # 多尺度
        self.flips = [False, True]  # 水平翻转
        self.conf_threshold = 0.1  # 低置信度阈值，后续融合时过滤
        
    def predict_with_tta(self, image_path: str, 
                        use_multiscale: bool = True,
                        use_flip: bool = True,
                        use_rotation: bool = False,
                        fusion_method: str = "nms") -> Dict[str, Any]:
        """
        使用TTA进行预测
        
        Args:
            image_path: 图像路径
            use_multiscale: 是否使用多尺度
            use_flip: 是否使用翻转
            use_rotation: 是否使用旋转
            fusion_method: 融合方法 ["nms", "wbf", "average"]
            
        Returns:
            融合后的检测结果
        """
        # 读取原始图像
        original_image = cv2.imread(image_path)
        if original_image is None:
            raise ValueError(f"无法读取图像: {image_path}")
            
        original_height, original_width = original_image.shape[:2]
        
        # 收集所有增强预测结果
        all_predictions = []
        
        # 生成所有增强组合
        augmentations = self._generate_augmentations(use_multiscale, use_flip, use_rotation)
        
        print(f"执行TTA，共{len(augmentations)}种增强组合...")
        
        for i, aug_config in enumerate(augmentations):
            # 应用增强
            aug_image = self._apply_augmentation(original_image, aug_config)
            
            # 模型预测
            results = self.model(aug_image, conf=self.conf_threshold, verbose=False)
            
            # 反向变换预测结果到原始图像坐标
            transformed_predictions = self._inverse_transform_predictions(
                results[0], aug_config, original_width, original_height
            )
            
            all_predictions.extend(transformed_predictions)
            
            print(f"增强 {i+1}/{len(augmentations)} 完成，检测到 {len(transformed_predictions)} 个目标")
        
        # 融合所有预测结果
        final_predictions = self._fuse_predictions(all_predictions, fusion_method)
        
        print(f"TTA完成，融合后检测到 {len(final_predictions)} 个目标")
        
        return {
            "predictions": final_predictions,
            "num_augmentations": len(augmentations),
            "fusion_method": fusion_method
        }
    
    def _generate_augmentations(self, use_multiscale: bool, use_flip: bool, use_rotation: bool) -> List[Dict]:
        """生成所有增强配置"""
        augmentations = []
        
        scales = self.scales if use_multiscale else [1.0]
        flips = self.flips if use_flip else [False]
        rotations = [0, 90, 180, 270] if use_rotation else [0]
        
        for scale in scales:
            for flip in flips:
                for rotation in rotations:
                    augmentations.append({
                        "scale": scale,
                        "flip": flip,
                        "rotation": rotation
                    })
        
        return augmentations
    
    def _apply_augmentation(self, image: np.ndarray, aug_config: Dict) -> np.ndarray:
        """应用单个增强配置"""
        aug_image = image.copy()
        
        # 缩放
        if aug_config["scale"] != 1.0:
            h, w = aug_image.shape[:2]
            new_h, new_w = int(h * aug_config["scale"]), int(w * aug_config["scale"])
            aug_image = cv2.resize(aug_image, (new_w, new_h))
        
        # 翻转
        if aug_config["flip"]:
            aug_image = cv2.flip(aug_image, 1)  # 水平翻转
        
        # 旋转
        if aug_config["rotation"] != 0:
            h, w = aug_image.shape[:2]
            center = (w // 2, h // 2)
            rotation_matrix = cv2.getRotationMatrix2D(center, aug_config["rotation"], 1.0)
            aug_image = cv2.warpAffine(aug_image, rotation_matrix, (w, h))
        
        return aug_image
    
    def _inverse_transform_predictions(self, results, aug_config: Dict, 
                                     original_width: int, original_height: int) -> List[Dict]:
        """将预测结果反向变换到原始图像坐标"""
        predictions = []
        
        if results.boxes is None or len(results.boxes) == 0:
            return predictions
        
        boxes = results.boxes.xyxy.cpu().numpy()
        confidences = results.boxes.conf.cpu().numpy()
        class_ids = results.boxes.cls.cpu().numpy()
        
        # 获取当前图像尺寸
        current_height, current_width = results.orig_shape
        
        for i in range(len(boxes)):
            x1, y1, x2, y2 = boxes[i]
            
            # 反向缩放
            if aug_config["scale"] != 1.0:
                scale = aug_config["scale"]
                x1 /= scale
                y1 /= scale
                x2 /= scale
                y2 /= scale
            
            # 反向翻转
            if aug_config["flip"]:
                x1_new = original_width - x2
                x2_new = original_width - x1
                x1, x2 = x1_new, x2_new
            
            # 反向旋转（简化处理，仅支持90度倍数）
            if aug_config["rotation"] != 0:
                x1, y1, x2, y2 = self._inverse_rotate_bbox(
                    x1, y1, x2, y2, aug_config["rotation"], original_width, original_height
                )
            
            # 确保边界框在图像范围内
            x1 = max(0, min(x1, original_width))
            y1 = max(0, min(y1, original_height))
            x2 = max(0, min(x2, original_width))
            y2 = max(0, min(y2, original_height))
            
            # 过滤无效边界框
            if x2 > x1 and y2 > y1:
                predictions.append({
                    "bbox": [x1, y1, x2, y2],
                    "confidence": float(confidences[i]),
                    "class_id": int(class_ids[i]),
                    "augmentation": aug_config
                })
        
        return predictions
    
    def _inverse_rotate_bbox(self, x1: float, y1: float, x2: float, y2: float,
                           rotation: int, width: int, height: int) -> Tuple[float, float, float, float]:
        """反向旋转边界框坐标"""
        # 简化处理：仅支持90度倍数的旋转
        if rotation == 90:
            return height - y2, x1, height - y1, x2
        elif rotation == 180:
            return width - x2, height - y2, width - x1, height - y1
        elif rotation == 270:
            return y1, width - x2, y2, width - x1
        else:
            return x1, y1, x2, y2
    
    def _fuse_predictions(self, predictions: List[Dict], method: str = "nms") -> List[Dict]:
        """融合多个预测结果"""
        if not predictions:
            return []
        
        if method == "nms":
            return self._nms_fusion(predictions)
        elif method == "wbf":
            return self._wbf_fusion(predictions)
        elif method == "average":
            return self._average_fusion(predictions)
        else:
            raise ValueError(f"不支持的融合方法: {method}")
    
    def _nms_fusion(self, predictions: List[Dict], iou_threshold: float = 0.5) -> List[Dict]:
        """使用NMS融合预测结果"""
        if not predictions:
            return []
        
        # 按类别分组
        class_predictions = {}
        for pred in predictions:
            class_id = pred["class_id"]
            if class_id not in class_predictions:
                class_predictions[class_id] = []
            class_predictions[class_id].append(pred)
        
        final_predictions = []
        
        # 对每个类别分别进行NMS
        for class_id, class_preds in class_predictions.items():
            if not class_preds:
                continue
            
            # 转换为tensor格式
            boxes = torch.tensor([pred["bbox"] for pred in class_preds])
            scores = torch.tensor([pred["confidence"] for pred in class_preds])
            
            # 执行NMS
            keep_indices = torch.ops.torchvision.nms(boxes, scores, iou_threshold)
            
            # 保留NMS后的预测
            for idx in keep_indices:
                final_predictions.append(class_preds[idx])
        
        return final_predictions
    
    def _wbf_fusion(self, predictions: List[Dict]) -> List[Dict]:
        """加权边界框融合（简化版本）"""
        # 这里实现简化版的WBF
        # 实际应用中可以使用ensemble-boxes库
        return self._nms_fusion(predictions, iou_threshold=0.6)
    
    def _average_fusion(self, predictions: List[Dict]) -> List[Dict]:
        """平均融合（简化版本）"""
        # 简化实现：直接使用NMS
        return self._nms_fusion(predictions, iou_threshold=0.7)

class TTAEvaluator:
    """TTA评估器"""
    
    def __init__(self, model_path: str):
        self.tta = TestTimeAugmentation(model_path)
        
    def evaluate_tta_impact(self, test_images: List[str], 
                           ground_truth: Dict = None) -> Dict[str, Any]:
        """评估TTA对性能的影响"""
        results = {
            "baseline": [],
            "tta_multiscale": [],
            "tta_flip": [],
            "tta_full": []
        }
        
        for image_path in test_images:
            print(f"评估图像: {image_path}")
            
            # 基线预测（无TTA）
            baseline_result = self.tta.model(image_path, verbose=False)[0]
            results["baseline"].append(self._extract_predictions(baseline_result))
            
            # 多尺度TTA
            multiscale_result = self.tta.predict_with_tta(
                image_path, use_multiscale=True, use_flip=False, use_rotation=False
            )
            results["tta_multiscale"].append(multiscale_result["predictions"])
            
            # 翻转TTA
            flip_result = self.tta.predict_with_tta(
                image_path, use_multiscale=False, use_flip=True, use_rotation=False
            )
            results["tta_flip"].append(flip_result["predictions"])
            
            # 完整TTA
            full_result = self.tta.predict_with_tta(
                image_path, use_multiscale=True, use_flip=True, use_rotation=False
            )
            results["tta_full"].append(full_result["predictions"])
        
        # 计算统计信息
        stats = self._compute_statistics(results)
        
        return {
            "results": results,
            "statistics": stats
        }
    
    def _extract_predictions(self, result) -> List[Dict]:
        """从YOLO结果中提取预测"""
        predictions = []
        
        if result.boxes is None or len(result.boxes) == 0:
            return predictions
        
        boxes = result.boxes.xyxy.cpu().numpy()
        confidences = result.boxes.conf.cpu().numpy()
        class_ids = result.boxes.cls.cpu().numpy()
        
        for i in range(len(boxes)):
            predictions.append({
                "bbox": boxes[i].tolist(),
                "confidence": float(confidences[i]),
                "class_id": int(class_ids[i])
            })
        
        return predictions
    
    def _compute_statistics(self, results: Dict) -> Dict[str, Any]:
        """计算统计信息"""
        stats = {}
        
        for method, predictions_list in results.items():
            total_detections = sum(len(preds) for preds in predictions_list)
            avg_detections = total_detections / len(predictions_list) if predictions_list else 0
            
            # 计算平均置信度
            all_confidences = []
            for preds in predictions_list:
                all_confidences.extend([pred["confidence"] for pred in preds])
            
            avg_confidence = np.mean(all_confidences) if all_confidences else 0
            
            stats[method] = {
                "total_detections": total_detections,
                "avg_detections_per_image": avg_detections,
                "avg_confidence": avg_confidence
            }
        
        return stats

# 使用示例
def main():
    """TTA使用示例"""
    model_path = "refined_models/refined_yolo11n_stage1_balanced/weights/best.pt"
    
    # 创建TTA实例
    tta = TestTimeAugmentation(model_path)
    
    # 测试单张图像
    test_image = "competition_2025_dataset/images/test/test_001.jpg"
    
    if os.path.exists(test_image):
        # 执行TTA预测
        result = tta.predict_with_tta(
            test_image,
            use_multiscale=True,
            use_flip=True,
            use_rotation=False,
            fusion_method="nms"
        )
        
        print(f"TTA结果:")
        print(f"- 增强组合数: {result['num_augmentations']}")
        print(f"- 融合方法: {result['fusion_method']}")
        print(f"- 最终检测数: {len(result['predictions'])}")
        
        # 显示前5个检测结果
        for i, pred in enumerate(result['predictions'][:5]):
            print(f"检测 {i+1}: 类别={pred['class_id']}, 置信度={pred['confidence']:.3f}")

if __name__ == "__main__":
    import os
    main()
