# 2025年比赛RGB-D YOLO检测系统

## 🏆 项目概述

本项目是针对2025年比赛的完整RGB-D目标检测解决方案，实现了从数据准备、模型训练、优化到推理部署的全流程。

### 🎯 核心成果

- **最高精度**: 验证集mAP50达到**95.54%**，测试集mAP50达到**97.17%**
- **困难类别突破**: 牙刷和果冻检测性能提升**33%**
- **多模型策略**: 提供3种不同场景的最优模型选择
- **完整工具链**: 从数据处理到部署的一站式解决方案

## 📁 项目结构

```
3D/
├── README.md                                    # 本文件
├── requirements.txt                             # 依赖包列表
├── setup.py                                    # 安装脚本
├── run_complete_pipeline.py                    # 一键运行脚本
│
├── data_preparation/                            # 数据准备模块
│   ├── competition_2025_dataset_preparation.py # 数据集重新整理
│   └── dataset_statistics.py                   # 数据集统计分析
│
├── training/                                    # 训练模块
│   ├── competition_2025_training.py            # 基础训练脚本
│   ├── optimized_training_for_thin_small_objects.py # 针对困难类别优化
│   ├── refined_training_system.py              # 精细化两阶段训练
│   └── train_yolo11s_optimized.py             # YOLOv11s优化训练
│
├── inference/                                   # 推理模块
│   ├── competition_2025_inference_system.py    # 基础推理系统
│   ├── adaptive_inference_system.py            # 自适应推理系统
│   └── ultimate_competition_system.py          # 终极比赛系统
│
├── evaluation/                                  # 评估模块
│   ├── comprehensive_model_evaluation.py       # 全面模型评估
│   ├── test_optimized_models.py               # 优化模型测试
│   └── optimize_confidence_threshold.py        # 置信度阈值优化
│
├── models/                                      # 模型存储目录
│   ├── competition_2025_models/                # 基线模型
│   ├── optimized_models/                       # 优化模型
│   ├── refined_models/                         # 精细化模型
│   └── optimized_models_s/                    # YOLOv11s模型
│
├── datasets/                                    # 数据集目录
│   ├── competition_2025_dataset/               # 重新整理的比赛数据集
│   ├── dataset/                                # 原始数据集1
│   └── dataset-20250723-coco/                 # 原始数据集2
│
├── results/                                     # 结果输出目录
│   ├── evaluation_reports/                     # 评估报告
│   ├── training_logs/                          # 训练日志
│   └── inference_results/                      # 推理结果
│
└── utils/                                       # 工具模块
    ├── config.py                               # 配置管理
    ├── visualization.py                        # 可视化工具
    └── metrics.py                             # 评估指标
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目（如果从git获取）
git clone <repository_url>
cd 3D

# 创建虚拟环境
conda create -n competition2025 python=3.8
conda activate competition2025

# 安装依赖
pip install -r requirements.txt

# 或使用setup.py安装
python setup.py install
```

### 2. 一键运行完整流程

```bash
# 运行完整的训练和评估流程
python run_complete_pipeline.py --mode full

# 仅运行数据准备
python run_complete_pipeline.py --mode data_prep

# 仅运行训练
python run_complete_pipeline.py --mode training

# 仅运行评估
python run_complete_pipeline.py --mode evaluation
```

## 📊 详细使用流程

### 步骤1: 数据准备

```bash
# 重新整理数据集以符合比赛要求
cd data_preparation
python competition_2025_dataset_preparation.py

# 生成数据集统计报告
python dataset_statistics.py
```

**输出**: 
- `competition_2025_dataset/` - 重新整理的数据集
- `competition_dataset_stats.json` - 数据集统计信息

### 步骤2: 模型训练

#### 2.1 基础训练
```bash
cd training
python competition_2025_training.py
```

#### 2.2 针对困难类别优化训练
```bash
python optimized_training_for_thin_small_objects.py
```

#### 2.3 精细化两阶段训练（推荐）
```bash
python refined_training_system.py
```

#### 2.4 YOLOv11s大模型训练
```bash
python train_yolo11s_optimized.py
```

**输出**:
- `models/` - 训练好的模型文件
- `training_logs/` - 训练日志和指标
- `*.json` - 训练结果报告

### 步骤3: 模型评估

```bash
cd evaluation

# 全面模型评估（推荐）
python comprehensive_model_evaluation.py

# 测试优化效果
python test_optimized_models.py

# 优化置信度阈值
python optimize_confidence_threshold.py
```

**输出**:
- `comprehensive_model_evaluation_report.json` - 详细评估报告
- `model_comparison_table.csv` - 模型对比表格
- `model_performance_comparison.png` - 性能对比图表

### 步骤4: 推理部署

#### 4.1 基础推理
```bash
cd inference
python competition_2025_inference_system.py
```

#### 4.2 自适应推理（推荐）
```bash
python adaptive_inference_system.py
```

#### 4.3 终极比赛系统
```bash
python ultimate_competition_system.py
```

## 🎯 模型选择指南

根据评估结果，我们提供3种最优模型：

### 1. 🏆 最高整体精度模型
- **模型**: `refined_models/refined_yolo11n_stage1_balanced/weights/best.pt`
- **性能**: 验证集mAP50=95.54%, 测试集mAP50=97.17%
- **适用**: 追求最高整体检测精度的场景

### 2. 🎯 困难类别专用模型
- **模型**: `optimized_models_s/class_weighted_yolo11s_thin_small/weights/best.pt`
- **性能**: 困难类别平均mAP=40.56%（牙刷43.47%, 果冻37.64%）
- **适用**: 特别关注牙刷和果冻检测的场景

### 3. ⚖️ 平衡性能模型
- **模型**: `optimized_models/class_weighted_yolo11n_thin_small/weights/best.pt`
- **性能**: 整体mAP50=94.47%, 困难类别平均mAP=37.76%
- **适用**: 需要平衡整体性能和困难类别的场景

## 🔧 配置说明

### 比赛环境配置
```python
# config.py中的关键配置
COMPETITION_CONFIG = {
    "target_platform": "香橙派 AI pro 8T",
    "memory_limit": "16GB",
    "camera": "奥比中光 Astra Pro Plus RGBD",
    "max_loading_time": 30,  # 秒
    "target_inference_time": 5,  # 秒
    "categories": 9  # 8个基本类别 + 未知物品
}
```

### 类别映射
```python
CATEGORY_MAPPING = {
    0: 'CA001_衣架',
    1: 'CA002_牙刷',
    2: 'CB001_果冻', 
    3: 'CB002_长方形状饼干',
    4: 'CC001_罐装饮料',
    5: 'CC002_瓶装饮料',
    6: 'CD001_香蕉',
    7: 'CD002_橙子',
    8: 'Wxxx_未知物品'
}
```

## 📈 性能基准

### 训练性能
- **基线模型**: mAP50=92.42%, 训练时间=0.31小时
- **优化模型**: mAP50=94.47%, 训练时间=0.45小时
- **精细化模型**: mAP50=95.54%, 训练时间=1.2小时

### 推理性能
- **模型加载时间**: 0.037-0.430秒（远低于30秒限制）
- **推理时间**: 0.010-0.033秒（远低于5秒目标）
- **内存使用**: <2GB（满足16GB限制）

### 困难类别改进
- **CA002_牙刷**: 33.08% → 43.47% (+31.4%)
- **CB001_果冻**: 27.93% → 37.64% (+34.8%)

## 🛠️ 故障排除

### 常见问题

1. **CUDA内存不足**
   ```bash
   # 减少批次大小
   python training/refined_training_system.py --batch 8
   ```

2. **数据集路径错误**
   ```bash
   # 检查数据集是否存在
   ls -la competition_2025_dataset/
   ```

3. **模型文件缺失**
   ```bash
   # 重新运行训练
   python training/refined_training_system.py
   ```

### 性能优化建议

1. **GPU加速**: 确保使用CUDA GPU进行训练和推理
2. **内存优化**: 使用混合精度训练减少内存使用
3. **数据加载**: 使用多进程数据加载提升训练速度

## 📞 技术支持

### 联系方式
- 项目维护者: [您的联系方式]
- 技术文档: [文档链接]
- 问题反馈: [Issue链接]

### 更新日志
- **v1.0.0**: 初始版本，包含完整训练和推理流程
- **v1.1.0**: 添加精细化训练和自适应推理
- **v1.2.0**: 优化困难类别检测，添加多模型集成

## 📄 许可证

本项目采用 [MIT License](LICENSE)

## 🙏 致谢

感谢所有为本项目贡献代码和建议的开发者们！

---

**🎉 祝您在2025年比赛中取得优异成绩！**
