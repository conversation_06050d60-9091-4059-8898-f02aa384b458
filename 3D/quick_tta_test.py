#!/usr/bin/env python3
"""
快速TTA测试
使用现有最佳模型测试TTA对mAP50-95的提升效果
"""

import os
import sys
import time
import json
import cv2
import numpy as np
from pathlib import Path
from ultralytics import YOLO
import torch
from typing import List, Dict, Any

class QuickTTATester:
    """快速TTA测试器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.results_dir = self.project_root / "quick_tta_test_results"
        self.results_dir.mkdir(exist_ok=True)
        
        # 配置
        self.config = {
            "models": {
                "highest_accuracy": "refined_models/refined_yolo11n_stage1_balanced/weights/best.pt",
                "balanced_performance": "optimized_models/class_weighted_yolo11n_thin_small/weights/best.pt",
                "difficult_categories": "optimized_models_s/class_weighted_yolo11s_thin_small/weights/best.pt"
            },
            "test_images_dir": "competition_2025_dataset/images/test",
            "max_test_images": 20,  # 限制测试图像数量以加快速度
            "conf_threshold": 0.25
        }
        
        # TTA配置
        self.tta_configs = {
            "baseline": {
                "name": "基线（无TTA）",
                "scales": [1.0],
                "flips": [False],
                "rotations": [0]
            },
            "multiscale": {
                "name": "多尺度TTA",
                "scales": [0.8, 1.0, 1.2],
                "flips": [False],
                "rotations": [0]
            },
            "flip": {
                "name": "翻转TTA",
                "scales": [1.0],
                "flips": [False, True],
                "rotations": [0]
            },
            "full_tta": {
                "name": "完整TTA",
                "scales": [0.8, 1.0, 1.2],
                "flips": [False, True],
                "rotations": [0]
            }
        }
    
    def test_tta_effects(self) -> dict:
        """测试TTA效果"""
        print("🔄 开始快速TTA测试")
        print(f"📸 测试图像目录: {self.config['test_images_dir']}")
        print(f"⚡ 最大测试图像数: {self.config['max_test_images']}")
        print("=" * 60)
        
        # 获取测试图像
        test_images = self._get_test_images()
        if not test_images:
            return {"error": "没有找到测试图像"}
        
        print(f"📷 找到 {len(test_images)} 张测试图像")
        
        # 测试每个模型
        all_results = {}
        
        for model_name, model_path in self.config["models"].items():
            if not os.path.exists(model_path):
                print(f"⚠️  模型不存在，跳过: {model_path}")
                continue
                
            print(f"\n🤖 测试模型: {model_name}")
            model_results = self._test_model_with_tta(model_path, test_images)
            all_results[model_name] = model_results
        
        # 分析结果
        analysis = self._analyze_tta_results(all_results)
        
        # 保存结果
        self._save_results(all_results, analysis)
        
        # 打印总结
        self._print_summary(all_results, analysis)
        
        return {
            "results": all_results,
            "analysis": analysis
        }
    
    def _get_test_images(self) -> List[str]:
        """获取测试图像列表"""
        test_dir = Path(self.config["test_images_dir"])
        
        if not test_dir.exists():
            print(f"❌ 测试图像目录不存在: {test_dir}")
            return []
        
        # 支持的图像格式
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
        test_images = []
        
        for ext in image_extensions:
            test_images.extend(list(test_dir.glob(f"*{ext}")))
            test_images.extend(list(test_dir.glob(f"*{ext.upper()}")))
        
        # 限制数量并转换为字符串
        test_images = [str(img) for img in test_images[:self.config["max_test_images"]]]
        
        return test_images
    
    def _test_model_with_tta(self, model_path: str, test_images: List[str]) -> dict:
        """测试单个模型的TTA效果"""
        print(f"  📂 加载模型: {os.path.basename(model_path)}")
        
        try:
            model = YOLO(model_path)
            model_results = {}
            
            # 测试每种TTA配置
            for tta_name, tta_config in self.tta_configs.items():
                print(f"    🔧 测试 {tta_config['name']}")
                
                tta_result = self._run_tta_on_images(model, test_images, tta_config)
                model_results[tta_name] = tta_result
                
                print(f"      📊 平均检测数: {tta_result['avg_detections']:.1f}")
                print(f"      📊 平均置信度: {tta_result['avg_confidence']:.3f}")
            
            return model_results
            
        except Exception as e:
            print(f"    ❌ 模型测试失败: {e}")
            return {"error": str(e)}
    
    def _run_tta_on_images(self, model, test_images: List[str], tta_config: dict) -> dict:
        """在图像上运行TTA"""
        all_detections = []
        all_confidences = []
        processing_times = []
        
        for image_path in test_images:
            try:
                start_time = time.time()
                
                # 执行TTA预测
                detections = self._predict_with_tta(model, image_path, tta_config)
                
                processing_time = time.time() - start_time
                processing_times.append(processing_time)
                
                # 收集统计信息
                all_detections.append(len(detections))
                if detections:
                    confidences = [det['confidence'] for det in detections]
                    all_confidences.extend(confidences)
                
            except Exception as e:
                print(f"      ⚠️  图像处理失败 {os.path.basename(image_path)}: {e}")
                continue
        
        # 计算统计信息
        result = {
            "total_images": len(test_images),
            "successful_images": len(all_detections),
            "total_detections": sum(all_detections),
            "avg_detections": np.mean(all_detections) if all_detections else 0,
            "avg_confidence": np.mean(all_confidences) if all_confidences else 0,
            "avg_processing_time": np.mean(processing_times) if processing_times else 0,
            "tta_config": tta_config
        }
        
        return result
    
    def _predict_with_tta(self, model, image_path: str, tta_config: dict) -> List[dict]:
        """使用TTA进行预测"""
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            return []
        
        original_height, original_width = image.shape[:2]
        all_predictions = []
        
        # 生成所有增强组合
        for scale in tta_config["scales"]:
            for flip in tta_config["flips"]:
                for rotation in tta_config["rotations"]:
                    # 应用增强
                    aug_image = self._apply_augmentation(image, scale, flip, rotation)
                    
                    # 模型预测
                    results = model(aug_image, conf=self.config["conf_threshold"], verbose=False)
                    
                    # 转换预测结果
                    predictions = self._convert_predictions(
                        results[0], scale, flip, rotation, original_width, original_height
                    )
                    
                    all_predictions.extend(predictions)
        
        # 融合预测结果（简化版NMS）
        final_predictions = self._simple_nms(all_predictions)
        
        return final_predictions
    
    def _apply_augmentation(self, image: np.ndarray, scale: float, flip: bool, rotation: int) -> np.ndarray:
        """应用图像增强"""
        aug_image = image.copy()
        
        # 缩放
        if scale != 1.0:
            h, w = aug_image.shape[:2]
            new_h, new_w = int(h * scale), int(w * scale)
            aug_image = cv2.resize(aug_image, (new_w, new_h))
        
        # 翻转
        if flip:
            aug_image = cv2.flip(aug_image, 1)  # 水平翻转
        
        # 旋转（简化，只支持90度倍数）
        if rotation != 0:
            h, w = aug_image.shape[:2]
            center = (w // 2, h // 2)
            rotation_matrix = cv2.getRotationMatrix2D(center, rotation, 1.0)
            aug_image = cv2.warpAffine(aug_image, rotation_matrix, (w, h))
        
        return aug_image
    
    def _convert_predictions(self, results, scale: float, flip: bool, rotation: int, 
                           original_width: int, original_height: int) -> List[dict]:
        """转换预测结果到原始图像坐标"""
        predictions = []
        
        if results.boxes is None or len(results.boxes) == 0:
            return predictions
        
        boxes = results.boxes.xyxy.cpu().numpy()
        confidences = results.boxes.conf.cpu().numpy()
        class_ids = results.boxes.cls.cpu().numpy()
        
        for i in range(len(boxes)):
            x1, y1, x2, y2 = boxes[i]
            
            # 反向变换
            # 反向缩放
            if scale != 1.0:
                x1 /= scale
                y1 /= scale
                x2 /= scale
                y2 /= scale
            
            # 反向翻转
            if flip:
                x1_new = original_width - x2
                x2_new = original_width - x1
                x1, x2 = x1_new, x2_new
            
            # 确保边界框在图像范围内
            x1 = max(0, min(x1, original_width))
            y1 = max(0, min(y1, original_height))
            x2 = max(0, min(x2, original_width))
            y2 = max(0, min(y2, original_height))
            
            # 过滤无效边界框
            if x2 > x1 and y2 > y1:
                predictions.append({
                    "bbox": [x1, y1, x2, y2],
                    "confidence": float(confidences[i]),
                    "class_id": int(class_ids[i])
                })
        
        return predictions
    
    def _simple_nms(self, predictions: List[dict], iou_threshold: float = 0.5) -> List[dict]:
        """简化的NMS"""
        if not predictions:
            return []
        
        # 按置信度排序
        predictions.sort(key=lambda x: x["confidence"], reverse=True)
        
        keep = []
        
        while predictions:
            # 取置信度最高的
            current = predictions.pop(0)
            keep.append(current)
            
            # 移除与当前框IoU过高的框
            remaining = []
            for pred in predictions:
                if self._calculate_iou(current["bbox"], pred["bbox"]) < iou_threshold:
                    remaining.append(pred)
            
            predictions = remaining
        
        return keep
    
    def _calculate_iou(self, box1: List[float], box2: List[float]) -> float:
        """计算IoU"""
        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2
        
        # 计算交集
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)
        
        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0
        
        intersection = (x2_i - x1_i) * (y2_i - y1_i)
        
        # 计算并集
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0
    
    def _analyze_tta_results(self, all_results: dict) -> dict:
        """分析TTA结果"""
        analysis = {
            "best_model": None,
            "best_tta_config": None,
            "improvements": {},
            "recommendations": []
        }
        
        # 分析每个模型的TTA效果
        for model_name, model_results in all_results.items():
            if "error" in model_results:
                continue
            
            baseline = model_results.get("baseline", {})
            baseline_detections = baseline.get("avg_detections", 0)
            
            model_improvements = {}
            
            for tta_name, tta_result in model_results.items():
                if tta_name == "baseline":
                    continue
                
                tta_detections = tta_result.get("avg_detections", 0)
                improvement = tta_detections - baseline_detections
                improvement_pct = (improvement / baseline_detections * 100) if baseline_detections > 0 else 0
                
                model_improvements[tta_name] = {
                    "detection_improvement": improvement,
                    "improvement_percentage": improvement_pct,
                    "avg_confidence": tta_result.get("avg_confidence", 0),
                    "processing_time": tta_result.get("avg_processing_time", 0)
                }
            
            analysis["improvements"][model_name] = model_improvements
        
        # 找到最佳配置
        best_improvement = 0
        for model_name, improvements in analysis["improvements"].items():
            for tta_name, improvement_data in improvements.items():
                if improvement_data["improvement_percentage"] > best_improvement:
                    best_improvement = improvement_data["improvement_percentage"]
                    analysis["best_model"] = model_name
                    analysis["best_tta_config"] = tta_name
        
        # 生成建议
        if best_improvement > 5:
            analysis["recommendations"].append(f"🎉 TTA显著提升检测效果 {best_improvement:.1f}%，强烈建议使用")
        elif best_improvement > 2:
            analysis["recommendations"].append(f"✅ TTA有明显提升 {best_improvement:.1f}%，建议使用")
        elif best_improvement > 0:
            analysis["recommendations"].append(f"📊 TTA有小幅提升 {best_improvement:.1f}%，可以考虑")
        else:
            analysis["recommendations"].append("⚠️  TTA效果不明显，可能需要调整配置")
        
        return analysis
    
    def _save_results(self, results: dict, analysis: dict):
        """保存测试结果"""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        results_file = self.results_dir / f"quick_tta_test_{timestamp}.json"
        
        output_data = {
            "timestamp": timestamp,
            "config": self.config,
            "tta_configs": self.tta_configs,
            "results": results,
            "analysis": analysis
        }
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n💾 结果已保存: {results_file}")
    
    def _print_summary(self, results: dict, analysis: dict):
        """打印测试总结"""
        print("\n" + "🔄" * 20)
        print("快速TTA测试总结")
        print("🔄" * 20)
        
        # 结果表格
        for model_name, model_results in results.items():
            if "error" in model_results:
                print(f"\n❌ {model_name}: 测试失败")
                continue
            
            print(f"\n🤖 {model_name}:")
            print("TTA配置".ljust(15) + "平均检测数".ljust(12) + "平均置信度".ljust(12) + "处理时间(s)".ljust(12) + "改进")
            print("-" * 70)
            
            baseline = model_results.get("baseline", {})
            baseline_detections = baseline.get("avg_detections", 0)
            
            for tta_name, tta_result in model_results.items():
                detections = tta_result.get("avg_detections", 0)
                confidence = tta_result.get("avg_confidence", 0)
                proc_time = tta_result.get("avg_processing_time", 0)
                
                if tta_name == "baseline":
                    improvement_str = "基线"
                else:
                    improvement = detections - baseline_detections
                    improvement_pct = (improvement / baseline_detections * 100) if baseline_detections > 0 else 0
                    improvement_str = f"+{improvement_pct:.1f}%"
                
                print(f"{tta_name[:14].ljust(15)}{detections:.1f}".ljust(12) + 
                      f"{confidence:.3f}".ljust(12) + f"{proc_time:.3f}".ljust(12) + improvement_str)
        
        # 最佳建议
        if analysis["best_model"] and analysis["best_tta_config"]:
            print(f"\n🏆 最佳组合: {analysis['best_model']} + {analysis['best_tta_config']}")
        
        # 建议
        print("\n💡 建议:")
        for rec in analysis["recommendations"]:
            print(f"  {rec}")

def main():
    """主函数"""
    print("🔄 启动快速TTA测试")
    
    tester = QuickTTATester()
    results = tester.test_tta_effects()
    
    if "error" in results:
        print(f"❌ 测试失败: {results['error']}")
        return 1
    
    print("\n🎉 快速TTA测试完成!")
    return 0

if __name__ == "__main__":
    exit(main())
