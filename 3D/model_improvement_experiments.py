#!/usr/bin/env python3
"""
模型改进实验脚本
基于调研结果，实现多种改进方案并进行对比实验
"""

import os
import json
import time
from pathlib import Path
from ultralytics import YOLO
import torch
import yaml

class ModelImprovementExperiments:
    def __init__(self, dataset_path="unified_rgbd_dataset", base_output_dir="improvement_experiments"):
        self.dataset_path = Path(dataset_path)
        self.base_output_dir = Path(base_output_dir)
        self.base_output_dir.mkdir(exist_ok=True)
        
        # 实验配置
        self.experiments = {
            "baseline_yolov8n": {
                "model": "yolov8n.pt",
                "epochs": 100,
                "batch": 16,
                "imgsz": 640,
                "description": "基线YOLOv8n模型"
            },
            "yolov8s_larger": {
                "model": "yolov8s.pt", 
                "epochs": 100,
                "batch": 12,
                "imgsz": 640,
                "description": "更大的YOLOv8s模型"
            },
            "yolov8m_medium": {
                "model": "yolov8m.pt",
                "epochs": 100, 
                "batch": 8,
                "imgsz": 640,
                "description": "中等大小的YOLOv8m模型"
            },
            "yolov11n_latest": {
                "model": "yolo11n.pt",
                "epochs": 100,
                "batch": 16, 
                "imgsz": 640,
                "description": "最新的YOLO11n模型"
            },
            "yolov11s_latest": {
                "model": "yolo11s.pt",
                "epochs": 100,
                "batch": 12,
                "imgsz": 640, 
                "description": "最新的YOLO11s模型"
            },
            "extended_training": {
                "model": "yolov8n.pt",
                "epochs": 200,
                "batch": 16,
                "imgsz": 640,
                "description": "延长训练的YOLOv8n"
            },
            "higher_resolution": {
                "model": "yolov8n.pt", 
                "epochs": 100,
                "batch": 8,
                "imgsz": 800,
                "description": "更高分辨率训练"
            },
            "optimized_augmentation": {
                "model": "yolov8n.pt",
                "epochs": 100,
                "batch": 16,
                "imgsz": 640,
                "description": "优化数据增强的YOLOv8n",
                "augmentation": {
                    "hsv_h": 0.02,
                    "hsv_s": 0.8,
                    "hsv_v": 0.5,
                    "degrees": 10.0,
                    "translate": 0.2,
                    "scale": 0.8,
                    "shear": 5.0,
                    "perspective": 0.001,
                    "flipud": 0.5,
                    "fliplr": 0.5,
                    "mosaic": 1.0,
                    "mixup": 0.1
                }
            }
        }
        
        self.results = {}
        
    def create_experiment_config(self, experiment_name, config):
        """为实验创建配置文件"""
        exp_dir = self.base_output_dir / experiment_name
        exp_dir.mkdir(exist_ok=True)
        
        # 基础配置
        dataset_config = {
            "path": str(self.dataset_path.absolute()),
            "train": "images/train",
            "val": "images/val", 
            "test": "images/test",
            "nc": 17,
            "names": [
                'CA001_勺子', 'CA002_筷子', 'CA003_碗', 'CA004_衣架',
                'CB001_沙琪玛', 'CB002_罐装蜜饯', 'CB003_火腿肠', 'CB004_薯片', 
                'CC001_罐装饮料', 'CC002_瓶装饮料', 'CC003_盒装牛奶', 'CC004_瓶装水',
                'CD001_苹果', 'CD002_橙子', 'CD003_香蕉', 'CD004_芒果', 'Wxxx_未知物品'
            ]
        }
        
        # 添加增强配置
        if "augmentation" in config:
            dataset_config.update(config["augmentation"])
            
        config_file = exp_dir / "dataset_config.yaml"
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(dataset_config, f, default_flow_style=False, allow_unicode=True)
            
        return config_file
        
    def run_experiment(self, experiment_name, config):
        """运行单个实验"""
        print(f"\n{'='*60}")
        print(f"开始实验: {experiment_name}")
        print(f"描述: {config['description']}")
        print(f"{'='*60}")
        
        try:
            # 创建实验目录和配置
            exp_dir = self.base_output_dir / experiment_name
            config_file = self.create_experiment_config(experiment_name, config)
            
            # 加载模型
            model = YOLO(config["model"])
            
            # 记录开始时间
            start_time = time.time()
            
            # 训练参数
            train_args = {
                "data": str(config_file),
                "epochs": config["epochs"],
                "batch": config["batch"],
                "imgsz": config["imgsz"],
                "device": 0 if torch.cuda.is_available() else "cpu",
                "project": str(exp_dir),
                "name": "training",
                "save": True,
                "plots": True,
                "val": True,
                "patience": 50,
                "save_period": 10
            }
            
            # 开始训练
            results = model.train(**train_args)
            
            # 记录结束时间
            end_time = time.time()
            training_time = end_time - start_time
            
            # 在验证集上评估
            val_results = model.val(data=str(config_file), split='val')
            
            # 保存实验结果
            experiment_results = {
                "experiment_name": experiment_name,
                "description": config["description"],
                "model": config["model"],
                "training_time_seconds": training_time,
                "training_time_hours": training_time / 3600,
                "epochs": config["epochs"],
                "batch_size": config["batch"],
                "image_size": config["imgsz"],
                "metrics": {
                    "mAP50": float(val_results.box.map50),
                    "mAP50_95": float(val_results.box.map),
                    "precision": float(val_results.box.mp),
                    "recall": float(val_results.box.mr),
                    "fitness": float(val_results.fitness) if hasattr(val_results, 'fitness') else None
                },
                "model_info": {
                    "parameters": results.model.model[-1].np if hasattr(results.model.model[-1], 'np') else None,
                    "gflops": results.model.model[-1].gflops if hasattr(results.model.model[-1], 'gflops') else None
                }
            }
            
            self.results[experiment_name] = experiment_results
            
            # 保存单个实验结果
            result_file = exp_dir / "experiment_results.json"
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(experiment_results, f, indent=2, ensure_ascii=False)
                
            print(f"实验 {experiment_name} 完成!")
            print(f"mAP50: {experiment_results['metrics']['mAP50']:.4f}")
            print(f"mAP50-95: {experiment_results['metrics']['mAP50_95']:.4f}")
            print(f"训练时间: {training_time/3600:.2f} 小时")
            
            return experiment_results
            
        except Exception as e:
            print(f"实验 {experiment_name} 失败: {e}")
            error_result = {
                "experiment_name": experiment_name,
                "description": config["description"],
                "status": "failed",
                "error": str(e)
            }
            self.results[experiment_name] = error_result
            return error_result
    
    def run_all_experiments(self, selected_experiments=None):
        """运行所有实验"""
        if selected_experiments is None:
            selected_experiments = list(self.experiments.keys())
            
        print(f"将运行 {len(selected_experiments)} 个实验")
        print(f"实验列表: {selected_experiments}")
        
        for exp_name in selected_experiments:
            if exp_name in self.experiments:
                self.run_experiment(exp_name, self.experiments[exp_name])
            else:
                print(f"警告: 未找到实验配置 {exp_name}")
                
        # 生成对比报告
        self.generate_comparison_report()
        
    def generate_comparison_report(self):
        """生成实验对比报告"""
        print(f"\n{'='*80}")
        print("实验对比报告")
        print(f"{'='*80}")
        
        # 按mAP50排序
        successful_results = {k: v for k, v in self.results.items() 
                            if 'metrics' in v and v['metrics']['mAP50'] is not None}
        
        if not successful_results:
            print("没有成功的实验结果")
            return
            
        sorted_results = sorted(successful_results.items(), 
                              key=lambda x: x[1]['metrics']['mAP50'], reverse=True)
        
        # 生成表格
        print(f"{'实验名称':<25} {'mAP50':<8} {'mAP50-95':<10} {'精确率':<8} {'召回率':<8} {'训练时间(h)':<12}")
        print("-" * 80)
        
        for exp_name, result in sorted_results:
            metrics = result['metrics']
            training_time = result.get('training_time_hours', 0)
            
            print(f"{exp_name:<25} {metrics['mAP50']:<8.4f} {metrics['mAP50_95']:<10.4f} "
                  f"{metrics['precision']:<8.4f} {metrics['recall']:<8.4f} {training_time:<12.2f}")
        
        # 保存详细报告
        report = {
            "summary": {
                "total_experiments": len(self.results),
                "successful_experiments": len(successful_results),
                "best_experiment": sorted_results[0][0] if sorted_results else None,
                "best_mAP50": sorted_results[0][1]['metrics']['mAP50'] if sorted_results else None
            },
            "detailed_results": self.results
        }
        
        report_file = self.base_output_dir / "comparison_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
            
        print(f"\n详细报告保存在: {report_file}")
        
        # 生成Markdown报告
        self.generate_markdown_report(sorted_results)
        
    def generate_markdown_report(self, sorted_results):
        """生成Markdown格式的报告"""
        md_content = f"""# RGB-D YOLO模型改进实验报告

## 实验概述

本报告对比了多种RGB-D YOLO模型改进方案的性能表现。

## 实验结果排名

| 排名 | 实验名称 | mAP50 | mAP50-95 | 精确率 | 召回率 | 训练时间(h) | 描述 |
|------|----------|-------|----------|--------|--------|-------------|------|
"""
        
        for i, (exp_name, result) in enumerate(sorted_results, 1):
            metrics = result['metrics']
            training_time = result.get('training_time_hours', 0)
            description = result.get('description', '')
            
            md_content += f"| {i} | {exp_name} | {metrics['mAP50']:.4f} | {metrics['mAP50_95']:.4f} | "
            md_content += f"{metrics['precision']:.4f} | {metrics['recall']:.4f} | {training_time:.2f} | {description} |\n"
        
        md_content += f"""
## 最佳模型分析

最佳模型: **{sorted_results[0][0]}**
- mAP50: {sorted_results[0][1]['metrics']['mAP50']:.4f}
- mAP50-95: {sorted_results[0][1]['metrics']['mAP50_95']:.4f}
- 描述: {sorted_results[0][1]['description']}

## 改进建议

基于实验结果，建议：
1. 使用性能最佳的模型配置
2. 考虑训练时间与性能的平衡
3. 根据具体应用场景选择合适的模型大小

## 实验环境

- 数据集: {self.dataset_path}
- GPU: {'CUDA可用' if torch.cuda.is_available() else 'CPU'}
- 实验时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        md_file = self.base_output_dir / "experiment_report.md"
        with open(md_file, 'w', encoding='utf-8') as f:
            f.write(md_content)
            
        print(f"Markdown报告保存在: {md_file}")

def main():
    # 检查数据集是否存在
    dataset_path = "unified_rgbd_dataset"
    if not Path(dataset_path).exists():
        print(f"错误: 数据集路径不存在: {dataset_path}")
        print("请先运行 dataset_integration.py 创建统一数据集")
        return
    
    # 创建实验管理器
    experiments = ModelImprovementExperiments(dataset_path)
    
    # 选择要运行的实验（可以根据需要调整）
    selected_experiments = [
        "yolov11n_latest",      # 最新YOLO11
        "yolov11s_latest",      # 更大的YOLO11
        "yolov8s_larger",       # 更大的YOLOv8
        "extended_training",    # 延长训练
        "optimized_augmentation" # 优化增强
    ]
    
    print("开始模型改进实验...")
    print(f"将运行以下实验: {selected_experiments}")
    
    # 运行实验
    experiments.run_all_experiments(selected_experiments)
    
    print("\n所有实验完成!")

if __name__ == "__main__":
    main()
